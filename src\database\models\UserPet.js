const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
    const UserPet = sequelize.define('UserPet', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id'
            },
            onDelete: 'CASCADE'
        },
        pet_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'pets',
                key: 'id'
            },
            onDelete: 'CASCADE'
        },
        // Pet status
        level: {
            type: DataTypes.INTEGER,
            defaultValue: 1,
            validate: {
                min: 1,
                max: 100
            }
        },
        experience: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0
            }
        },
        // Pet condition
        happiness: {
            type: DataTypes.INTEGER,
            defaultValue: 100,
            validate: {
                min: 0,
                max: 100
            }
        },
        hunger: {
            type: DataTypes.INTEGER,
            defaultValue: 100,
            validate: {
                min: 0,
                max: 100
            }
        },
        health: {
            type: DataTypes.INTEGER,
            defaultValue: 100,
            validate: {
                min: 0,
                max: 100
            }
        },
        // Activity
        is_active: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        },
        last_fed: {
            type: DataTypes.DATE,
            allowNull: true
        },
        last_played: {
            type: DataTypes.DATE,
            allowNull: true
        },
        last_battle: {
            type: DataTypes.DATE,
            allowNull: true
        },
        // Stats tracking
        battles_won: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0
            }
        },
        battles_lost: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0
            }
        },
        total_damage_dealt: {
            type: DataTypes.BIGINT,
            defaultValue: 0,
            validate: {
                min: 0
            }
        },
        // Custom name
        nickname: {
            type: DataTypes.STRING(30),
            allowNull: true,
            validate: {
                len: [1, 30]
            }
        },
        // Acquisition info
        acquired_at: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW
        },
        acquired_from: {
            type: DataTypes.STRING,
            allowNull: true // 'gacha', 'shop', 'evolution', etc.
        }
    }, {
        tableName: 'user_pets',
        indexes: [
            {
                fields: ['user_id']
            },
            {
                fields: ['pet_id']
            },
            {
                fields: ['user_id', 'is_active']
            },
            {
                fields: ['level']
            }
        ]
    });

    // Instance methods
    UserPet.prototype.getDisplayName = function() {
        if (this.nickname) {
            return this.nickname;
        }
        return this.pet ? this.pet.name : 'Unknown Pet';
    };

    UserPet.prototype.getFormattedName = function() {
        const displayName = this.getDisplayName();
        const emoji = this.pet ? this.pet.emoji : '🐾';
        const level = this.level;
        
        return `${emoji} ${displayName} (Lv.${level})`;
    };

    UserPet.prototype.getCurrentStats = function() {
        if (!this.pet) return { attack: 0, defense: 0, health: 0 };
        
        const baseStats = this.pet.getStatsAtLevel(this.level);
        
        // Apply condition modifiers
        const happinessModifier = this.happiness / 100;
        const healthModifier = this.health / 100;
        
        return {
            attack: Math.floor(baseStats.attack * happinessModifier),
            defense: Math.floor(baseStats.defense * healthModifier),
            health: Math.floor(baseStats.health * healthModifier)
        };
    };

    UserPet.prototype.getRequiredXP = function() {
        const baseXP = 50;
        const multiplier = 1.3;
        return Math.floor(baseXP * Math.pow(multiplier, this.level - 1));
    };

    UserPet.prototype.canLevelUp = function() {
        if (!this.pet) return false;
        if (this.level >= this.pet.max_level) return false;
        return this.experience >= this.getRequiredXP();
    };

    UserPet.prototype.levelUp = function() {
        if (!this.canLevelUp()) return false;
        
        this.level += 1;
        this.experience -= this.getRequiredXP();
        
        return true;
    };

    UserPet.prototype.gainExperience = function(amount) {
        this.experience += amount;
        
        let leveledUp = false;
        while (this.canLevelUp()) {
            this.levelUp();
            leveledUp = true;
        }
        
        return leveledUp;
    };

    UserPet.prototype.feed = function(amount = 20) {
        this.hunger = Math.min(100, this.hunger + amount);
        this.happiness = Math.min(100, this.happiness + Math.floor(amount / 2));
        this.last_fed = new Date();
        
        return this.save();
    };

    UserPet.prototype.play = function(amount = 15) {
        this.happiness = Math.min(100, this.happiness + amount);
        this.hunger = Math.max(0, this.hunger - 5);
        this.last_played = new Date();
        
        return this.save();
    };

    UserPet.prototype.rest = function(amount = 25) {
        this.health = Math.min(100, this.health + amount);
        this.happiness = Math.min(100, this.happiness + 5);
        
        return this.save();
    };

    UserPet.prototype.updateCondition = function() {
        const now = new Date();
        const hoursSinceLastFed = this.last_fed ? 
            (now - new Date(this.last_fed)) / (1000 * 60 * 60) : 24;
        const hoursSinceLastPlayed = this.last_played ? 
            (now - new Date(this.last_played)) / (1000 * 60 * 60) : 24;

        // Decrease hunger over time (1 point per hour)
        this.hunger = Math.max(0, this.hunger - Math.floor(hoursSinceLastFed));
        
        // Decrease happiness over time (0.5 points per hour)
        this.happiness = Math.max(0, this.happiness - Math.floor(hoursSinceLastPlayed * 0.5));
        
        // Health decreases if hunger is low
        if (this.hunger < 20) {
            this.health = Math.max(0, this.health - 1);
        }
        
        // Health decreases if happiness is low
        if (this.happiness < 20) {
            this.health = Math.max(0, this.health - 1);
        }
        
        return this.save();
    };

    UserPet.prototype.canEvolve = function() {
        if (!this.pet || !this.pet.canEvolve()) return false;
        
        const requirements = this.pet.getEvolutionRequirements();
        if (!requirements) return false;
        
        return this.level >= requirements.level;
    };

    UserPet.prototype.getConditionStatus = function() {
        const getStatus = (value) => {
            if (value >= 80) return { status: 'excellent', emoji: '😊', color: '#00FF00' };
            if (value >= 60) return { status: 'good', emoji: '🙂', color: '#90EE90' };
            if (value >= 40) return { status: 'okay', emoji: '😐', color: '#FFFF00' };
            if (value >= 20) return { status: 'poor', emoji: '😟', color: '#FFA500' };
            return { status: 'critical', emoji: '😢', color: '#FF0000' };
        };

        return {
            happiness: getStatus(this.happiness),
            hunger: getStatus(this.hunger),
            health: getStatus(this.health)
        };
    };

    UserPet.prototype.getBattleEffectiveness = function() {
        const avgCondition = (this.happiness + this.hunger + this.health) / 3;
        
        if (avgCondition >= 80) return { effectiveness: 1.0, description: 'Peak Performance' };
        if (avgCondition >= 60) return { effectiveness: 0.85, description: 'Good Condition' };
        if (avgCondition >= 40) return { effectiveness: 0.7, description: 'Average Condition' };
        if (avgCondition >= 20) return { effectiveness: 0.5, description: 'Poor Condition' };
        return { effectiveness: 0.25, description: 'Critical Condition' };
    };

    // Static methods
    UserPet.findActiveByUser = function(userId) {
        return this.findOne({
            where: {
                user_id: userId,
                is_active: true
            },
            include: ['pet']
        });
    };

    UserPet.findByUserAndPet = function(userId, petId) {
        return this.findOne({
            where: {
                user_id: userId,
                pet_id: petId
            },
            include: ['pet']
        });
    };

    return UserPet;
};

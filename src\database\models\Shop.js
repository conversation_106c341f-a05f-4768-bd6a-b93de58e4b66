const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
    const Shop = sequelize.define('Shop', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        item_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'items',
                key: 'id'
            },
            onDelete: 'CASCADE'
        },
        category: {
            type: DataTypes.ENUM,
            values: ['weapons', 'armor', 'accessories', 'consumables', 'materials', 'premium', 'vip_exclusive'],
            allowNull: false,
            defaultValue: 'weapons'
        },
        price_gold: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0
            }
        },
        price_gems: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0
            }
        },
        // Requirements
        level_requirement: {
            type: DataTypes.INTEGER,
            defaultValue: 1,
            validate: {
                min: 1,
                max: 100
            }
        },
        vip_requirement: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0,
                max: 5
            }
        },
        // Stock system
        stock_quantity: {
            type: DataTypes.INTEGER,
            allowNull: true, // null = unlimited
            validate: {
                min: 0
            }
        },
        max_per_user: {
            type: DataTypes.INTEGER,
            allowNull: true, // null = unlimited
            validate: {
                min: 1
            }
        },
        reset_period: {
            type: DataTypes.ENUM,
            values: ['never', 'daily', 'weekly', 'monthly'],
            defaultValue: 'never'
        },
        // Availability
        is_available: {
            type: DataTypes.BOOLEAN,
            defaultValue: true
        },
        is_featured: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        },
        is_limited_time: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        },
        available_from: {
            type: DataTypes.DATE,
            allowNull: true
        },
        available_until: {
            type: DataTypes.DATE,
            allowNull: true
        },
        // Discounts
        discount_percentage: {
            type: DataTypes.DECIMAL(5, 2),
            defaultValue: 0.00,
            validate: {
                min: 0,
                max: 100
            }
        },
        discount_until: {
            type: DataTypes.DATE,
            allowNull: true
        },
        // Display
        display_order: {
            type: DataTypes.INTEGER,
            defaultValue: 0
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        // Purchase tracking
        total_purchases: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0
            }
        },
        last_purchased: {
            type: DataTypes.DATE,
            allowNull: true
        }
    }, {
        tableName: 'shop',
        indexes: [
            {
                fields: ['category']
            },
            {
                fields: ['is_available']
            },
            {
                fields: ['is_featured']
            },
            {
                fields: ['level_requirement']
            },
            {
                fields: ['vip_requirement']
            },
            {
                fields: ['display_order']
            }
        ]
    });

    // Static methods
    Shop.getAvailableItems = function(character, category = null) {
        const whereClause = {
            is_available: true,
            level_requirement: {
                [sequelize.Sequelize.Op.lte]: character.level
            },
            vip_requirement: {
                [sequelize.Sequelize.Op.lte]: character.vip_level
            }
        };

        if (category) {
            whereClause.category = category;
        }

        // Check time-limited availability
        const now = new Date();
        whereClause[sequelize.Sequelize.Op.or] = [
            { is_limited_time: false },
            {
                is_limited_time: true,
                available_from: { [sequelize.Sequelize.Op.lte]: now },
                available_until: { [sequelize.Sequelize.Op.gte]: now }
            }
        ];

        return this.findAll({
            where: whereClause,
            include: [
                {
                    association: 'item',
                    attributes: ['name', 'description', 'type', 'rarity', 'icon']
                }
            ],
            order: [
                ['is_featured', 'DESC'],
                ['display_order', 'ASC'],
                ['created_at', 'DESC']
            ]
        });
    };

    Shop.getFeaturedItems = function(limit = 5) {
        return this.findAll({
            where: {
                is_available: true,
                is_featured: true
            },
            include: ['item'],
            order: [['display_order', 'ASC']],
            limit: limit
        });
    };

    Shop.getCategories = function() {
        return [
            { key: 'weapons', name: 'Weapons', emoji: '⚔️' },
            { key: 'armor', name: 'Armor', emoji: '🛡️' },
            { key: 'accessories', name: 'Accessories', emoji: '💍' },
            { key: 'consumables', name: 'Consumables', emoji: '🧪' },
            { key: 'materials', name: 'Materials', emoji: '🔧' },
            { key: 'premium', name: 'Premium', emoji: '💎' },
            { key: 'vip_exclusive', name: 'VIP Exclusive', emoji: '👑' }
        ];
    };

    // Instance methods
    Shop.prototype.getCurrentPrice = function(currency = 'gold') {
        const basePrice = currency === 'gold' ? this.price_gold : this.price_gems;
        
        if (this.discount_percentage > 0 && this.discount_until && new Date() < this.discount_until) {
            return Math.floor(basePrice * (1 - this.discount_percentage / 100));
        }
        
        return basePrice;
    };

    Shop.prototype.isAvailableFor = function(character) {
        if (!this.is_available) return false;
        if (character.level < this.level_requirement) return false;
        if (character.vip_level < this.vip_requirement) return false;

        // Check time availability
        if (this.is_limited_time) {
            const now = new Date();
            if (this.available_from && now < this.available_from) return false;
            if (this.available_until && now > this.available_until) return false;
        }

        return true;
    };

    Shop.prototype.hasStock = function() {
        if (this.stock_quantity === null) return true; // Unlimited stock
        return this.stock_quantity > 0;
    };

    Shop.prototype.canUserPurchase = function(userId, userPurchaseCount = 0) {
        if (!this.hasStock()) return false;
        if (this.max_per_user !== null && userPurchaseCount >= this.max_per_user) return false;
        return true;
    };

    Shop.prototype.purchase = function(quantity = 1) {
        if (this.stock_quantity !== null) {
            this.stock_quantity = Math.max(0, this.stock_quantity - quantity);
        }
        
        this.total_purchases += quantity;
        this.last_purchased = new Date();
        
        return this.save();
    };

    Shop.prototype.getFormattedPrice = function() {
        const goldPrice = this.getCurrentPrice('gold');
        const gemPrice = this.getCurrentPrice('gems');
        
        const prices = [];
        
        if (goldPrice > 0) {
            prices.push(`${goldPrice.toLocaleString()} 🪙`);
        }
        
        if (gemPrice > 0) {
            prices.push(`${gemPrice.toLocaleString()} 💎`);
        }
        
        return prices.join(' or ') || 'Free';
    };

    Shop.prototype.getDiscountInfo = function() {
        if (this.discount_percentage <= 0 || !this.discount_until || new Date() >= this.discount_until) {
            return null;
        }

        const timeLeft = Math.ceil((this.discount_until - new Date()) / (1000 * 60 * 60)); // hours
        
        return {
            percentage: this.discount_percentage,
            timeLeft: timeLeft,
            formattedTimeLeft: timeLeft > 24 ? `${Math.ceil(timeLeft / 24)} days` : `${timeLeft} hours`
        };
    };

    return Shop;
};

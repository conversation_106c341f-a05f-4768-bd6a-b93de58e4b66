#!/usr/bin/env node

const { sequelize } = require('../src/database/database');
const DatabaseInitializer = require('../src/database/init');

async function initDatabase() {
    try {
        console.log('🚀 Initializing AFK Game Bot Database...\n');
        
        const dbInit = new DatabaseInitializer(sequelize);
        await dbInit.initialize();
        
        console.log('\n✅ Database initialization completed successfully!');
        console.log('🎮 Your AFK Game Bot is ready to use!');
        
    } catch (error) {
        console.error('\n❌ Database initialization failed:', error);
        process.exit(1);
    } finally {
        await sequelize.close();
    }
}

// Run if called directly
if (require.main === module) {
    initDatabase();
}

module.exports = initDatabase;

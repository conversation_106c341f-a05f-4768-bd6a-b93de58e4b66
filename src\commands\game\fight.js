const { User, Character, Transaction } = require('../../database/database');
const GameEmbedBuilder = require('../../utils/embedBuilder');
const CombatEngine = require('../../utils/combatEngine');

module.exports = {
    data: {
        name: 'fight',
        aliases: ['f', 'battle', 'attack'],
        description: 'Fight a random monster to gain experience and gold',
        usage: '!fight',
        cooldown: 30
    },
    async execute(message, args) {
        try {
            // Get user and character
            const user = await User.findByDiscordId(message.author.id);
            const character = await Character.findOne({
                where: { user_id: user.id }
            });
            
            // Check if character can fight
            const canFight = await CombatEngine.canCharacterFight(character);
            if (!canFight.canFight) {
                if (canFight.reason === 'cooldown') {
                    const embed = GameEmbedBuilder.createCooldownEmbed('fight', canFight.remainingTime);
                    return message.reply({ embeds: [embed] });
                } else {
                    const embed = GameEmbedBuilder.createErrorEmbed('Cannot Fight', canFight.reason);
                    return message.reply({ embeds: [embed] });
                }
            }
            
            // Find a random monster
            const monster = await CombatEngine.getRandomMonster(character, 'normal');
            if (!monster) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'No Monsters Available',
                    'No suitable monsters found for your level. Try again later!'
                );
                return message.reply({ embeds: [embed] });
            }
            
            // Send initial combat message
            const startEmbed = GameEmbedBuilder.createInfoEmbed(
                '⚔️ Combat Started!',
                `You encounter ${monster.getFormattedName()}!\nPreparing for battle...`
            );
            const combatMessage = await message.reply({ embeds: [startEmbed] });
            
            // Simulate the battle
            const battleResult = await CombatEngine.simulateBattle(character, monster, 'fight');
            
            // Apply VIP cooldown reduction
            const vipReduction = CombatEngine.calculateVipCooldownReduction(character.vip_level);
            const baseCooldown = 30000; // 30 seconds
            const actualCooldown = Math.floor(baseCooldown * (1 - vipReduction));
            
            // Update character cooldown
            character.last_fight = new Date();
            
            // Apply battle rewards if victory
            let levelUpMessage = '';
            if (battleResult.result === 'victory') {
                const rewardResult = await CombatEngine.applyBattleRewards(
                    character, 
                    battleResult.rewards, 
                    battleResult.drops
                );
                
                if (rewardResult.leveledUp) {
                    levelUpMessage = `\n🎉 **LEVEL UP!** You are now level ${character.level}!`;
                }
                
                // Create transaction record
                await Transaction.createReward(
                    user.id,
                    battleResult.rewards.gold,
                    battleResult.rewards.gems,
                    character.gold,
                    character.gems,
                    `Combat victory vs ${monster.name}`,
                    'monster',
                    monster.id
                );
            }
            
            // Save character
            await character.save();
            
            // Create result embed
            const resultEmbed = GameEmbedBuilder.createCombatResultEmbed(battleResult);
            
            // Add level up message if applicable
            if (levelUpMessage) {
                const currentDescription = resultEmbed.data.description || '';
                resultEmbed.setDescription(currentDescription + levelUpMessage);
            }
            
            // Add dropped items information
            if (battleResult.drops && battleResult.drops.length > 0) {
                const { Item } = require('../../database/database');
                const dropTexts = [];
                
                for (const drop of battleResult.drops) {
                    const item = await Item.findByPk(drop.item_id);
                    if (item) {
                        const rarityInfo = item.getRarityInfo();
                        dropTexts.push(`${rarityInfo.emoji} ${item.name} x${drop.quantity}`);
                    }
                }
                
                if (dropTexts.length > 0) {
                    resultEmbed.addFields({
                        name: '📦 Items Dropped',
                        value: dropTexts.join('\n'),
                        inline: false
                    });
                }
            }
            
            // Add cooldown information
            if (actualCooldown < baseCooldown) {
                const savedTime = Math.floor((baseCooldown - actualCooldown) / 1000);
                resultEmbed.addFields({
                    name: '👑 VIP Benefit',
                    value: `Cooldown reduced by ${savedTime}s (${Math.round(vipReduction * 100)}% VIP bonus)`,
                    inline: false
                });
            }
            
            // Add quick action buttons
            const nextCooldown = actualCooldown / 1000;
            resultEmbed.setFooter({ 
                text: `Next fight available in ${nextCooldown}s | Use !profile to see your stats` 
            });
            
            // Update the combat message with results
            await combatMessage.edit({ embeds: [resultEmbed] });
            
            // Add reaction for quick repeat (if no cooldown)
            if (actualCooldown <= 5000) { // If cooldown is 5 seconds or less
                await combatMessage.react('⚔️');
                
                // Create reaction collector for quick repeat
                const filter = (reaction, user) => {
                    return reaction.emoji.name === '⚔️' && user.id === message.author.id;
                };
                
                const collector = combatMessage.createReactionCollector({ 
                    filter, 
                    time: actualCooldown,
                    max: 1 
                });
                
                collector.on('collect', async () => {
                    // Wait for cooldown to finish
                    setTimeout(async () => {
                        try {
                            // Re-run fight command
                            await this.execute(message, args);
                        } catch (error) {
                            console.error('Error in quick repeat fight:', error);
                        }
                    }, Math.max(0, actualCooldown - 1000)); // Small buffer
                });
            }
            
        } catch (error) {
            console.error('Error in fight command:', error);
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Combat Error',
                'An error occurred during combat. Please try again.'
            );
            await message.reply({ embeds: [embed] });
        }
    }
};

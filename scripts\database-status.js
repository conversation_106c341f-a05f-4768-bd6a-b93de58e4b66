#!/usr/bin/env node

const { sequelize } = require('../src/database/database');
const DatabaseInitializer = require('../src/database/init');

async function showDatabaseStatus() {
    try {
        console.log('📊 AFK Game Bot Database Status\n');
        
        const dbInit = new DatabaseInitializer(sequelize);
        const status = await dbInit.getStatus();
        
        console.log('🔄 Migrations:');
        console.log(`  ✅ Executed: ${status.migrations.executed.length}`);
        status.migrations.executed.forEach(migration => {
            console.log(`    - ${migration}`);
        });
        
        if (status.migrations.pending.length > 0) {
            console.log(`  ⏳ Pending: ${status.migrations.pending.length}`);
            status.migrations.pending.forEach(migration => {
                console.log(`    - ${migration}`);
            });
        } else {
            console.log('  ✅ All migrations up to date');
        }
        
        console.log('\n🌱 Seeders:');
        console.log(`  ✅ Executed: ${status.seeders.executed.length}`);
        status.seeders.executed.forEach(seeder => {
            console.log(`    - ${seeder}`);
        });
        
        if (status.seeders.pending.length > 0) {
            console.log(`  ⏳ Pending: ${status.seeders.pending.length}`);
            status.seeders.pending.forEach(seeder => {
                console.log(`    - ${seeder}`);
            });
        } else {
            console.log('  ✅ All seeders up to date');
        }
        
        // Test database connection
        console.log('\n🔗 Connection Test:');
        await sequelize.authenticate();
        console.log('  ✅ Database connection successful');
        
        // Show table counts
        console.log('\n📋 Table Statistics:');
        try {
            const { User, Character, Item, Monster, BattleLog, Transaction } = require('../src/database/database');
            
            const userCount = await User.count();
            const characterCount = await Character.count();
            const itemCount = await Item.count();
            const monsterCount = await Monster.count();
            const battleCount = await BattleLog.count();
            const transactionCount = await Transaction.count();
            
            console.log(`  👥 Users: ${userCount}`);
            console.log(`  🎮 Characters: ${characterCount}`);
            console.log(`  📦 Items: ${itemCount}`);
            console.log(`  👹 Monsters: ${monsterCount}`);
            console.log(`  ⚔️ Battle Logs: ${battleCount}`);
            console.log(`  💰 Transactions: ${transactionCount}`);
            
        } catch (error) {
            console.log('  ⚠️ Could not retrieve table statistics (tables may not exist yet)');
        }
        
    } catch (error) {
        console.error('❌ Error checking database status:', error);
        process.exit(1);
    } finally {
        await sequelize.close();
    }
}

// Run if called directly
if (require.main === module) {
    showDatabaseStatus();
}

module.exports = showDatabaseStatus;

const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
    const Item = sequelize.define('Item', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notEmpty: true,
                len: [1, 100]
            }
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        type: {
            type: DataTypes.ENUM,
            values: ['weapon', 'armor', 'helmet', 'boots', 'ring', 'necklace', 'consumable', 'material'],
            allowNull: false
        },
        rarity: {
            type: DataTypes.ENUM,
            values: ['common', 'rare', 'epic', 'legendary', 'mythic'],
            allowNull: false,
            defaultValue: 'common'
        },
        level_requirement: {
            type: DataTypes.INTEGER,
            defaultValue: 1,
            validate: {
                min: 1,
                max: 100
            }
        },
        // Base stats
        attack_bonus: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0
            }
        },
        defense_bonus: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0
            }
        },
        health_bonus: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0
            }
        },
        crit_rate_bonus: {
            type: DataTypes.DECIMAL(5, 2),
            defaultValue: 0.00,
            validate: {
                min: 0,
                max: 100
            }
        },
        crit_damage_bonus: {
            type: DataTypes.DECIMAL(6, 2),
            defaultValue: 0.00,
            validate: {
                min: 0
            }
        },
        luck_bonus: {
            type: DataTypes.DECIMAL(5, 2),
            defaultValue: 0.00,
            validate: {
                min: 0
            }
        },
        // Economy
        base_price: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0
            }
        },
        sell_price: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0
            }
        },
        gem_price: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0
            }
        },
        // Properties
        is_tradeable: {
            type: DataTypes.BOOLEAN,
            defaultValue: true
        },
        is_sellable: {
            type: DataTypes.BOOLEAN,
            defaultValue: true
        },
        is_upgradeable: {
            type: DataTypes.BOOLEAN,
            defaultValue: true
        },
        max_upgrade_level: {
            type: DataTypes.INTEGER,
            defaultValue: 10,
            validate: {
                min: 0,
                max: 20
            }
        },
        // Visual
        icon: {
            type: DataTypes.STRING,
            allowNull: true
        },
        color: {
            type: DataTypes.STRING(7),
            allowNull: true,
            validate: {
                is: /^#[0-9A-F]{6}$/i
            }
        },
        // Special effects
        special_effects: {
            type: DataTypes.JSONB,
            defaultValue: []
        },
        // Drop information
        drop_sources: {
            type: DataTypes.JSONB,
            defaultValue: []
        },
        drop_rate: {
            type: DataTypes.DECIMAL(5, 4),
            defaultValue: 0.0000,
            validate: {
                min: 0,
                max: 1
            }
        }
    }, {
        tableName: 'items',
        indexes: [
            {
                fields: ['type']
            },
            {
                fields: ['rarity']
            },
            {
                fields: ['level_requirement']
            }
        ]
    });

    // Static methods
    Item.getRarityInfo = function(rarity) {
        const rarityData = {
            common: {
                name: 'Common',
                color: '#FFFFFF',
                emoji: '⚪',
                multiplier: 1.0
            },
            rare: {
                name: 'Rare',
                color: '#00FF00',
                emoji: '🟢',
                multiplier: 1.5
            },
            epic: {
                name: 'Epic',
                color: '#9932CC',
                emoji: '🟣',
                multiplier: 2.0
            },
            legendary: {
                name: 'Legendary',
                color: '#FFA500',
                emoji: '🟠',
                multiplier: 3.0
            },
            mythic: {
                name: 'Mythic',
                color: '#FF0000',
                emoji: '🔴',
                multiplier: 5.0
            }
        };

        return rarityData[rarity] || rarityData.common;
    };

    Item.getTypeInfo = function(type) {
        const typeData = {
            weapon: { name: 'Weapon', emoji: '⚔️', slot: 'weapon' },
            armor: { name: 'Armor', emoji: '🛡️', slot: 'armor' },
            helmet: { name: 'Helmet', emoji: '⛑️', slot: 'helmet' },
            boots: { name: 'Boots', emoji: '👢', slot: 'boots' },
            ring: { name: 'Ring', emoji: '💍', slot: 'ring' },
            necklace: { name: 'Necklace', emoji: '📿', slot: 'necklace' },
            consumable: { name: 'Consumable', emoji: '🧪', slot: null },
            material: { name: 'Material', emoji: '🔧', slot: null }
        };

        return typeData[type] || typeData.weapon;
    };

    // Instance methods
    Item.prototype.getRarityInfo = function() {
        return Item.getRarityInfo(this.rarity);
    };

    Item.prototype.getTypeInfo = function() {
        return Item.getTypeInfo(this.type);
    };

    Item.prototype.getFormattedName = function() {
        const rarityInfo = this.getRarityInfo();
        const typeInfo = this.getTypeInfo();
        return `${rarityInfo.emoji} ${this.name} ${typeInfo.emoji}`;
    };

    Item.prototype.getTotalStats = function(upgradeLevel = 0) {
        const multiplier = 1 + (upgradeLevel * 0.1); // 10% increase per upgrade level
        
        return {
            attack: Math.floor(this.attack_bonus * multiplier),
            defense: Math.floor(this.defense_bonus * multiplier),
            health: Math.floor(this.health_bonus * multiplier),
            crit_rate: parseFloat((this.crit_rate_bonus * multiplier).toFixed(2)),
            crit_damage: parseFloat((this.crit_damage_bonus * multiplier).toFixed(2)),
            luck: parseFloat((this.luck_bonus * multiplier).toFixed(2))
        };
    };

    Item.prototype.getUpgradeCost = function(currentLevel) {
        if (currentLevel >= this.max_upgrade_level) return null;
        
        const baseCost = this.base_price || 100;
        const rarityMultiplier = Item.getRarityInfo(this.rarity).multiplier;
        
        return Math.floor(baseCost * rarityMultiplier * Math.pow(1.5, currentLevel));
    };

    Item.prototype.canBeEquippedBy = function(character) {
        return character.level >= this.level_requirement;
    };

    return Item;
};

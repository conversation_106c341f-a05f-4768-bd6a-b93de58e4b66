const { DataTypes } = require('sequelize');

module.exports = {
    up: async (queryInterface, Sequelize) => {
        // Create transactions table
        await queryInterface.createTable('transactions', {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            user_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'users',
                    key: 'id'
                },
                onDelete: 'CASCADE'
            },
            type: {
                type: DataTypes.ENUM('purchase', 'sale', 'reward', 'penalty', 'transfer', 'gacha', 'upgrade', 'repair', 'daily', 'admin', 'payment', 'refund', 'vip_purchase'),
                allowNull: false
            },
            gold_change: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            gems_change: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            gold_balance_after: {
                type: DataTypes.BIGINT,
                allowNull: false
            },
            gems_balance_after: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            description: {
                type: DataTypes.STRING,
                allowNull: false
            },
            reference_type: {
                type: DataTypes.STRING,
                allowNull: true
            },
            reference_id: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            items_involved: {
                type: DataTypes.JSONB,
                defaultValue: []
            },
            payment_method: {
                type: DataTypes.STRING,
                allowNull: true
            },
            payment_id: {
                type: DataTypes.STRING,
                allowNull: true
            },
            payment_amount: {
                type: DataTypes.DECIMAL(10, 2),
                allowNull: true
            },
            payment_currency: {
                type: DataTypes.STRING(3),
                allowNull: true
            },
            payment_status: {
                type: DataTypes.ENUM('pending', 'completed', 'failed', 'refunded'),
                allowNull: true
            },
            metadata: {
                type: DataTypes.JSONB,
                defaultValue: {}
            },
            admin_id: {
                type: DataTypes.STRING,
                allowNull: true
            },
            admin_reason: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            status: {
                type: DataTypes.ENUM('pending', 'completed', 'failed', 'cancelled'),
                defaultValue: 'completed'
            },
            ip_address: {
                type: DataTypes.INET,
                allowNull: true
            },
            user_agent: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            created_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            },
            updated_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            }
        });

        // Create pets table
        await queryInterface.createTable('pets', {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            name: {
                type: DataTypes.STRING,
                allowNull: false
            },
            description: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            type: {
                type: DataTypes.ENUM('combat', 'support', 'gathering', 'special'),
                allowNull: false,
                defaultValue: 'combat'
            },
            rarity: {
                type: DataTypes.ENUM('common', 'rare', 'epic', 'legendary', 'mythic'),
                allowNull: false,
                defaultValue: 'common'
            },
            base_attack: {
                type: DataTypes.INTEGER,
                defaultValue: 5
            },
            base_defense: {
                type: DataTypes.INTEGER,
                defaultValue: 3
            },
            base_health: {
                type: DataTypes.INTEGER,
                defaultValue: 50
            },
            attack_growth: {
                type: DataTypes.DECIMAL(4, 2),
                defaultValue: 1.00
            },
            defense_growth: {
                type: DataTypes.DECIMAL(4, 2),
                defaultValue: 0.50
            },
            health_growth: {
                type: DataTypes.DECIMAL(4, 2),
                defaultValue: 5.00
            },
            special_abilities: {
                type: DataTypes.JSONB,
                defaultValue: []
            },
            max_level: {
                type: DataTypes.INTEGER,
                defaultValue: 50
            },
            evolution_pet_id: {
                type: DataTypes.INTEGER,
                allowNull: true,
                references: {
                    model: 'pets',
                    key: 'id'
                }
            },
            evolution_level_required: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            evolution_materials: {
                type: DataTypes.JSONB,
                defaultValue: []
            },
            acquisition_method: {
                type: DataTypes.ENUM('gacha', 'shop', 'quest', 'event', 'evolution', 'vip_exclusive'),
                allowNull: false,
                defaultValue: 'gacha'
            },
            acquisition_cost: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            acquisition_currency: {
                type: DataTypes.ENUM('gold', 'gems'),
                defaultValue: 'gems'
            },
            level_requirement: {
                type: DataTypes.INTEGER,
                defaultValue: 1
            },
            vip_requirement: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            emoji: {
                type: DataTypes.STRING,
                defaultValue: '🐾'
            },
            color: {
                type: DataTypes.STRING(7),
                allowNull: true
            },
            is_available: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            is_event_pet: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            event_start: {
                type: DataTypes.DATE,
                allowNull: true
            },
            event_end: {
                type: DataTypes.DATE,
                allowNull: true
            },
            created_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            },
            updated_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            }
        });

        // Create user_pets table
        await queryInterface.createTable('user_pets', {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            user_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'users',
                    key: 'id'
                },
                onDelete: 'CASCADE'
            },
            pet_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'pets',
                    key: 'id'
                },
                onDelete: 'CASCADE'
            },
            level: {
                type: DataTypes.INTEGER,
                defaultValue: 1
            },
            experience: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            happiness: {
                type: DataTypes.INTEGER,
                defaultValue: 100
            },
            hunger: {
                type: DataTypes.INTEGER,
                defaultValue: 100
            },
            health: {
                type: DataTypes.INTEGER,
                defaultValue: 100
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            last_fed: {
                type: DataTypes.DATE,
                allowNull: true
            },
            last_played: {
                type: DataTypes.DATE,
                allowNull: true
            },
            last_battle: {
                type: DataTypes.DATE,
                allowNull: true
            },
            battles_won: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            battles_lost: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            total_damage_dealt: {
                type: DataTypes.BIGINT,
                defaultValue: 0
            },
            nickname: {
                type: DataTypes.STRING(30),
                allowNull: true
            },
            acquired_at: {
                type: DataTypes.DATE,
                defaultValue: DataTypes.NOW
            },
            acquired_from: {
                type: DataTypes.STRING,
                allowNull: true
            },
            created_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            },
            updated_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            }
        });

        // Add indexes
        await queryInterface.addIndex('transactions', ['user_id']);
        await queryInterface.addIndex('transactions', ['type']);
        await queryInterface.addIndex('transactions', ['status']);
        await queryInterface.addIndex('transactions', ['payment_status']);
        await queryInterface.addIndex('transactions', ['created_at']);
        await queryInterface.addIndex('transactions', ['user_id', 'created_at']);
        await queryInterface.addIndex('transactions', ['reference_type', 'reference_id']);

        await queryInterface.addIndex('pets', ['type']);
        await queryInterface.addIndex('pets', ['rarity']);
        await queryInterface.addIndex('pets', ['acquisition_method']);
        await queryInterface.addIndex('pets', ['is_available']);

        await queryInterface.addIndex('user_pets', ['user_id']);
        await queryInterface.addIndex('user_pets', ['pet_id']);
        await queryInterface.addIndex('user_pets', ['user_id', 'is_active']);
        await queryInterface.addIndex('user_pets', ['level']);
    },

    down: async (queryInterface, Sequelize) => {
        await queryInterface.dropTable('user_pets');
        await queryInterface.dropTable('pets');
        await queryInterface.dropTable('transactions');
    }
};

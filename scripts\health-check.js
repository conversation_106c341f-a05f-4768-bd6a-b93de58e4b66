#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

class HealthChecker {
    constructor() {
        this.issues = [];
        this.passed = [];
        this.srcPath = path.join(__dirname, '../src');
    }

    async runHealthCheck() {
        console.log('🏥 Running AFK Game Bot Health Check...\n');

        try {
            // Database checks
            await this.checkDatabase();
            
            // Model checks
            await this.checkModels();
            
            // Command checks
            await this.checkCommands();
            
            // Configuration checks
            await this.checkConfiguration();
            
            // File structure checks
            await this.checkFileStructure();
            
            this.generateReport();
            
        } catch (error) {
            console.error('❌ Health check failed:', error);
            process.exit(1);
        }
    }

    async checkDatabase() {
        console.log('🗄️ Checking database...');
        
        try {
            const { sequelize } = require('../src/database/database');
            await sequelize.authenticate();
            this.passed.push('Database connection successful');
            
            // Check if database file exists
            const dbPath = path.join(__dirname, '../database.sqlite');
            if (fs.existsSync(dbPath)) {
                this.passed.push('Database file exists');
            } else {
                this.issues.push('Database file not found');
            }
            
        } catch (error) {
            this.issues.push(`Database connection failed: ${error.message}`);
        }
        
        console.log('  ✅ Database checks completed');
    }

    async checkModels() {
        console.log('📋 Checking models...');
        
        try {
            const { User, Character, Item, UserItem, Transaction } = require('../src/database/database');
            
            // Check if models are properly defined
            const models = { User, Character, Item, UserItem, Transaction };
            
            for (const [name, model] of Object.entries(models)) {
                if (model && typeof model.findAll === 'function') {
                    this.passed.push(`${name} model loaded correctly`);
                } else {
                    this.issues.push(`${name} model has issues`);
                }
            }
            
            // Check Character methods
            if (typeof Character.prototype.getVipBenefits === 'function') {
                this.passed.push('Character.getVipBenefits method exists');
            } else {
                this.issues.push('Character.getVipBenefits method missing');
            }
            
        } catch (error) {
            this.issues.push(`Model loading failed: ${error.message}`);
        }
        
        console.log('  ✅ Model checks completed');
    }

    async checkCommands() {
        console.log('⚙️ Checking commands...');
        
        const commandsPath = path.join(this.srcPath, 'commands');
        let totalCommands = 0;
        let validCommands = 0;
        let errorCommands = 0;
        
        await this.processDirectory(commandsPath, (filePath) => {
            if (!filePath.endsWith('.js')) return;
            
            totalCommands++;
            
            try {
                delete require.cache[require.resolve(filePath)];
                const command = require(filePath);
                
                if (command.data && command.data.name && command.execute) {
                    validCommands++;
                    
                    // Check for common issues
                    const content = fs.readFileSync(filePath, 'utf8');

                    // Check for actual Shop model usage (not just text mentions)
                    if ((content.includes('Shop.') || content.includes('require(\'.*Shop\')') || content.includes('{ Shop')) && !content.includes('// Shop')) {
                        this.issues.push(`${path.basename(filePath)} still references Shop model`);
                    }
                    
                    if (content.includes('getBenefits') && !content.includes('getVipBenefits')) {
                        this.issues.push(`${path.basename(filePath)} uses old VIP benefits method`);
                    }
                    
                } else {
                    errorCommands++;
                    this.issues.push(`${path.basename(filePath)} missing required properties`);
                }
                
            } catch (error) {
                errorCommands++;
                this.issues.push(`${path.basename(filePath)} failed to load: ${error.message}`);
            }
        });
        
        this.passed.push(`${validCommands}/${totalCommands} commands loaded successfully`);
        
        if (errorCommands > 0) {
            this.issues.push(`${errorCommands} commands have errors`);
        }
        
        console.log('  ✅ Command checks completed');
    }

    async checkConfiguration() {
        console.log('⚙️ Checking configuration...');
        
        // Check .env file
        const envPath = path.join(__dirname, '../.env');
        if (fs.existsSync(envPath)) {
            this.passed.push('.env file exists');
            
            const envContent = fs.readFileSync(envPath, 'utf8');
            const requiredVars = ['DISCORD_TOKEN', 'PREFIX'];
            
            for (const varName of requiredVars) {
                if (envContent.includes(`${varName}=`)) {
                    this.passed.push(`${varName} configured`);
                } else {
                    this.issues.push(`${varName} not configured`);
                }
            }
        } else {
            this.issues.push('.env file not found');
        }
        
        // Check package.json
        const packagePath = path.join(__dirname, '../package.json');
        if (fs.existsSync(packagePath)) {
            this.passed.push('package.json exists');
            
            const packageJson = JSON.parse(fs.readFileSync(packagePath, 'utf8'));
            
            if (packageJson.scripts && packageJson.scripts.start) {
                this.passed.push('Start script configured');
            } else {
                this.issues.push('Start script missing');
            }
        } else {
            this.issues.push('package.json not found');
        }
        
        console.log('  ✅ Configuration checks completed');
    }

    async checkFileStructure() {
        console.log('📁 Checking file structure...');
        
        const requiredFiles = [
            'src/bot.js',
            'src/database/database.js',
            'src/database/models/User.js',
            'src/database/models/Character.js',
            'src/database/models/Item.js',
            'src/utils/embedBuilder.js',
            'src/database/migrations/001-initial-setup.js'
        ];
        
        for (const file of requiredFiles) {
            const filePath = path.join(__dirname, '..', file);
            if (fs.existsSync(filePath)) {
                this.passed.push(`${file} exists`);
            } else {
                this.issues.push(`${file} missing`);
            }
        }
        
        console.log('  ✅ File structure checks completed');
    }

    async processDirectory(dir, processor) {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                await this.processDirectory(fullPath, processor);
            } else {
                await processor(fullPath);
            }
        }
    }

    generateReport() {
        console.log('\n📋 Health Check Report:');
        console.log(`✅ ${this.passed.length} checks passed`);
        console.log(`❌ ${this.issues.length} issues found\n`);
        
        if (this.passed.length > 0) {
            console.log('✅ Passed Checks:');
            this.passed.forEach(check => console.log(`  • ${check}`));
        }
        
        if (this.issues.length > 0) {
            console.log('\n❌ Issues Found:');
            this.issues.forEach(issue => console.log(`  • ${issue}`));
            
            console.log('\n💡 Recommendations:');
            console.log('  1. Fix the issues listed above');
            console.log('  2. Run the health check again');
            console.log('  3. Test bot functionality with: ihelp, iprofile, ishop');
        } else {
            console.log('\n🎉 All checks passed! Your bot is healthy!');
            console.log('\n🎮 Ready to use:');
            console.log('  • ihelp - View commands');
            console.log('  • iprofile - View profile');
            console.log('  • ishop - Browse shop');
            console.log('  • ifight - Fight monsters');
            console.log('  • idaily - Daily rewards');
        }
        
        console.log(`\n📊 Overall Health: ${this.issues.length === 0 ? '🟢 HEALTHY' : '🟡 NEEDS ATTENTION'}`);
    }
}

// Run if called directly
if (require.main === module) {
    const checker = new HealthChecker();
    checker.runHealthCheck();
}

module.exports = HealthChecker;

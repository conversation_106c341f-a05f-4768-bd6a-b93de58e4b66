module.exports = {
    up: async (queryInterface, Sequelize) => {
        await queryInterface.bulkInsert('items', [
            // Starter Weapons
            {
                name: 'Rusty Sword',
                description: 'A worn but reliable blade for beginners',
                type: 'weapon',
                rarity: 'common',
                level_requirement: 1,
                attack_bonus: 5,
                defense_bonus: 0,
                health_bonus: 0,
                base_price: 50,
                sell_price: 25,
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                name: 'Iron Sword',
                description: 'A sturdy iron blade with decent sharpness',
                type: 'weapon',
                rarity: 'common',
                level_requirement: 5,
                attack_bonus: 12,
                defense_bonus: 0,
                health_bonus: 0,
                base_price: 200,
                sell_price: 100,
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                name: 'Steel Blade',
                description: 'A well-crafted steel sword for experienced fighters',
                type: 'weapon',
                rarity: 'rare',
                level_requirement: 10,
                attack_bonus: 25,
                defense_bonus: 2,
                health_bonus: 0,
                crit_rate_bonus: 2.00,
                base_price: 800,
                sell_price: 400,
                created_at: new Date(),
                updated_at: new Date()
            },
            
            // Starter Armor
            {
                name: '<PERSON><PERSON><PERSON>',
                description: 'Basic cloth protection for novice adventurers',
                type: 'armor',
                rarity: 'common',
                level_requirement: 1,
                attack_bonus: 0,
                defense_bonus: 3,
                health_bonus: 10,
                base_price: 40,
                sell_price: 20,
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                name: 'Leather Armor',
                description: 'Flexible leather protection offering decent defense',
                type: 'armor',
                rarity: 'common',
                level_requirement: 3,
                attack_bonus: 0,
                defense_bonus: 8,
                health_bonus: 25,
                base_price: 150,
                sell_price: 75,
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                name: 'Chain Mail',
                description: 'Interlocked metal rings providing solid protection',
                type: 'armor',
                rarity: 'rare',
                level_requirement: 8,
                attack_bonus: 0,
                defense_bonus: 18,
                health_bonus: 50,
                base_price: 600,
                sell_price: 300,
                created_at: new Date(),
                updated_at: new Date()
            },
            
            // Starter Helmets
            {
                name: 'Cloth Cap',
                description: 'A simple cap offering minimal head protection',
                type: 'helmet',
                rarity: 'common',
                level_requirement: 1,
                attack_bonus: 0,
                defense_bonus: 2,
                health_bonus: 5,
                base_price: 30,
                sell_price: 15,
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                name: 'Leather Helmet',
                description: 'Sturdy leather headgear for better protection',
                type: 'helmet',
                rarity: 'common',
                level_requirement: 4,
                attack_bonus: 0,
                defense_bonus: 6,
                health_bonus: 15,
                base_price: 120,
                sell_price: 60,
                created_at: new Date(),
                updated_at: new Date()
            },
            
            // Starter Boots
            {
                name: 'Worn Boots',
                description: 'Old but functional footwear',
                type: 'boots',
                rarity: 'common',
                level_requirement: 1,
                attack_bonus: 1,
                defense_bonus: 1,
                health_bonus: 5,
                base_price: 25,
                sell_price: 12,
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                name: 'Leather Boots',
                description: 'Comfortable leather boots for long journeys',
                type: 'boots',
                rarity: 'common',
                level_requirement: 3,
                attack_bonus: 2,
                defense_bonus: 4,
                health_bonus: 10,
                luck_bonus: 0.50,
                base_price: 100,
                sell_price: 50,
                created_at: new Date(),
                updated_at: new Date()
            },
            
            // Starter Accessories
            {
                name: 'Copper Ring',
                description: 'A simple copper band with minor magical properties',
                type: 'ring',
                rarity: 'common',
                level_requirement: 2,
                attack_bonus: 2,
                defense_bonus: 1,
                health_bonus: 5,
                crit_rate_bonus: 1.00,
                base_price: 80,
                sell_price: 40,
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                name: 'Silver Ring',
                description: 'A polished silver ring that enhances combat abilities',
                type: 'ring',
                rarity: 'rare',
                level_requirement: 7,
                attack_bonus: 5,
                defense_bonus: 3,
                health_bonus: 15,
                crit_rate_bonus: 3.00,
                crit_damage_bonus: 10.00,
                base_price: 400,
                sell_price: 200,
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                name: 'Wooden Pendant',
                description: 'A carved wooden pendant bringing good fortune',
                type: 'necklace',
                rarity: 'common',
                level_requirement: 2,
                attack_bonus: 1,
                defense_bonus: 1,
                health_bonus: 8,
                luck_bonus: 1.00,
                base_price: 60,
                sell_price: 30,
                created_at: new Date(),
                updated_at: new Date()
            },
            
            // Consumables
            {
                name: 'Health Potion',
                description: 'Restores 50 health points',
                type: 'consumable',
                rarity: 'common',
                level_requirement: 1,
                health_bonus: 50,
                base_price: 20,
                sell_price: 10,
                special_effects: JSON.stringify([{ type: 'heal', value: 50 }]),
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                name: 'Mana Potion',
                description: 'Restores magical energy (future feature)',
                type: 'consumable',
                rarity: 'common',
                level_requirement: 1,
                base_price: 25,
                sell_price: 12,
                special_effects: JSON.stringify([{ type: 'mana', value: 30 }]),
                created_at: new Date(),
                updated_at: new Date()
            },
            
            // Materials
            {
                name: 'Iron Ore',
                description: 'Raw iron ore used for crafting and upgrades',
                type: 'material',
                rarity: 'common',
                level_requirement: 1,
                base_price: 5,
                sell_price: 2,
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                name: 'Leather Scraps',
                description: 'Pieces of leather useful for repairs and crafting',
                type: 'material',
                rarity: 'common',
                level_requirement: 1,
                base_price: 3,
                sell_price: 1,
                created_at: new Date(),
                updated_at: new Date()
            }
        ]);
    },

    down: async (queryInterface, Sequelize) => {
        await queryInterface.bulkDelete('items', null, {});
    }
};

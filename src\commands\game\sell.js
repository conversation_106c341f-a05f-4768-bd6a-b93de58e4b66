const { User, Character, UserItem, Item, Transaction } = require('../../database/database');
const GameEmbedBuilder = require('../../utils/embedBuilder');

module.exports = {
    data: {
        name: 'sell',
        aliases: ['s', 'sellitem'],
        description: 'Sell an item from your inventory for gold',
        usage: '!sell <item_name> [quantity]',
        cooldown: 3
    },
    async execute(message, args) {
        try {
            if (args.length === 0) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Missing Item Name',
                    'Please specify the item you want to sell.\n**Usage:** `!sell <item_name> [quantity]`'
                );
                return message.reply({ embeds: [embed] });
            }
            
            // Get user and character
            const user = await User.findByDiscordId(message.author.id);
            const character = await Character.findOne({
                where: { user_id: user.id }
            });
            
            // Parse arguments
            let quantity = 1;
            let itemName = args.join(' ').toLowerCase();
            
            // Check if last argument is a number (quantity)
            const lastArg = args[args.length - 1];
            if (!isNaN(lastArg) && parseInt(lastArg) > 0) {
                quantity = parseInt(lastArg);
                itemName = args.slice(0, -1).join(' ').toLowerCase();
            }
            
            // Find the item in user's inventory
            const userItems = await UserItem.findAll({
                where: {
                    user_id: user.id,
                    quantity: { [require('sequelize').Op.gt]: 0 }
                },
                include: [{
                    model: Item,
                    as: 'item',
                    where: {
                        name: { [require('sequelize').Op.iLike]: `%${itemName}%` },
                        is_sellable: true
                    }
                }]
            });
            
            if (userItems.length === 0) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Item Not Found',
                    `You don't have any sellable item matching "${itemName}" in your inventory.`
                );
                return message.reply({ embeds: [embed] });
            }
            
            // If multiple matches, show options
            if (userItems.length > 1) {
                const embed = GameEmbedBuilder.createInfoEmbed(
                    'Multiple Items Found',
                    'Multiple items match your search. Please be more specific:'
                );
                
                let itemList = '';
                userItems.forEach((userItem, index) => {
                    const item = userItem.item;
                    const rarityInfo = item.getRarityInfo();
                    const sellPrice = userItem.getSellPrice();
                    const equipped = userItem.is_equipped ? ' ✅' : '';
                    const upgrade = userItem.upgrade_level > 0 ? ` +${userItem.upgrade_level}` : '';
                    
                    itemList += `**${index + 1}.** ${rarityInfo.emoji} ${item.name}${upgrade}${equipped}\n`;
                    itemList += `*Quantity: ${userItem.quantity} • Sell Price: ${sellPrice} 🪙*\n\n`;
                });
                
                embed.setDescription(itemList);
                embed.setFooter({ text: 'Use the exact item name to sell it' });
                
                return message.reply({ embeds: [embed] });
            }
            
            const userItem = userItems[0];
            const item = userItem.item;
            
            // Check if item is equipped
            if (userItem.is_equipped) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Cannot Sell Equipped Item',
                    `You cannot sell **${item.name}** while it's equipped!\nUse \`!unequip ${item.type}\` first.`
                );
                return message.reply({ embeds: [embed] });
            }
            
            // Check if user has enough quantity
            if (quantity > userItem.quantity) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Insufficient Quantity',
                    `You only have **${userItem.quantity}** of **${item.name}**.`
                );
                return message.reply({ embeds: [embed] });
            }
            
            // Calculate sell price
            const unitPrice = userItem.getSellPrice();
            const totalPrice = unitPrice * quantity;
            
            // Create confirmation embed
            const rarityInfo = item.getRarityInfo();
            const confirmEmbed = GameEmbedBuilder.createWarningEmbed(
                '💰 Confirm Sale',
                `Are you sure you want to sell **${quantity}x ${rarityInfo.emoji} ${item.name}**?`
            );
            
            confirmEmbed.addFields(
                {
                    name: '💵 Sale Details',
                    value: `**Unit Price:** ${unitPrice} 🪙\n**Total Price:** ${totalPrice} 🪙\n**Remaining:** ${userItem.quantity - quantity}`,
                    inline: true
                },
                {
                    name: '📊 Item Info',
                    value: `**Type:** ${item.type}\n**Rarity:** ${rarityInfo.name}\n**Upgrade:** +${userItem.upgrade_level}`,
                    inline: true
                }
            );
            
            confirmEmbed.setFooter({ text: 'React with ✅ to confirm or ❌ to cancel' });
            
            const confirmMessage = await message.reply({ embeds: [confirmEmbed] });
            await confirmMessage.react('✅');
            await confirmMessage.react('❌');
            
            // Wait for confirmation
            const filter = (reaction, user) => {
                return ['✅', '❌'].includes(reaction.emoji.name) && user.id === message.author.id;
            };
            
            const collector = confirmMessage.createReactionCollector({ filter, time: 30000, max: 1 });
            
            collector.on('collect', async (reaction) => {
                if (reaction.emoji.name === '❌') {
                    const cancelEmbed = GameEmbedBuilder.createInfoEmbed(
                        'Sale Cancelled',
                        'You decided not to sell the item.'
                    );
                    await confirmMessage.edit({ embeds: [cancelEmbed] });
                    return;
                }
                
                // Process the sale
                try {
                    // Update user item quantity
                    userItem.quantity -= quantity;
                    
                    // If quantity reaches 0, delete the user item
                    if (userItem.quantity <= 0) {
                        await userItem.destroy();
                    } else {
                        await userItem.save();
                    }
                    
                    // Add gold to character
                    character.gold += totalPrice;
                    await character.save();
                    
                    // Create transaction record
                    await Transaction.createSale(
                        user.id,
                        item.id,
                        totalPrice,
                        character.gold,
                        character.gems,
                        `Sold ${quantity}x ${item.name}`
                    );
                    
                    // Create success embed
                    const successEmbed = GameEmbedBuilder.createSuccessEmbed(
                        '💰 Item Sold!',
                        `Successfully sold **${quantity}x ${rarityInfo.emoji} ${item.name}**!`
                    );
                    
                    successEmbed.addFields(
                        {
                            name: '💵 Sale Summary',
                            value: `**Gold Earned:** ${totalPrice} 🪙\n**New Balance:** ${character.gold.toLocaleString()} 🪙`,
                            inline: true
                        }
                    );
                    
                    if (userItem.quantity > 0) {
                        successEmbed.addFields({
                            name: '📦 Remaining',
                            value: `You still have **${userItem.quantity}** of this item`,
                            inline: true
                        });
                    } else {
                        successEmbed.addFields({
                            name: '📦 Inventory',
                            value: 'This was your last copy of this item',
                            inline: true
                        });
                    }
                    
                    // Add upgrade bonus information if applicable
                    if (userItem.upgrade_level > 0) {
                        const basePrice = Math.floor(totalPrice / (1 + userItem.upgrade_level * 0.2));
                        const upgradeBonus = totalPrice - basePrice;
                        
                        successEmbed.addFields({
                            name: '⬆️ Upgrade Bonus',
                            value: `Base price: ${basePrice} 🪙\nUpgrade bonus: +${upgradeBonus} 🪙`,
                            inline: false
                        });
                    }
                    
                    successEmbed.setFooter({ text: 'Use !shop to buy new items or !inventory to see your remaining items' });
                    
                    await confirmMessage.edit({ embeds: [successEmbed] });
                    
                } catch (error) {
                    console.error('Error processing sale:', error);
                    const errorEmbed = GameEmbedBuilder.createErrorEmbed(
                        'Sale Error',
                        'An error occurred while processing the sale. Please try again.'
                    );
                    await confirmMessage.edit({ embeds: [errorEmbed] });
                }
            });
            
            collector.on('end', (collected) => {
                if (collected.size === 0) {
                    const timeoutEmbed = GameEmbedBuilder.createWarningEmbed(
                        'Sale Timeout',
                        'You took too long to confirm. Sale cancelled.'
                    );
                    confirmMessage.edit({ embeds: [timeoutEmbed] }).catch(console.error);
                }
                confirmMessage.reactions.removeAll().catch(console.error);
            });
            
        } catch (error) {
            console.error('Error in sell command:', error);
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Sell Error',
                'An error occurred while selling the item. Please try again.'
            );
            await message.reply({ embeds: [embed] });
        }
    }
};

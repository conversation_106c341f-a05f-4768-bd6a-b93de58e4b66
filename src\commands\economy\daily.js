const { User, Character, Transaction } = require('../../database/database');
const GameEmbedBuilder = require('../../utils/embedBuilder');

module.exports = {
    data: {
        name: 'daily',
        aliases: ['d', 'dailyreward', 'claim'],
        description: 'Claim your daily login rewards',
        usage: '!daily',
        cooldown: 5
    },
    async execute(message, args) {
        try {
            // Get user and character
            const user = await User.findByDiscordId(message.author.id);
            const character = await Character.findOne({
                where: { user_id: user.id }
            });
            
            // Check if daily reward is available
            if (character.isOnCooldown('daily')) {
                const remaining = character.getCooldownRemaining('daily');
                const hours = Math.floor(remaining / 3600000);
                const minutes = Math.floor((remaining % 3600000) / 60000);
                
                const embed = GameEmbedBuilder.createWarningEmbed(
                    '⏰ Daily Reward Not Ready',
                    `You can claim your next daily reward in **${hours}h ${minutes}m**.\n\nCome back tomorrow for your reward!`
                );
                
                // Show current streak
                if (character.daily_streak > 0) {
                    embed.addFields({
                        name: '🔥 Current Streak',
                        value: `**${character.daily_streak} days**\nKeep it up!`,
                        inline: true
                    });
                }
                
                return message.reply({ embeds: [embed] });
            }
            
            // Calculate streak
            const now = new Date();
            const lastDaily = character.last_daily ? new Date(character.last_daily) : null;
            let newStreak = 1;
            
            if (lastDaily) {
                const timeDiff = now - lastDaily;
                const daysDiff = Math.floor(timeDiff / (1000 * 60 * 60 * 24));
                
                if (daysDiff === 1) {
                    // Consecutive day - increase streak
                    newStreak = character.daily_streak + 1;
                } else if (daysDiff > 1) {
                    // Streak broken - reset to 1
                    newStreak = 1;
                } else {
                    // Same day - shouldn't happen due to cooldown check
                    newStreak = character.daily_streak;
                }
            }
            
            // Calculate rewards based on level, VIP, and streak
            const baseGold = 100 + (character.level * 10);
            const baseGems = Math.floor(character.level / 10) + 1;
            const baseExp = character.level * 5;
            
            // VIP bonuses
            const vipInfo = character.getVipBenefits();
            const vipGoldMultiplier = 1 + vipInfo.gold_bonus;
            const vipDailyGems = vipInfo.daily_gems || 0;
            
            // Streak bonuses (up to 7 days)
            const streakMultiplier = Math.min(1 + (newStreak - 1) * 0.1, 1.7); // Max 70% bonus at 7+ days
            
            // Calculate final rewards
            const goldReward = Math.floor(baseGold * vipGoldMultiplier * streakMultiplier);
            const gemReward = baseGems + vipDailyGems + Math.floor(newStreak / 7); // Extra gem every 7 days
            const expReward = Math.floor(baseExp * streakMultiplier);
            
            // Special milestone rewards
            let milestoneReward = null;
            if (newStreak === 7) {
                milestoneReward = { type: 'Weekly Bonus', gems: 10, description: '7-day streak bonus!' };
            } else if (newStreak === 30) {
                milestoneReward = { type: 'Monthly Bonus', gems: 50, description: '30-day streak bonus!' };
            } else if (newStreak === 100) {
                milestoneReward = { type: 'Legendary Streak', gems: 200, description: '100-day streak achievement!' };
            } else if (newStreak % 10 === 0 && newStreak >= 10) {
                milestoneReward = { type: 'Streak Milestone', gems: 5, description: `${newStreak}-day streak milestone!` };
            }
            
            // Apply rewards
            character.gold += goldReward;
            character.gems += gemReward;
            character.experience += expReward;
            character.daily_streak = newStreak;
            character.last_daily = now;
            
            // Add milestone bonus
            if (milestoneReward) {
                character.gems += milestoneReward.gems;
            }
            
            // Check for level up
            let leveledUp = false;
            while (character.canLevelUp()) {
                character.levelUp();
                leveledUp = true;
            }
            
            // Save character
            await character.save();
            
            // Create transaction record
            const totalGems = gemReward + (milestoneReward ? milestoneReward.gems : 0);
            await Transaction.createReward(
                user.id,
                goldReward,
                totalGems,
                character.gold,
                character.gems,
                `Daily reward (Day ${newStreak})`,
                'daily',
                null
            );
            
            // Create success embed
            const embed = GameEmbedBuilder.createSuccessEmbed(
                '🎁 Daily Reward Claimed!',
                `Welcome back! Here are your daily rewards:`
            );
            
            // Base rewards
            let rewardsText = '';
            rewardsText += `**Gold:** +${goldReward} 🪙\n`;
            rewardsText += `**Gems:** +${gemReward} 💎\n`;
            rewardsText += `**Experience:** +${expReward} ✨\n`;
            
            embed.addFields({
                name: '🎁 Base Rewards',
                value: rewardsText,
                inline: true
            });
            
            // Streak information
            let streakText = `**Current Streak:** ${newStreak} days\n`;
            if (streakMultiplier > 1) {
                streakText += `**Streak Bonus:** +${Math.round((streakMultiplier - 1) * 100)}%\n`;
            }
            streakText += `**Next Reward:** 24 hours`;
            
            embed.addFields({
                name: '🔥 Daily Streak',
                value: streakText,
                inline: true
            });
            
            // VIP bonuses
            if (character.vip_level > 0) {
                let vipText = `**${vipInfo.name}**\n`;
                if (vipInfo.gold_bonus > 0) {
                    vipText += `Gold Bonus: +${Math.round(vipInfo.gold_bonus * 100)}%\n`;
                }
                if (vipDailyGems > 0) {
                    vipText += `Daily Gems: +${vipDailyGems} 💎\n`;
                }
                
                embed.addFields({
                    name: '👑 VIP Benefits',
                    value: vipText,
                    inline: false
                });
            }
            
            // Milestone reward
            if (milestoneReward) {
                embed.addFields({
                    name: `🏆 ${milestoneReward.type}`,
                    value: `${milestoneReward.description}\n**Bonus:** +${milestoneReward.gems} 💎`,
                    inline: false
                });
            }
            
            // Level up message
            if (leveledUp) {
                embed.addFields({
                    name: '🎉 Level Up!',
                    value: `Congratulations! You are now **Level ${character.level}**!`,
                    inline: false
                });
            }
            
            // Current totals
            embed.addFields({
                name: '💰 Your Currency',
                value: `**Gold:** ${character.gold.toLocaleString()} 🪙\n**Gems:** ${character.gems.toLocaleString()} 💎`,
                inline: true
            });
            
            // Streak progress
            const nextMilestone = newStreak < 7 ? 7 : newStreak < 30 ? 30 : newStreak < 100 ? 100 : (Math.floor(newStreak / 10) + 1) * 10;
            const progressToNext = nextMilestone - newStreak;
            
            embed.addFields({
                name: '📈 Streak Progress',
                value: `**Next Milestone:** ${nextMilestone} days\n**Days to go:** ${progressToNext}`,
                inline: true
            });
            
            // Tips
            const tips = [
                'Come back every day to maintain your streak!',
                'Higher streaks give better rewards!',
                'VIP players get bonus daily gems!',
                'Use your gold to buy better equipment!',
                'Spend gems on premium items and gacha!'
            ];
            
            const randomTip = tips[Math.floor(Math.random() * tips.length)];
            embed.setFooter({ text: `💡 Tip: ${randomTip}` });
            
            // Set thumbnail based on streak
            if (newStreak >= 100) {
                embed.setColor('#FFD700'); // Gold for 100+ days
            } else if (newStreak >= 30) {
                embed.setColor('#C0C0C0'); // Silver for 30+ days
            } else if (newStreak >= 7) {
                embed.setColor('#CD7F32'); // Bronze for 7+ days
            }
            
            await message.reply({ embeds: [embed] });
            
        } catch (error) {
            console.error('Error in daily command:', error);
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Daily Reward Error',
                'An error occurred while claiming your daily reward. Please try again.'
            );
            await message.reply({ embeds: [embed] });
        }
    }
};

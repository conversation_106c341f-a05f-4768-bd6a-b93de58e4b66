const { Sequelize } = require('sequelize');
const logger = require('../utils/logger');

// Database configuration
const sequelize = process.env.DATABASE_URL
    ? new Sequelize(process.env.DATABASE_URL, {
        dialect: 'postgres',
        logging: (msg) => {
            if (process.env.NODE_ENV === 'development') {
                logger.debug('Database Query:', msg);
            }
        },
        pool: {
            max: 5,
            min: 0,
            acquire: 30000,
            idle: 10000
        }
    })
    : new Sequelize({
        dialect: 'sqlite',
        storage: './database.sqlite',
        logging: (msg) => {
            if (process.env.NODE_ENV === 'development') {
                logger.debug('Database Query:', msg);
            }
        },
    pool: {
        max: 10,
        min: 0,
        acquire: 30000,
        idle: 10000
    },
    define: {
        timestamps: true,
        underscored: true,
        freezeTableName: true
    }
});

// Import models
const User = require('./models/User')(sequelize);
const Character = require('./models/Character')(sequelize);
const Item = require('./models/Item')(sequelize);
const UserItem = require('./models/UserItem')(sequelize);
const Pet = require('./models/Pet')(sequelize);
const UserPet = require('./models/UserPet')(sequelize);
const Monster = require('./models/Monster')(sequelize);
const BattleLog = require('./models/BattleLog')(sequelize);
// const Shop = require('./models/Shop')(sequelize); // Removed - using Item model directly
const VipLevel = require('./models/VipLevel')(sequelize);
const Transaction = require('./models/Transaction')(sequelize);

// Define associations
User.hasOne(Character, { foreignKey: 'user_id', as: 'character' });
Character.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

User.hasMany(UserItem, { foreignKey: 'user_id', as: 'userItems' });
UserItem.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
UserItem.belongsTo(Item, { foreignKey: 'item_id', as: 'item' });
Item.hasMany(UserItem, { foreignKey: 'item_id', as: 'userItems' });

User.hasMany(UserPet, { foreignKey: 'user_id', as: 'userPets' });
UserPet.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
UserPet.belongsTo(Pet, { foreignKey: 'pet_id', as: 'pet' });
Pet.hasMany(UserPet, { foreignKey: 'pet_id', as: 'userPets' });

User.hasMany(BattleLog, { foreignKey: 'user_id', as: 'battleLogs' });
BattleLog.belongsTo(User, { foreignKey: 'user_id', as: 'user' });
BattleLog.belongsTo(Monster, { foreignKey: 'monster_id', as: 'monster' });

User.hasMany(Transaction, { foreignKey: 'user_id', as: 'transactions' });
Transaction.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

Character.belongsTo(VipLevel, {
    foreignKey: 'vip_level',
    targetKey: 'level', // Reference to level field instead of id
    as: 'vip'
});

// Equipment associations
Character.belongsTo(Item, { foreignKey: 'weapon_id', as: 'weapon' });
Character.belongsTo(Item, { foreignKey: 'armor_id', as: 'armor' });
Character.belongsTo(Item, { foreignKey: 'helmet_id', as: 'helmet' });
Character.belongsTo(Item, { foreignKey: 'boots_id', as: 'boots' });
Character.belongsTo(Item, { foreignKey: 'ring_id', as: 'ring' });
Character.belongsTo(Item, { foreignKey: 'necklace_id', as: 'necklace' });

module.exports = {
    sequelize,
    User,
    Character,
    Item,
    UserItem,
    Pet,
    UserPet,
    Monster,
    BattleLog,
    // Shop, // Removed
    VipLevel,
    Transaction
};

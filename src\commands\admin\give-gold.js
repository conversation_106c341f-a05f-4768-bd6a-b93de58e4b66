const { SlashCommandBuilder } = require('discord.js');
const { User, Character, Transaction } = require('../../database/database');
const GameEmbedBuilder = require('../../utils/embedBuilder');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('admin-give-gold')
        .setDescription('Give gold to a user (Admin only)')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('The user to give gold to')
                .setRequired(true))
        .addIntegerOption(option =>
            option.setName('amount')
                .setDescription('Amount of gold to give')
                .setRequired(true)
                .setMinValue(1)
                .setMaxValue(1000000))
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Reason for giving gold')
                .setRequired(false)),
    
    async execute(interaction) {
        try {
            // Check if user is admin
            if (!this.isAdmin(interaction.user.id)) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    '🚫 Access Denied',
                    'You do not have permission to use this command.'
                );
                return interaction.reply({ embeds: [embed], ephemeral: true });
            }
            
            const targetUser = interaction.options.getUser('user');
            const amount = interaction.options.getInteger('amount');
            const reason = interaction.options.getString('reason') || 'Admin gift';
            
            // Get target user from database
            const user = await User.findByDiscordId(targetUser.id);
            if (!user) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'User Not Found',
                    `${targetUser.username} hasn't started playing yet!`
                );
                return interaction.reply({ embeds: [embed], ephemeral: true });
            }
            
            // Get character
            const character = await Character.findOne({
                where: { user_id: user.id }
            });
            
            if (!character) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Character Not Found',
                    'Character data not found for this user.'
                );
                return interaction.reply({ embeds: [embed], ephemeral: true });
            }
            
            // Add gold
            const oldGold = character.gold;
            character.gold += amount;
            await character.save();
            
            // Create transaction record
            await Transaction.create({
                user_id: user.id,
                type: 'admin',
                gold_change: amount,
                gems_change: 0,
                gold_balance_after: character.gold,
                gems_balance_after: character.gems,
                description: `Admin gave ${amount} gold: ${reason}`,
                admin_id: interaction.user.id,
                admin_reason: reason
            });
            
            // Create success embed
            const embed = GameEmbedBuilder.createSuccessEmbed(
                '💰 Gold Given Successfully',
                `Successfully gave **${amount.toLocaleString()} gold** to ${targetUser.username}!`
            );
            
            embed.addFields(
                {
                    name: '📊 Transaction Details',
                    value: `**Recipient:** ${targetUser.username}\n**Amount:** ${amount.toLocaleString()} 🪙\n**Reason:** ${reason}`,
                    inline: true
                },
                {
                    name: '💳 Balance Update',
                    value: `**Previous:** ${oldGold.toLocaleString()} 🪙\n**New:** ${character.gold.toLocaleString()} 🪙\n**Change:** +${amount.toLocaleString()} 🪙`,
                    inline: true
                }
            );
            
            embed.addFields({
                name: '👑 Admin Info',
                value: `**Admin:** ${interaction.user.username}\n**Timestamp:** <t:${Math.floor(Date.now() / 1000)}:F>`,
                inline: false
            });
            
            embed.setFooter({ text: 'This action has been logged for audit purposes.' });
            
            await interaction.reply({ embeds: [embed] });
            
            // Try to notify the user (if they're in the same server)
            try {
                const notifyEmbed = GameEmbedBuilder.createSuccessEmbed(
                    '🎁 You Received Gold!',
                    `An admin has given you **${amount.toLocaleString()} gold**!`
                );
                
                notifyEmbed.addFields({
                    name: '📋 Details',
                    value: `**Amount:** ${amount.toLocaleString()} 🪙\n**Reason:** ${reason}\n**New Balance:** ${character.gold.toLocaleString()} 🪙`,
                    inline: false
                });
                
                await targetUser.send({ embeds: [notifyEmbed] });
            } catch (error) {
                // User might have DMs disabled, that's okay
                console.log(`Could not notify user ${targetUser.username} about gold gift:`, error.message);
            }
            
        } catch (error) {
            console.error('Error in admin-give-gold command:', error);
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Command Error',
                'An error occurred while giving gold. Please try again.'
            );
            await interaction.reply({ embeds: [embed], ephemeral: true });
        }
    },
    
    isAdmin(userId) {
        // Define admin user IDs here
        const adminIds = process.env.ADMIN_IDS ? process.env.ADMIN_IDS.split(',') : [];
        return adminIds.includes(userId);
    }
};

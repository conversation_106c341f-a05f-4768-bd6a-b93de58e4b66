const { performance } = require('perf_hooks');

class PerformanceMonitor {
    constructor() {
        this.metrics = new Map();
        this.activeTimers = new Map();
    }

    // Start timing an operation
    startTimer(name) {
        this.activeTimers.set(name, performance.now());
    }

    // End timing and record metric
    endTimer(name) {
        const startTime = this.activeTimers.get(name);
        if (!startTime) {
            console.warn(`Timer '${name}' was not started`);
            return 0;
        }

        const duration = performance.now() - startTime;
        this.activeTimers.delete(name);
        
        this.recordMetric(name, duration);
        return duration;
    }

    // Record a metric value
    recordMetric(name, value) {
        if (!this.metrics.has(name)) {
            this.metrics.set(name, {
                count: 0,
                total: 0,
                min: Infinity,
                max: -Infinity,
                values: []
            });
        }

        const metric = this.metrics.get(name);
        metric.count++;
        metric.total += value;
        metric.min = Math.min(metric.min, value);
        metric.max = Math.max(metric.max, value);
        
        // Keep last 100 values for percentile calculations
        metric.values.push(value);
        if (metric.values.length > 100) {
            metric.values.shift();
        }
    }

    // Get metric statistics
    getMetric(name) {
        const metric = this.metrics.get(name);
        if (!metric) return null;

        const average = metric.total / metric.count;
        const sorted = [...metric.values].sort((a, b) => a - b);
        const p95Index = Math.floor(sorted.length * 0.95);
        const p99Index = Math.floor(sorted.length * 0.99);

        return {
            count: metric.count,
            average: Math.round(average * 100) / 100,
            min: Math.round(metric.min * 100) / 100,
            max: Math.round(metric.max * 100) / 100,
            p95: sorted[p95Index] ? Math.round(sorted[p95Index] * 100) / 100 : 0,
            p99: sorted[p99Index] ? Math.round(sorted[p99Index] * 100) / 100 : 0
        };
    }

    // Get all metrics
    getAllMetrics() {
        const result = {};
        for (const [name] of this.metrics) {
            result[name] = this.getMetric(name);
        }
        return result;
    }

    // Clear all metrics
    clearMetrics() {
        this.metrics.clear();
        this.activeTimers.clear();
    }

    // Memory usage monitoring
    getMemoryUsage() {
        const usage = process.memoryUsage();
        return {
            rss: Math.round(usage.rss / 1024 / 1024 * 100) / 100, // MB
            heapTotal: Math.round(usage.heapTotal / 1024 / 1024 * 100) / 100, // MB
            heapUsed: Math.round(usage.heapUsed / 1024 / 1024 * 100) / 100, // MB
            external: Math.round(usage.external / 1024 / 1024 * 100) / 100, // MB
            arrayBuffers: Math.round(usage.arrayBuffers / 1024 / 1024 * 100) / 100 // MB
        };
    }

    // Database query optimization
    async optimizeQuery(queryFn, queryName) {
        this.startTimer(`db_${queryName}`);
        
        try {
            const result = await queryFn();
            this.endTimer(`db_${queryName}`);
            return result;
        } catch (error) {
            this.endTimer(`db_${queryName}`);
            this.recordMetric(`db_${queryName}_errors`, 1);
            throw error;
        }
    }

    // Command execution monitoring
    async monitorCommand(commandFn, commandName) {
        this.startTimer(`cmd_${commandName}`);
        
        try {
            const result = await commandFn();
            this.endTimer(`cmd_${commandName}`);
            this.recordMetric(`cmd_${commandName}_success`, 1);
            return result;
        } catch (error) {
            this.endTimer(`cmd_${commandName}`);
            this.recordMetric(`cmd_${commandName}_errors`, 1);
            throw error;
        }
    }

    // Generate performance report
    generateReport() {
        const metrics = this.getAllMetrics();
        const memory = this.getMemoryUsage();
        
        let report = '📊 Performance Report\n';
        report += '===================\n\n';
        
        report += `💾 Memory Usage:\n`;
        report += `  RSS: ${memory.rss} MB\n`;
        report += `  Heap Used: ${memory.heapUsed} MB\n`;
        report += `  Heap Total: ${memory.heapTotal} MB\n\n`;
        
        if (Object.keys(metrics).length > 0) {
            report += `⏱️ Timing Metrics:\n`;
            
            // Group metrics by type
            const commandMetrics = {};
            const dbMetrics = {};
            const otherMetrics = {};
            
            for (const [name, metric] of Object.entries(metrics)) {
                if (name.startsWith('cmd_')) {
                    commandMetrics[name] = metric;
                } else if (name.startsWith('db_')) {
                    dbMetrics[name] = metric;
                } else {
                    otherMetrics[name] = metric;
                }
            }
            
            // Commands
            if (Object.keys(commandMetrics).length > 0) {
                report += `\n🎮 Commands:\n`;
                for (const [name, metric] of Object.entries(commandMetrics)) {
                    const cleanName = name.replace('cmd_', '');
                    report += `  ${cleanName}: ${metric.average}ms avg (${metric.count} calls)\n`;
                }
            }
            
            // Database
            if (Object.keys(dbMetrics).length > 0) {
                report += `\n🗄️ Database:\n`;
                for (const [name, metric] of Object.entries(dbMetrics)) {
                    const cleanName = name.replace('db_', '');
                    report += `  ${cleanName}: ${metric.average}ms avg (${metric.count} calls)\n`;
                }
            }
            
            // Other
            if (Object.keys(otherMetrics).length > 0) {
                report += `\n🔧 Other:\n`;
                for (const [name, metric] of Object.entries(otherMetrics)) {
                    report += `  ${name}: ${metric.average}ms avg (${metric.count} calls)\n`;
                }
            }
        }
        
        return report;
    }

    // Log slow operations
    logSlowOperation(name, duration, threshold = 1000) {
        if (duration > threshold) {
            console.warn(`🐌 Slow operation detected: ${name} took ${duration}ms`);
        }
    }

    // Middleware for automatic timing
    createTimingMiddleware(name) {
        return async (fn, ...args) => {
            this.startTimer(name);
            try {
                const result = await fn(...args);
                const duration = this.endTimer(name);
                this.logSlowOperation(name, duration);
                return result;
            } catch (error) {
                this.endTimer(name);
                throw error;
            }
        };
    }
}

// Cache implementation for frequently accessed data
class SimpleCache {
    constructor(maxSize = 1000, ttl = 300000) { // 5 minutes default TTL
        this.cache = new Map();
        this.maxSize = maxSize;
        this.ttl = ttl;
    }

    set(key, value) {
        // Remove oldest entries if cache is full
        if (this.cache.size >= this.maxSize) {
            const firstKey = this.cache.keys().next().value;
            this.cache.delete(firstKey);
        }

        this.cache.set(key, {
            value,
            timestamp: Date.now()
        });
    }

    get(key) {
        const entry = this.cache.get(key);
        if (!entry) return null;

        // Check if entry has expired
        if (Date.now() - entry.timestamp > this.ttl) {
            this.cache.delete(key);
            return null;
        }

        return entry.value;
    }

    has(key) {
        return this.get(key) !== null;
    }

    delete(key) {
        return this.cache.delete(key);
    }

    clear() {
        this.cache.clear();
    }

    size() {
        return this.cache.size;
    }

    // Clean expired entries
    cleanup() {
        const now = Date.now();
        for (const [key, entry] of this.cache.entries()) {
            if (now - entry.timestamp > this.ttl) {
                this.cache.delete(key);
            }
        }
    }
}

// Rate limiter for API calls
class RateLimiter {
    constructor(maxRequests = 100, windowMs = 60000) { // 100 requests per minute
        this.maxRequests = maxRequests;
        this.windowMs = windowMs;
        this.requests = new Map();
    }

    isAllowed(identifier) {
        const now = Date.now();
        const windowStart = now - this.windowMs;

        // Get or create request history for this identifier
        if (!this.requests.has(identifier)) {
            this.requests.set(identifier, []);
        }

        const requestHistory = this.requests.get(identifier);

        // Remove old requests outside the window
        while (requestHistory.length > 0 && requestHistory[0] < windowStart) {
            requestHistory.shift();
        }

        // Check if under limit
        if (requestHistory.length < this.maxRequests) {
            requestHistory.push(now);
            return true;
        }

        return false;
    }

    getRemainingRequests(identifier) {
        const now = Date.now();
        const windowStart = now - this.windowMs;

        if (!this.requests.has(identifier)) {
            return this.maxRequests;
        }

        const requestHistory = this.requests.get(identifier);
        const validRequests = requestHistory.filter(timestamp => timestamp > windowStart);
        
        return Math.max(0, this.maxRequests - validRequests.length);
    }

    getResetTime(identifier) {
        if (!this.requests.has(identifier)) {
            return 0;
        }

        const requestHistory = this.requests.get(identifier);
        if (requestHistory.length === 0) {
            return 0;
        }

        return requestHistory[0] + this.windowMs;
    }

    cleanup() {
        const now = Date.now();
        const windowStart = now - this.windowMs;

        for (const [identifier, requestHistory] of this.requests.entries()) {
            // Remove old requests
            while (requestHistory.length > 0 && requestHistory[0] < windowStart) {
                requestHistory.shift();
            }

            // Remove empty histories
            if (requestHistory.length === 0) {
                this.requests.delete(identifier);
            }
        }
    }
}

// Global instances
const performanceMonitor = new PerformanceMonitor();
const gameCache = new SimpleCache(5000, 600000); // 10 minutes TTL
const commandRateLimiter = new RateLimiter(30, 60000); // 30 commands per minute

// Cleanup interval
setInterval(() => {
    gameCache.cleanup();
    commandRateLimiter.cleanup();
}, 60000); // Clean up every minute

module.exports = {
    PerformanceMonitor,
    SimpleCache,
    RateLimiter,
    performanceMonitor,
    gameCache,
    commandRateLimiter
};

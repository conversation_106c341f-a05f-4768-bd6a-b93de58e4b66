// Jest setup file
require('dotenv').config({ path: '.env.test' });

// Mock Discord.js
jest.mock('discord.js', () => ({
    Client: jest.fn().mockImplementation(() => ({
        login: jest.fn().mockResolvedValue(true),
        user: {
            tag: 'TestBot#0001',
            setActivity: jest.fn()
        },
        guilds: {
            cache: {
                size: 1
            }
        },
        on: jest.fn(),
        once: jest.fn(),
        destroy: jest.fn()
    })),
    GatewayIntentBits: {
        Guilds: 1,
        GuildMessages: 2,
        MessageContent: 4,
        GuildMembers: 8
    },
    Collection: Map,
    REST: jest.fn().mockImplementation(() => ({
        setToken: jest.fn().mockReturnThis(),
        put: jest.fn().mockResolvedValue([])
    })),
    Routes: {
        applicationCommands: jest.fn().mockReturnValue('test-route')
    },
    SlashCommandBuilder: jest.fn().mockImplementation(() => ({
        setName: jest.fn().mockReturnThis(),
        setDescription: jest.fn().mockReturnThis(),
        addUserOption: jest.fn().mockReturnThis(),
        addIntegerOption: jest.fn().mockReturnThis(),
        addStringOption: jest.fn().mockReturnThis(),
        toJSON: jest.fn().mockReturnValue({})
    })),
    EmbedBuilder: jest.fn().mockImplementation(() => ({
        setTitle: jest.fn().mockReturnThis(),
        setDescription: jest.fn().mockReturnThis(),
        setColor: jest.fn().mockReturnThis(),
        addFields: jest.fn().mockReturnThis(),
        setFooter: jest.fn().mockReturnThis(),
        setThumbnail: jest.fn().mockReturnThis(),
        setTimestamp: jest.fn().mockReturnThis(),
        toJSON: jest.fn().mockReturnValue({})
    }))
}));

// Set test environment variables
process.env.NODE_ENV = 'test';
process.env.DATABASE_URL = process.env.TEST_DATABASE_URL || 'postgresql://test:test@localhost:5432/afk_game_bot_test';
process.env.DISCORD_TOKEN = 'test-token';
process.env.CLIENT_ID = 'test-client-id';
process.env.PREFIX = '!';
process.env.ADMIN_IDS = '123456789';

// Global test timeout
jest.setTimeout(30000);

// Console suppression for cleaner test output
const originalConsoleError = console.error;
const originalConsoleWarn = console.warn;
const originalConsoleLog = console.log;

beforeAll(() => {
    console.error = jest.fn();
    console.warn = jest.fn();
    console.log = jest.fn();
});

afterAll(() => {
    console.error = originalConsoleError;
    console.warn = originalConsoleWarn;
    console.log = originalConsoleLog;
});

// Database cleanup helpers
global.cleanupDatabase = async () => {
    const { sequelize } = require('../src/database/database');
    
    try {
        // Drop all tables
        await sequelize.drop();
        
        // Recreate tables
        await sequelize.sync({ force: true });
    } catch (error) {
        console.error('Database cleanup failed:', error);
    }
};

// Mock embed builder for tests
global.mockEmbedBuilder = {
    createSuccessEmbed: jest.fn().mockReturnValue({
        data: { title: 'Success', color: 0x00FF00 }
    }),
    createErrorEmbed: jest.fn().mockReturnValue({
        data: { title: 'Error', color: 0xFF0000 }
    }),
    createWarningEmbed: jest.fn().mockReturnValue({
        data: { title: 'Warning', color: 0xFFFF00 }
    }),
    createInfoEmbed: jest.fn().mockReturnValue({
        data: { title: 'Info', color: 0x0099FF }
    }),
    createCombatResultEmbed: jest.fn().mockReturnValue({
        data: { title: 'Combat Result', color: 0x00FF00 }
    }),
    createShopEmbed: jest.fn().mockReturnValue({
        data: { title: 'Shop', color: 0x9932CC }
    }),
    createCooldownEmbed: jest.fn().mockReturnValue({
        data: { title: 'Cooldown', color: 0xFFFF00 }
    })
};

// Mock reaction collector
global.mockReactionCollector = {
    on: jest.fn(),
    once: jest.fn()
};

// Helper to create mock Discord message
global.createMockMessage = (authorId = '123456789', content = '!test') => ({
    author: {
        id: authorId,
        username: 'testuser',
        discriminator: '0001',
        displayAvatarURL: () => 'https://example.com/avatar.png',
        bot: false
    },
    content,
    guild: {
        id: 'test-guild-id',
        name: 'Test Guild'
    },
    channel: {
        id: 'test-channel-id',
        send: jest.fn().mockResolvedValue({
            id: 'test-message-id',
            react: jest.fn().mockResolvedValue(true),
            edit: jest.fn().mockResolvedValue(true),
            delete: jest.fn().mockResolvedValue(true),
            createReactionCollector: jest.fn().mockReturnValue(mockReactionCollector)
        })
    },
    reply: jest.fn().mockResolvedValue({
        id: 'test-reply-id',
        react: jest.fn().mockResolvedValue(true),
        edit: jest.fn().mockResolvedValue(true),
        delete: jest.fn().mockResolvedValue(true),
        createReactionCollector: jest.fn().mockReturnValue(mockReactionCollector)
    }),
    react: jest.fn().mockResolvedValue(true),
    edit: jest.fn().mockResolvedValue(true),
    delete: jest.fn().mockResolvedValue(true),
    createReactionCollector: jest.fn().mockReturnValue(mockReactionCollector)
});

// Helper to create mock Discord interaction
global.createMockInteraction = (userId = '123456789', commandName = 'test') => ({
    user: {
        id: userId,
        username: 'testuser',
        discriminator: '0001',
        displayAvatarURL: () => 'https://example.com/avatar.png'
    },
    commandName,
    options: {
        getUser: jest.fn(),
        getInteger: jest.fn(),
        getString: jest.fn(),
        getBoolean: jest.fn()
    },
    guild: {
        id: 'test-guild-id',
        name: 'Test Guild'
    },
    channel: {
        id: 'test-channel-id'
    },
    reply: jest.fn().mockResolvedValue(true),
    followUp: jest.fn().mockResolvedValue(true),
    editReply: jest.fn().mockResolvedValue(true),
    deferReply: jest.fn().mockResolvedValue(true),
    replied: false,
    deferred: false,
    isChatInputCommand: () => true
});

// Error handling for unhandled promise rejections in tests
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
});

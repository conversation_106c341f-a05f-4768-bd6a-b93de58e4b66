const CombatEngine = require('../../src/utils/combatEngine');
const { Character, Monster, User } = require('../../src/database/database');
const { sequelize } = require('../../src/database/database');

describe('Combat Engine', () => {
    let testUser;
    let testCharacter;
    let testMonster;

    beforeAll(async () => {
        await sequelize.sync({ force: true });
    });

    beforeEach(async () => {
        // Create test user and character
        testUser = await User.create({
            discord_id: '123456789',
            username: 'testuser',
            discriminator: '0001'
        });

        testCharacter = await Character.create({
            user_id: testUser.id,
            level: 10,
            experience: 0,
            health: 150,
            max_health: 150,
            attack: 25,
            defense: 10,
            crit_rate: 10.0,
            crit_damage: 150.0,
            gold: 100,
            gems: 0
        });

        // Create test monster
        testMonster = await <PERSON>.create({
            name: 'Test Goblin',
            type: 'normal',
            level: 8,
            health: 80,
            attack: 15,
            defense: 5,
            base_exp: 50,
            base_gold: 25,
            is_active: true
        });
    });

    afterEach(async () => {
        await Monster.destroy({ where: {} });
        await Character.destroy({ where: {} });
        await User.destroy({ where: {} });
    });

    afterAll(async () => {
        await sequelize.close();
    });

    describe('Damage Calculation', () => {
        test('should calculate basic damage correctly', () => {
            const damage = CombatEngine.calculateDamage(25, 5, false);
            expect(damage).toBeGreaterThan(0);
            expect(damage).toBeLessThanOrEqual(25); // Should not exceed attack
        });

        test('should handle critical hits', () => {
            const normalDamage = CombatEngine.calculateDamage(25, 5, false);
            const critDamage = CombatEngine.calculateDamage(25, 5, true, 200.0);
            
            expect(critDamage).toBeGreaterThan(normalDamage);
        });

        test('should handle high defense', () => {
            const damage = CombatEngine.calculateDamage(10, 20, false);
            expect(damage).toBe(1); // Minimum damage should be 1
        });

        test('should apply damage variance', () => {
            const damages = [];
            for (let i = 0; i < 10; i++) {
                damages.push(CombatEngine.calculateDamage(20, 0, false));
            }
            
            // Should have some variance in damage values
            const uniqueDamages = [...new Set(damages)];
            expect(uniqueDamages.length).toBeGreaterThan(1);
        });
    });

    describe('Critical Hit System', () => {
        test('should determine critical hits based on rate', () => {
            let critCount = 0;
            const trials = 1000;
            
            for (let i = 0; i < trials; i++) {
                if (CombatEngine.isCriticalHit(10.0)) {
                    critCount++;
                }
            }
            
            // Should be approximately 10% (within reasonable margin)
            const critRate = (critCount / trials) * 100;
            expect(critRate).toBeGreaterThan(5);
            expect(critRate).toBeLessThan(15);
        });

        test('should handle 0% crit rate', () => {
            for (let i = 0; i < 100; i++) {
                expect(CombatEngine.isCriticalHit(0)).toBe(false);
            }
        });

        test('should handle 100% crit rate', () => {
            for (let i = 0; i < 100; i++) {
                expect(CombatEngine.isCriticalHit(100)).toBe(true);
            }
        });
    });

    describe('Battle Simulation', () => {
        test('should simulate a complete battle', async () => {
            const result = await CombatEngine.simulateBattle(testCharacter, testMonster, 'fight');
            
            expect(result).toHaveProperty('result');
            expect(result).toHaveProperty('turns');
            expect(result).toHaveProperty('damageDealt');
            expect(result).toHaveProperty('damageReceived');
            expect(result).toHaveProperty('rewards');
            
            expect(['victory', 'defeat'].includes(result.result)).toBe(true);
            expect(result.turns).toBeGreaterThan(0);
        });

        test('should handle character victory', async () => {
            // Make character much stronger
            testCharacter.attack = 100;
            testCharacter.defense = 50;
            
            const result = await CombatEngine.simulateBattle(testCharacter, testMonster, 'fight');
            
            expect(result.result).toBe('victory');
            expect(result.rewards.exp).toBeGreaterThan(0);
            expect(result.rewards.gold).toBeGreaterThan(0);
        });

        test('should handle character defeat', async () => {
            // Make monster much stronger
            testMonster.attack = 100;
            testMonster.health = 500;
            
            const result = await CombatEngine.simulateBattle(testCharacter, testMonster, 'fight');
            
            expect(result.result).toBe('defeat');
            expect(result.rewards.exp).toBe(0);
            expect(result.rewards.gold).toBe(0);
        });

        test('should prevent infinite battles', async () => {
            // Create very defensive characters
            testCharacter.attack = 1;
            testCharacter.defense = 100;
            testMonster.attack = 1;
            testMonster.defense = 100;
            
            const result = await CombatEngine.simulateBattle(testCharacter, testMonster, 'fight');
            
            expect(result.turns).toBeLessThanOrEqual(100); // Max turns limit
        });
    });

    describe('Reward Calculation', () => {
        test('should calculate rewards based on monster level', () => {
            const rewards = CombatEngine.calculateRewards(testMonster, testCharacter.level, 0);
            
            expect(rewards.exp).toBeGreaterThan(0);
            expect(rewards.gold).toBeGreaterThan(0);
            expect(rewards.gems).toBeGreaterThanOrEqual(0);
        });

        test('should apply VIP bonuses', () => {
            const normalRewards = CombatEngine.calculateRewards(testMonster, testCharacter.level, 0);
            const vipRewards = CombatEngine.calculateRewards(testMonster, testCharacter.level, 3);
            
            expect(vipRewards.gold).toBeGreaterThan(normalRewards.gold);
        });

        test('should scale with level difference', () => {
            const lowLevelRewards = CombatEngine.calculateRewards(testMonster, 1, 0);
            const highLevelRewards = CombatEngine.calculateRewards(testMonster, 20, 0);
            
            // Higher level characters should get different rewards
            expect(lowLevelRewards.exp).not.toBe(highLevelRewards.exp);
        });
    });

    describe('VIP Cooldown Reduction', () => {
        test('should calculate VIP cooldown reduction', () => {
            expect(CombatEngine.calculateVipCooldownReduction(0)).toBe(0);
            expect(CombatEngine.calculateVipCooldownReduction(1)).toBe(0.25);
            expect(CombatEngine.calculateVipCooldownReduction(3)).toBe(0.45);
            expect(CombatEngine.calculateVipCooldownReduction(5)).toBe(0.65);
        });

        test('should handle invalid VIP levels', () => {
            expect(CombatEngine.calculateVipCooldownReduction(-1)).toBe(0);
            expect(CombatEngine.calculateVipCooldownReduction(10)).toBe(0.65); // Max reduction
        });
    });

    describe('Battle Rewards Application', () => {
        test('should apply rewards to character', async () => {
            const rewards = {
                exp: 100,
                gold: 50,
                gems: 2
            };
            
            const oldExp = testCharacter.experience;
            const oldGold = testCharacter.gold;
            const oldGems = testCharacter.gems;
            
            const result = await CombatEngine.applyBattleRewards(testCharacter, rewards, []);
            
            expect(testCharacter.experience).toBe(oldExp + rewards.exp);
            expect(testCharacter.gold).toBe(oldGold + rewards.gold);
            expect(testCharacter.gems).toBe(oldGems + rewards.gems);
        });

        test('should handle level ups during reward application', async () => {
            testCharacter.experience = 950; // Close to level up (needs 1000 for level 2)
            
            const rewards = {
                exp: 100,
                gold: 50,
                gems: 0
            };
            
            const oldLevel = testCharacter.level;
            const result = await CombatEngine.applyBattleRewards(testCharacter, rewards, []);
            
            expect(result.leveledUp).toBe(true);
            expect(testCharacter.level).toBeGreaterThan(oldLevel);
        });
    });

    describe('Error Handling', () => {
        test('should handle null character', async () => {
            await expect(CombatEngine.simulateBattle(null, testMonster, 'fight'))
                .rejects.toThrow();
        });

        test('should handle null monster', async () => {
            await expect(CombatEngine.simulateBattle(testCharacter, null, 'fight'))
                .rejects.toThrow();
        });

        test('should handle invalid battle type', async () => {
            const result = await CombatEngine.simulateBattle(testCharacter, testMonster, 'invalid');
            expect(result).toBeDefined(); // Should still work with default handling
        });
    });
});

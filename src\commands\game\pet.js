const { User, Character, Pet, UserPet } = require('../../database/database');
const GameEmbedBuilder = require('../../utils/embedBuilder');

module.exports = {
    data: {
        name: 'pet',
        aliases: ['pets', 'companion'],
        description: 'Manage your pets - view, feed, play, and evolve',
        usage: '!pet [list|active|feed|play|evolve|activate] [pet_name]',
        cooldown: 5
    },
    async execute(message, args) {
        try {
            // Get user and character
            const user = await User.findByDiscordId(message.author.id);
            const character = await Character.findOne({
                where: { user_id: user.id }
            });
            
            // Parse arguments
            let action = 'list';
            let petName = null;
            
            if (args.length > 0) {
                const firstArg = args[0].toLowerCase();
                if (['list', 'active', 'feed', 'play', 'evolve', 'activate', 'status'].includes(firstArg)) {
                    action = firstArg;
                    if (args.length > 1) {
                        petName = args.slice(1).join(' ').toLowerCase();
                    }
                } else {
                    // First argument might be pet name
                    petName = args.join(' ').toLowerCase();
                    action = 'status';
                }
            }
            
            // Handle different actions
            switch (action) {
                case 'list':
                    await this.handleListPets(message, user);
                    break;
                case 'active':
                    await this.handleActivePet(message, user);
                    break;
                case 'feed':
                    await this.handleFeedPet(message, user, petName);
                    break;
                case 'play':
                    await this.handlePlayWithPet(message, user, petName);
                    break;
                case 'evolve':
                    await this.handleEvolvePet(message, user, petName);
                    break;
                case 'activate':
                    await this.handleActivatePet(message, user, petName);
                    break;
                case 'status':
                    await this.handlePetStatus(message, user, petName);
                    break;
                default:
                    await this.handleListPets(message, user);
            }
            
        } catch (error) {
            console.error('Error in pet command:', error);
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Pet System Error',
                'An error occurred while processing your pet request. Please try again.'
            );
            await message.reply({ embeds: [embed] });
        }
    },
    
    async handleListPets(message, user) {
        const userPets = await UserPet.findAll({
            where: { user_id: user.id },
            include: [{
                model: Pet,
                as: 'pet'
            }],
            order: [
                ['is_active', 'DESC'],
                ['level', 'DESC'],
                ['acquired_at', 'ASC']
            ]
        });
        
        if (userPets.length === 0) {
            const embed = GameEmbedBuilder.createInfoEmbed(
                '🐾 No Pets Found',
                'You don\'t have any pets yet!\n\nPets can be obtained through:\n• Gacha rolls\n• Special events\n• Shop purchases\n• Quest rewards'
            );
            
            embed.addFields({
                name: '🎰 Getting Your First Pet',
                value: 'Try using `!gacha` to roll for pets!\nSome pets are also available in the `!shop`.',
                inline: false
            });
            
            return message.reply({ embeds: [embed] });
        }
        
        const embed = GameEmbedBuilder.createInfoEmbed(
            '🐾 Your Pet Collection',
            `You have ${userPets.length} pet${userPets.length > 1 ? 's' : ''}:`
        );
        
        let petsText = '';
        userPets.forEach((userPet, index) => {
            const pet = userPet.pet;
            const rarityInfo = pet.getRarityInfo();
            const typeInfo = pet.getTypeInfo();
            const active = userPet.is_active ? ' ⭐' : '';
            const nickname = userPet.nickname ? ` "${userPet.nickname}"` : '';
            
            petsText += `**${index + 1}.** ${rarityInfo.emoji} ${pet.name}${nickname}${active}\n`;
            petsText += `*Level ${userPet.level} ${typeInfo.name} • ${rarityInfo.name}*\n`;
            
            // Show condition status
            const condition = userPet.getConditionStatus();
            petsText += `${condition.happiness.emoji} ${condition.hunger.emoji} ${condition.health.emoji} `;
            
            // Show battle effectiveness
            const effectiveness = userPet.getBattleEffectiveness();
            petsText += `(${effectiveness.description})\n\n`;
        });
        
        embed.setDescription(petsText);
        
        // Show active pet summary
        const activePet = userPets.find(pet => pet.is_active);
        if (activePet) {
            const stats = activePet.getCurrentStats();
            embed.addFields({
                name: '⭐ Active Pet',
                value: `**${activePet.getFormattedName()}**\nATK: ${stats.attack} • DEF: ${stats.defense} • HP: ${stats.health}`,
                inline: true
            });
        }
        
        embed.addFields({
            name: '📋 Pet Commands',
            value: [
                '`!pet active` - View active pet details',
                '`!pet feed <name>` - Feed a pet',
                '`!pet play <name>` - Play with a pet',
                '`!pet activate <name>` - Set active pet'
            ].join('\n'),
            inline: false
        });
        
        embed.setFooter({ text: 'Use !pet <pet_name> to view detailed pet information' });
        
        await message.reply({ embeds: [embed] });
    },
    
    async handleActivePet(message, user) {
        const activePet = await UserPet.findActiveByUser(user.id);
        
        if (!activePet) {
            const embed = GameEmbedBuilder.createWarningEmbed(
                '🐾 No Active Pet',
                'You don\'t have an active pet!\nUse `!pet activate <pet_name>` to set an active pet.'
            );
            return message.reply({ embeds: [embed] });
        }
        
        await this.showDetailedPetInfo(message, activePet);
    },
    
    async handleFeedPet(message, user, petName) {
        if (!petName) {
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Missing Pet Name',
                'Please specify which pet to feed.\n**Usage:** `!pet feed <pet_name>`'
            );
            return message.reply({ embeds: [embed] });
        }
        
        const userPet = await this.findUserPet(user.id, petName);
        if (!userPet) {
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Pet Not Found',
                `You don't have a pet matching "${petName}".`
            );
            return message.reply({ embeds: [embed] });
        }
        
        // Check if pet needs feeding
        if (userPet.hunger >= 90) {
            const embed = GameEmbedBuilder.createWarningEmbed(
                '🍽️ Pet Not Hungry',
                `**${userPet.getDisplayName()}** is not hungry right now!\nHunger: ${userPet.hunger}/100`
            );
            return message.reply({ embeds: [embed] });
        }
        
        // Feed the pet
        const oldHunger = userPet.hunger;
        const oldHappiness = userPet.happiness;
        
        await userPet.feed();
        
        const embed = GameEmbedBuilder.createSuccessEmbed(
            '🍽️ Pet Fed!',
            `You fed **${userPet.getDisplayName()}**!`
        );
        
        embed.addFields(
            {
                name: '📊 Condition Changes',
                value: `**Hunger:** ${oldHunger} → ${userPet.hunger} (+${userPet.hunger - oldHunger})\n**Happiness:** ${oldHappiness} → ${userPet.happiness} (+${userPet.happiness - oldHappiness})`,
                inline: true
            },
            {
                name: '😊 Current Status',
                value: this.getConditionText(userPet),
                inline: true
            }
        );
        
        embed.setFooter({ text: 'Well-fed pets perform better in battle!' });
        
        await message.reply({ embeds: [embed] });
    },
    
    async handlePlayWithPet(message, user, petName) {
        if (!petName) {
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Missing Pet Name',
                'Please specify which pet to play with.\n**Usage:** `!pet play <pet_name>`'
            );
            return message.reply({ embeds: [embed] });
        }
        
        const userPet = await this.findUserPet(user.id, petName);
        if (!userPet) {
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Pet Not Found',
                `You don't have a pet matching "${petName}".`
            );
            return message.reply({ embeds: [embed] });
        }
        
        // Check if pet wants to play
        if (userPet.happiness >= 95) {
            const embed = GameEmbedBuilder.createWarningEmbed(
                '🎾 Pet Not Interested',
                `**${userPet.getDisplayName()}** is already very happy and doesn't want to play right now!\nHappiness: ${userPet.happiness}/100`
            );
            return message.reply({ embeds: [embed] });
        }
        
        // Play with the pet
        const oldHappiness = userPet.happiness;
        const oldHunger = userPet.hunger;
        
        await userPet.play();
        
        const embed = GameEmbedBuilder.createSuccessEmbed(
            '🎾 Playtime!',
            `You played with **${userPet.getDisplayName()}**!`
        );
        
        embed.addFields(
            {
                name: '📊 Condition Changes',
                value: `**Happiness:** ${oldHappiness} → ${userPet.happiness} (+${userPet.happiness - oldHappiness})\n**Hunger:** ${oldHunger} → ${userPet.hunger} (${userPet.hunger - oldHunger})`,
                inline: true
            },
            {
                name: '😊 Current Status',
                value: this.getConditionText(userPet),
                inline: true
            }
        );
        
        embed.setFooter({ text: 'Happy pets are more effective in combat!' });
        
        await message.reply({ embeds: [embed] });
    },
    
    async handleEvolvePet(message, user, petName) {
        if (!petName) {
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Missing Pet Name',
                'Please specify which pet to evolve.\n**Usage:** `!pet evolve <pet_name>`'
            );
            return message.reply({ embeds: [embed] });
        }
        
        const userPet = await this.findUserPet(user.id, petName);
        if (!userPet) {
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Pet Not Found',
                `You don't have a pet matching "${petName}".`
            );
            return message.reply({ embeds: [embed] });
        }
        
        // Check if pet can evolve
        if (!userPet.canEvolve()) {
            const embed = GameEmbedBuilder.createWarningEmbed(
                '🚫 Cannot Evolve',
                `**${userPet.getDisplayName()}** cannot evolve.\n\nReasons:\n• Pet may not have an evolution\n• Level requirement not met\n• Missing evolution materials`
            );
            return message.reply({ embeds: [embed] });
        }
        
        const requirements = userPet.pet.getEvolutionRequirements();
        
        // Check level requirement
        if (userPet.level < requirements.level) {
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Level Requirement Not Met',
                `**${userPet.getDisplayName()}** needs to be level **${requirements.level}** to evolve.\nCurrent level: **${userPet.level}**`
            );
            return message.reply({ embeds: [embed] });
        }
        
        // For now, just show evolution requirements (materials system can be implemented later)
        const embed = GameEmbedBuilder.createInfoEmbed(
            '✨ Evolution Available!',
            `**${userPet.getDisplayName()}** can evolve!`
        );
        
        embed.addFields({
            name: '📋 Evolution Requirements',
            value: `**Level:** ${requirements.level} ✅\n**Materials:** Coming soon!`,
            inline: false
        });
        
        embed.setFooter({ text: 'Evolution system will be fully implemented in a future update!' });
        
        await message.reply({ embeds: [embed] });
    },
    
    async handleActivatePet(message, user, petName) {
        if (!petName) {
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Missing Pet Name',
                'Please specify which pet to activate.\n**Usage:** `!pet activate <pet_name>`'
            );
            return message.reply({ embeds: [embed] });
        }
        
        const userPet = await this.findUserPet(user.id, petName);
        if (!userPet) {
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Pet Not Found',
                `You don't have a pet matching "${petName}".`
            );
            return message.reply({ embeds: [embed] });
        }
        
        // Deactivate current active pet
        await UserPet.update(
            { is_active: false },
            { where: { user_id: user.id, is_active: true } }
        );
        
        // Activate the selected pet
        userPet.is_active = true;
        await userPet.save();
        
        const embed = GameEmbedBuilder.createSuccessEmbed(
            '⭐ Pet Activated!',
            `**${userPet.getFormattedName()}** is now your active pet!`
        );
        
        const stats = userPet.getCurrentStats();
        embed.addFields({
            name: '⚔️ Combat Stats',
            value: `**Attack:** ${stats.attack}\n**Defense:** ${stats.defense}\n**Health:** ${stats.health}`,
            inline: true
        });
        
        const effectiveness = userPet.getBattleEffectiveness();
        embed.addFields({
            name: '📊 Battle Effectiveness',
            value: `**${effectiveness.effectiveness * 100}%**\n*${effectiveness.description}*`,
            inline: true
        });
        
        embed.setFooter({ text: 'Your active pet will assist you in combat!' });
        
        await message.reply({ embeds: [embed] });
    },
    
    async handlePetStatus(message, user, petName) {
        if (!petName) {
            return this.handleActivePet(message, user);
        }
        
        const userPet = await this.findUserPet(user.id, petName);
        if (!userPet) {
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Pet Not Found',
                `You don't have a pet matching "${petName}".`
            );
            return message.reply({ embeds: [embed] });
        }
        
        await this.showDetailedPetInfo(message, userPet);
    },
    
    async findUserPet(userId, petName) {
        const userPets = await UserPet.findAll({
            where: { user_id: userId },
            include: [{
                model: Pet,
                as: 'pet',
                where: {
                    name: { [require('sequelize').Op.iLike]: `%${petName}%` }
                }
            }]
        });
        
        if (userPets.length === 0) return null;
        
        // If multiple matches, prefer exact match or active pet
        if (userPets.length > 1) {
            const exactMatch = userPets.find(up => up.pet.name.toLowerCase() === petName.toLowerCase());
            if (exactMatch) return exactMatch;
            
            const activeMatch = userPets.find(up => up.is_active);
            if (activeMatch) return activeMatch;
        }
        
        return userPets[0];
    },
    
    async showDetailedPetInfo(message, userPet) {
        const pet = userPet.pet;
        const rarityInfo = pet.getRarityInfo();
        const typeInfo = pet.getTypeInfo();
        
        const embed = GameEmbedBuilder.createInfoEmbed(
            `${rarityInfo.emoji} ${userPet.getDisplayName()}`,
            `*${pet.description || 'A loyal companion'}*`
        );
        
        embed.setColor(rarityInfo.color);
        
        // Basic info
        embed.addFields(
            {
                name: '📋 Basic Info',
                value: `**Type:** ${typeInfo.name} ${typeInfo.emoji}\n**Rarity:** ${rarityInfo.name}\n**Level:** ${userPet.level}/${pet.max_level}`,
                inline: true
            },
            {
                name: '📊 Experience',
                value: `**Current:** ${userPet.experience}\n**Required:** ${userPet.getRequiredXP()}\n**Progress:** ${Math.round((userPet.experience / userPet.getRequiredXP()) * 100)}%`,
                inline: true
            }
        );
        
        // Combat stats
        const stats = userPet.getCurrentStats();
        embed.addFields({
            name: '⚔️ Combat Stats',
            value: `**Attack:** ${stats.attack}\n**Defense:** ${stats.defense}\n**Health:** ${stats.health}`,
            inline: true
        });
        
        // Condition
        embed.addFields({
            name: '😊 Condition',
            value: this.getConditionText(userPet),
            inline: true
        });
        
        // Battle effectiveness
        const effectiveness = userPet.getBattleEffectiveness();
        embed.addFields({
            name: '📈 Battle Effectiveness',
            value: `**${Math.round(effectiveness.effectiveness * 100)}%**\n*${effectiveness.description}*`,
            inline: true
        });
        
        // Battle record
        if (userPet.battles_won > 0 || userPet.battles_lost > 0) {
            const totalBattles = userPet.battles_won + userPet.battles_lost;
            const winRate = Math.round((userPet.battles_won / totalBattles) * 100);
            
            embed.addFields({
                name: '🏆 Battle Record',
                value: `**Wins:** ${userPet.battles_won}\n**Losses:** ${userPet.battles_lost}\n**Win Rate:** ${winRate}%`,
                inline: false
            });
        }
        
        // Evolution info
        if (pet.canEvolve()) {
            const requirements = pet.getEvolutionRequirements();
            const canEvolve = userPet.level >= requirements.level;
            
            embed.addFields({
                name: '✨ Evolution',
                value: `**Available:** ${canEvolve ? 'Yes ✅' : 'No ❌'}\n**Required Level:** ${requirements.level}`,
                inline: false
            });
        }
        
        // Status
        const status = userPet.is_active ? '⭐ Active' : 'Inactive';
        embed.addFields({
            name: '📍 Status',
            value: status,
            inline: true
        });
        
        embed.setFooter({ text: 'Use !pet feed/play to improve your pet\'s condition!' });
        
        await message.reply({ embeds: [embed] });
    },
    
    getConditionText(userPet) {
        const condition = userPet.getConditionStatus();
        return `**Happiness:** ${userPet.happiness}/100 ${condition.happiness.emoji}\n**Hunger:** ${userPet.hunger}/100 ${condition.hunger.emoji}\n**Health:** ${userPet.health}/100 ${condition.health.emoji}`;
    }
};

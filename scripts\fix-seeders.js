#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Fix seeders for SQLite compatibility
function fixSeedersForSQLite() {
    console.log('🔧 Fixing seeders for SQLite compatibility...');
    
    const seedersDir = path.join(__dirname, '../src/database/seeders');
    const files = fs.readdirSync(seedersDir).filter(file => file.endsWith('.js'));
    
    for (const file of files) {
        const filePath = path.join(seedersDir, file);
        let content = fs.readFileSync(filePath, 'utf8');
        
        console.log(`  📄 Processing ${file}...`);
        
        // Fix arrays that need to be JSON.stringify()
        const arrayFields = [
            'special_features',
            'special_effects', 
            'drop_table',
            'rare_drop_table',
            'special_abilities',
            'drop_sources'
        ];
        
        let modified = false;
        
        for (const field of arrayFields) {
            // Pattern to match field: [array content]
            const pattern = new RegExp(`(${field}:\\s*)\\[([^\\]]*(?:\\[[^\\]]*\\][^\\]]*)*)\\]`, 'g');
            
            content = content.replace(pattern, (match, prefix, arrayContent) => {
                // Don't modify if already JSON.stringify
                if (prefix.includes('JSON.stringify')) {
                    return match;
                }
                
                modified = true;
                return `${prefix}JSON.stringify([${arrayContent}])`;
            });
        }
        
        if (modified) {
            fs.writeFileSync(filePath, content);
            console.log(`    ✅ Fixed ${file}`);
        } else {
            console.log(`    ⏭️ No changes needed for ${file}`);
        }
    }
    
    console.log('✅ All seeders fixed for SQLite compatibility!');
}

// Run if called directly
if (require.main === module) {
    fixSeedersForSQLite();
}

module.exports = fixSeedersForSQLite;

module.exports = {
    up: async (queryInterface, Sequelize) => {
        await queryInterface.bulkInsert('vip_levels', [
            {
                level: 0,
                name: 'Free Player',
                required_spending: 0.00,
                cooldown_reduction: 0.00,
                gold_bonus: 0.00,
                daily_gems: 0,
                gacha_discount: 0.00,
                special_features: JSON.stringify([]),
                color: '#FFFFFF',
                description: 'Basic game features',
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                level: 1,
                name: 'Bronze VIP',
                required_spending: 10.00,
                cooldown_reduction: 0.25,
                gold_bonus: 0.10,
                daily_gems: 5,
                gacha_discount: 0.05,
                special_features: JSON.stringify(['priority_support']),
                color: '#CD7F32',
                description: 'Entry level VIP with basic benefits',
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                level: 2,
                name: 'Silver VIP',
                required_spending: 50.00,
                cooldown_reduction: 0.35,
                gold_bonus: 0.20,
                daily_gems: 10,
                gacha_discount: 0.10,
                special_features: JSON.stringify(['priority_support', 'exclusive_chat']),
                color: '#C0C0C0',
                description: 'Enhanced benefits and exclusive features',
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                level: 3,
                name: 'Gold VIP',
                required_spending: 150.00,
                cooldown_reduction: 0.45,
                gold_bonus: 0.30,
                daily_gems: 20,
                gacha_discount: 0.15,
                special_features: JSON.stringify(['priority_support', 'exclusive_chat', 'premium_gacha']),
                color: '#FFD700',
                description: 'Premium tier with premium gacha access',
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                level: 4,
                name: 'Platinum VIP',
                required_spending: 500.00,
                cooldown_reduction: 0.55,
                gold_bonus: 0.50,
                daily_gems: 35,
                gacha_discount: 0.20,
                special_features: JSON.stringify(['priority_support', 'exclusive_chat', 'premium_gacha', 'exclusive_items']),
                color: '#E5E4E2',
                description: 'Elite tier with exclusive items and major bonuses',
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                level: 5,
                name: 'Diamond VIP',
                required_spending: 1500.00,
                cooldown_reduction: 0.65,
                gold_bonus: 1.00,
                daily_gems: 75,
                gacha_discount: 0.25,
                special_features: JSON.stringify(['priority_support', 'exclusive_chat', 'premium_gacha', 'exclusive_items', 'custom_commands']),
                color: '#B9F2FF',
                description: 'Ultimate VIP tier with maximum benefits',
                created_at: new Date(),
                updated_at: new Date()
            }
        ]);
    },

    down: async (queryInterface, Sequelize) => {
        await queryInterface.bulkDelete('vip_levels', null, {});
    }
};

const { User, Character, Monster, Transaction } = require('../../database/database');
const GameEmbedBuilder = require('../../utils/embedBuilder');
const CombatEngine = require('../../utils/combatEngine');

module.exports = {
    data: {
        name: 'boss',
        aliases: ['b', 'bossfight', 'raid'],
        description: 'Challenge a powerful boss for greater rewards (Level 20+ required)',
        usage: '!boss [boss_name]',
        cooldown: 3600 // 1 hour
    },
    async execute(message, args) {
        try {
            // Get user and character
            const user = await User.findByDiscordId(message.author.id);
            const character = await Character.findOne({
                where: { user_id: user.id }
            });
            
            // Check level requirement
            if (character.level < 20) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Level Requirement Not Met',
                    'You must be at least **Level 20** to challenge bosses!\n' +
                    `Your current level: **${character.level}**`
                );
                return message.reply({ embeds: [embed] });
            }
            
            // Check cooldown
            if (character.isOnCooldown('boss_fight')) {
                const remaining = character.getCooldownRemaining('boss_fight');
                const embed = GameEmbedBuilder.createCooldownEmbed('boss fight', remaining);
                return message.reply({ embeds: [embed] });
            }
            
            // Check health
            if (character.health <= 0) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Cannot Fight Boss',
                    'You have no health left! Rest to recover before challenging bosses.'
                );
                return message.reply({ embeds: [embed] });
            }
            
            let boss;
            
            // If specific boss name provided
            if (args.length > 0) {
                const bossName = args.join(' ').toLowerCase();
                boss = await Monster.findOne({
                    where: {
                        name: { [require('sequelize').Op.iLike]: `%${bossName}%` },
                        type: { [require('sequelize').Op.in]: ['boss', 'raid_boss'] },
                        min_level_requirement: { [require('sequelize').Op.lte]: character.level },
                        vip_requirement: { [require('sequelize').Op.lte]: character.vip_level },
                        is_active: true
                    }
                });
                
                if (!boss) {
                    const embed = GameEmbedBuilder.createErrorEmbed(
                        'Boss Not Found',
                        `Boss "${args.join(' ')}" not found or not available for your level.`
                    );
                    return message.reply({ embeds: [embed] });
                }
            } else {
                // Show available bosses
                const availableBosses = await Monster.findAll({
                    where: {
                        type: { [require('sequelize').Op.in]: ['boss', 'raid_boss'] },
                        min_level_requirement: { [require('sequelize').Op.lte]: character.level },
                        vip_requirement: { [require('sequelize').Op.lte]: character.vip_level },
                        is_active: true
                    },
                    order: [['level', 'ASC']],
                    limit: 10
                });
                
                if (availableBosses.length === 0) {
                    const embed = GameEmbedBuilder.createErrorEmbed(
                        'No Bosses Available',
                        'No bosses are currently available for your level and VIP status.'
                    );
                    return message.reply({ embeds: [embed] });
                }
                
                const embed = GameEmbedBuilder.createInfoEmbed(
                    '👺 Available Bosses',
                    'Choose a boss to challenge:'
                );
                
                let bossListText = '';
                availableBosses.forEach((availableBoss, index) => {
                    const typeInfo = Monster.getTypeInfo(availableBoss.type);
                    const rewards = availableBoss.calculateRewards(character.level, character.vip_level);
                    
                    bossListText += `**${index + 1}.** ${availableBoss.getFormattedName()}\n`;
                    bossListText += `${typeInfo.emoji} ${typeInfo.name} • HP: ${availableBoss.health}\n`;
                    bossListText += `💰 Rewards: ~${rewards.gold} gold, ${rewards.exp} XP\n\n`;
                });
                
                embed.setDescription(bossListText);
                embed.setFooter({ text: 'Use !boss <boss_name> to challenge a specific boss' });
                
                return message.reply({ embeds: [embed] });
            }
            
            // Confirm boss challenge
            const typeInfo = boss.getTypeInfo();
            const estimatedRewards = boss.calculateRewards(character.level, character.vip_level);
            
            const confirmEmbed = GameEmbedBuilder.createWarningEmbed(
                '⚠️ Boss Challenge Confirmation',
                `You are about to challenge **${boss.getFormattedName()}**!`
            );
            
            confirmEmbed.addFields(
                {
                    name: '👺 Boss Info',
                    value: `**Type:** ${typeInfo.name}\n**Level:** ${boss.level}\n**Health:** ${boss.health}\n**Attack:** ${boss.attack}`,
                    inline: true
                },
                {
                    name: '🎁 Potential Rewards',
                    value: `**Gold:** ~${estimatedRewards.gold} 🪙\n**EXP:** ~${estimatedRewards.exp}\n**Gems:** ${estimatedRewards.gems} 💎`,
                    inline: true
                },
                {
                    name: '⚠️ Warning',
                    value: 'Boss fights are dangerous! You may lose health even if you win.\nCooldown: **1 hour**',
                    inline: false
                }
            );
            
            confirmEmbed.setFooter({ text: 'React with ✅ to confirm or ❌ to cancel' });
            
            const confirmMessage = await message.reply({ embeds: [confirmEmbed] });
            await confirmMessage.react('✅');
            await confirmMessage.react('❌');
            
            // Wait for confirmation
            const filter = (reaction, user) => {
                return ['✅', '❌'].includes(reaction.emoji.name) && user.id === message.author.id;
            };
            
            const collector = confirmMessage.createReactionCollector({ filter, time: 30000, max: 1 });
            
            collector.on('collect', async (reaction) => {
                if (reaction.emoji.name === '❌') {
                    const cancelEmbed = GameEmbedBuilder.createInfoEmbed(
                        'Boss Challenge Cancelled',
                        'You decided not to challenge the boss.'
                    );
                    await confirmMessage.edit({ embeds: [cancelEmbed] });
                    return;
                }
                
                // Start boss fight
                const fightEmbed = GameEmbedBuilder.createInfoEmbed(
                    '⚔️ Boss Battle Started!',
                    `The battle against **${boss.getFormattedName()}** begins!\nThis may take a while...`
                );
                await confirmMessage.edit({ embeds: [fightEmbed] });
                
                // Simulate boss battle (longer delay for dramatic effect)
                await new Promise(resolve => setTimeout(resolve, 3000));
                
                const battleResult = await CombatEngine.simulateBattle(character, boss, 'boss');
                
                // Apply VIP cooldown reduction
                const vipReduction = CombatEngine.calculateVipCooldownReduction(character.vip_level);
                const baseCooldown = 3600000; // 1 hour
                const actualCooldown = Math.floor(baseCooldown * (1 - vipReduction));
                
                // Update character cooldown
                character.last_boss_fight = new Date();
                
                // Apply battle rewards if victory
                let levelUpMessage = '';
                if (battleResult.result === 'victory') {
                    const rewardResult = await CombatEngine.applyBattleRewards(
                        character, 
                        battleResult.rewards, 
                        battleResult.drops
                    );
                    
                    if (rewardResult.leveledUp) {
                        levelUpMessage = `\n🎉 **LEVEL UP!** You are now level ${character.level}!`;
                    }
                    
                    // Create transaction record
                    await Transaction.createReward(
                        user.id,
                        battleResult.rewards.gold,
                        battleResult.rewards.gems,
                        character.gold,
                        character.gems,
                        `Boss victory vs ${boss.name}`,
                        'monster',
                        boss.id
                    );
                }
                
                // Save character
                await character.save();
                
                // Create result embed
                const resultEmbed = GameEmbedBuilder.createCombatResultEmbed(battleResult);
                resultEmbed.setTitle(battleResult.result === 'victory' ? 
                    '🏆 Boss Defeated!' : '💀 Boss Victory...');
                
                // Add level up message if applicable
                if (levelUpMessage) {
                    const currentDescription = resultEmbed.data.description || '';
                    resultEmbed.setDescription(currentDescription + levelUpMessage);
                }
                
                // Add boss-specific rewards information
                if (battleResult.result === 'victory') {
                    resultEmbed.addFields({
                        name: '👑 Boss Victory Bonus',
                        value: `You have proven your strength against a mighty ${typeInfo.name.toLowerCase()}!`,
                        inline: false
                    });
                    
                    // Add dropped items information
                    if (battleResult.drops && battleResult.drops.length > 0) {
                        const { Item } = require('../../database/database');
                        const dropTexts = [];
                        
                        for (const drop of battleResult.drops) {
                            const item = await Item.findByPk(drop.item_id);
                            if (item) {
                                const rarityInfo = item.getRarityInfo();
                                dropTexts.push(`${rarityInfo.emoji} ${item.name} x${drop.quantity}`);
                            }
                        }
                        
                        if (dropTexts.length > 0) {
                            resultEmbed.addFields({
                                name: '📦 Boss Drops',
                                value: dropTexts.join('\n'),
                                inline: false
                            });
                        }
                    }
                }
                
                // Add VIP cooldown information
                if (actualCooldown < baseCooldown) {
                    const savedTime = Math.floor((baseCooldown - actualCooldown) / 1000 / 60);
                    resultEmbed.addFields({
                        name: '👑 VIP Benefit',
                        value: `Cooldown reduced by ${savedTime}m (${Math.round(vipReduction * 100)}% VIP bonus)`,
                        inline: false
                    });
                }
                
                const nextCooldown = Math.floor(actualCooldown / 1000 / 60);
                resultEmbed.setFooter({ 
                    text: `Next boss fight available in ${nextCooldown}m | Use !boss to see available bosses` 
                });
                
                await confirmMessage.edit({ embeds: [resultEmbed] });
            });
            
            collector.on('end', (collected) => {
                if (collected.size === 0) {
                    const timeoutEmbed = GameEmbedBuilder.createWarningEmbed(
                        'Boss Challenge Timeout',
                        'You took too long to confirm. Boss challenge cancelled.'
                    );
                    confirmMessage.edit({ embeds: [timeoutEmbed] }).catch(console.error);
                }
                confirmMessage.reactions.removeAll().catch(console.error);
            });
            
        } catch (error) {
            console.error('Error in boss command:', error);
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Boss Fight Error',
                'An error occurred during the boss fight. Please try again.'
            );
            await message.reply({ embeds: [embed] });
        }
    }
};

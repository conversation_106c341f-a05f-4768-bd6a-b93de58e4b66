const { User, Character, Item, UserItem, Transaction } = require('../../database/database');
const GameEmbedBuilder = require('../../utils/embedBuilder');

module.exports = {
    data: {
        name: 'gacha',
        aliases: ['roll', 'pull', 'summon'],
        description: 'Try your luck with the gacha system',
        usage: 'igacha [single|multi] [premium]',
        cooldown: 5
    },

    async execute(message, args) {
        try {
            // Get user and character
            const user = await User.findByDiscordId(message.author.id);
            const character = await Character.findOne({
                where: { user_id: user.id }
            });

            // Show gacha information
            const embed = GameEmbedBuilder.createInfoEmbed(
                '🎰 Gacha System',
                'Try your luck and get powerful items!'
            );

            // Basic costs
            embed.addFields({
                name: '🎲 Normal Gacha',
                value: `**Single Roll:** 10 💎\n**10x Roll:** 90 💎`,
                inline: true
            });

            if (character.vip_level >= 3) {
                embed.addFields({
                    name: '💎 Premium Gacha (VIP 3+)',
                    value: `**Single Roll:** 25 💎\n**10x Roll:** 225 💎`,
                    inline: true
                });
            }

            embed.addFields({
                name: '💰 Your Gems',
                value: `${character.gems.toLocaleString()} 💎`,
                inline: true
            });

            if (character.vip_level > 0) {
                const discounts = [0, 5, 10, 15, 20, 25];
                const discount = discounts[character.vip_level] || 0;
                if (discount > 0) {
                    embed.addFields({
                        name: '👑 VIP Discount',
                        value: `${discount}% OFF all rolls!`,
                        inline: true
                    });
                }
            }

            embed.addFields({
                name: '📖 Usage',
                value: '`igacha` - View this information\n`igacha single` - Single roll (coming soon)\n`igacha multi` - 10x roll (coming soon)',
                inline: false
            });

            embed.setFooter({ text: 'Gacha rolling feature is under development!' });

            return message.reply({ embeds: [embed] });

        } catch (error) {
            console.error('Error in gacha command:', error);
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Gacha Error',
                'An error occurred while processing the gacha. Please try again.'
            );
            await message.reply({ embeds: [embed] });
        }
    }
};
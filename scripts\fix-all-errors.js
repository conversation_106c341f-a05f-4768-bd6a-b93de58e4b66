#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

class ProjectFixer {
    constructor() {
        this.errors = [];
        this.fixes = [];
        this.srcPath = path.join(__dirname, '../src');
    }

    async fixAllErrors() {
        console.log('🔧 Starting comprehensive project error fixing...\n');

        try {
            // 1. Fix database issues
            await this.fixDatabaseIssues();
            
            // 2. Fix model associations
            await this.fixModelAssociations();
            
            // 3. Fix command imports and usage
            await this.fixCommandIssues();
            
            // 4. Fix embedBuilder issues
            await this.fixEmbedBuilderIssues();
            
            // 5. Fix migration issues
            await this.fixMigrationIssues();
            
            // 6. Clean up unused imports
            await this.cleanupUnusedImports();
            
            // 7. Validate all fixes
            await this.validateFixes();
            
            this.generateReport();
            
        } catch (error) {
            console.error('❌ Error fixing project:', error);
            process.exit(1);
        }
    }

    async fixDatabaseIssues() {
        console.log('🗄️ Fixing database issues...');
        
        // Fix database.js pool configuration
        const dbPath = path.join(this.srcPath, 'database/database.js');
        let dbContent = fs.readFileSync(dbPath, 'utf8');
        
        // Fix missing closing brace in pool config
        if (dbContent.includes('pool: {') && !dbContent.includes('    },')) {
            dbContent = dbContent.replace(
                /pool: \{\s+max: 10,\s+min: 0,\s+acquire: 30000,\s+idle: 10000\s+\}/,
                `pool: {
        max: 10,
        min: 0,
        acquire: 30000,
        idle: 10000
    }`
            );
            fs.writeFileSync(dbPath, dbContent);
            this.fixes.push('Fixed database pool configuration');
        }
        
        console.log('  ✅ Database configuration fixed');
    }

    async fixModelAssociations() {
        console.log('🔗 Fixing model associations...');
        
        const dbPath = path.join(this.srcPath, 'database/database.js');
        let dbContent = fs.readFileSync(dbPath, 'utf8');
        
        // Remove Shop model import if it exists but causes issues
        if (dbContent.includes("const Shop = require('./models/Shop')(sequelize);")) {
            // Check if Shop model file exists
            const shopModelPath = path.join(this.srcPath, 'database/models/Shop.js');
            if (!fs.existsSync(shopModelPath)) {
                dbContent = dbContent.replace(/const Shop = require\('\.\/models\/Shop'\)\(sequelize\);\n/, '');
                dbContent = dbContent.replace(/,\s*Shop/g, '');
                dbContent = dbContent.replace(/Shop,\s*/g, '');
                fs.writeFileSync(dbPath, dbContent);
                this.fixes.push('Removed non-existent Shop model import');
            }
        }
        
        console.log('  ✅ Model associations fixed');
    }

    async fixCommandIssues() {
        console.log('⚙️ Fixing command issues...');
        
        const commandsPath = path.join(this.srcPath, 'commands');
        await this.processDirectory(commandsPath, this.fixCommandFile.bind(this));
        
        console.log('  ✅ Command issues fixed');
    }

    async fixCommandFile(filePath) {
        if (!filePath.endsWith('.js')) return;
        
        let content = fs.readFileSync(filePath, 'utf8');
        const originalContent = content;
        
        // Fix common import issues
        content = content.replace(
            /const \{ User, Character, Shop, Item \}/g,
            'const { User, Character, Item }'
        );
        
        // Fix Shop model usage
        content = content.replace(/Shop\.getCategories\(\)/g, `[
            { key: 'weapons', name: 'Weapons', emoji: '⚔️' },
            { key: 'armor', name: 'Armor', emoji: '🛡️' },
            { key: 'accessories', name: 'Accessories', emoji: '💍' },
            { key: 'consumables', name: 'Consumables', emoji: '🧪' },
            { key: 'materials', name: 'Materials', emoji: '⚒️' }
        ]`);
        
        // Fix VIP benefits usage
        content = content.replace(
            /require\('.*VipLevel'\)\.getBenefits\([^)]+\)/g,
            'character.getVipBenefits()'
        );
        
        // Fix discriminator validation issues
        content = content.replace(
            /discriminator: discordUser\.discriminator/g,
            'discriminator: discordUser.discriminator || "0"'
        );
        
        if (content !== originalContent) {
            fs.writeFileSync(filePath, content);
            this.fixes.push(`Fixed command file: ${path.relative(this.srcPath, filePath)}`);
        }
    }

    async fixEmbedBuilderIssues() {
        console.log('🎨 Fixing embed builder issues...');
        
        const embedPath = path.join(this.srcPath, 'utils/embedBuilder.js');
        let content = fs.readFileSync(embedPath, 'utf8');
        
        // Ensure DEFAULT_COLOR is properly defined
        if (!content.includes('const DEFAULT_COLOR')) {
            content = content.replace(
                /const \{ EmbedBuilder \} = require\('discord\.js'\);/,
                `const { EmbedBuilder } = require('discord.js');

// Default embed color - can be changed via admin command
const DEFAULT_COLOR = 0x8D689E; // Purple color #8D689E`
            );
        }
        
        // Fix createShopEmbed to handle Item objects directly
        content = content.replace(
            /const item = shopItem\.item;/g,
            '// shopItem is now directly an Item object'
        );
        
        content = content.replace(
            /shopItem\.getFormattedPrice\(\)/g,
            '`${item.base_price} 🪙`'
        );
        
        fs.writeFileSync(embedPath, content);
        this.fixes.push('Fixed embed builder issues');
        
        console.log('  ✅ Embed builder fixed');
    }

    async fixMigrationIssues() {
        console.log('📊 Fixing migration issues...');
        
        const migrationPath = path.join(this.srcPath, 'database/migrations/001-initial-setup.js');
        let content = fs.readFileSync(migrationPath, 'utf8');
        
        // Ensure discriminator allows variable length
        content = content.replace(
            /discriminator: \{\s+type: DataTypes\.STRING,\s+allowNull: false\s+\}/,
            `discriminator: {
                type: DataTypes.STRING,
                allowNull: true,
                validate: {
                    len: [1, 4] // Support both old (#1234) and new (0) Discord systems
                }
            }`
        );
        
        // Ensure settings column exists
        if (!content.includes('settings: {')) {
            content = content.replace(
                /total_spent: \{[^}]+\},/,
                `total_spent: {
                type: DataTypes.DECIMAL(10, 2),
                defaultValue: 0.00
            },
            settings: {
                type: DataTypes.TEXT,
                defaultValue: '{}'
            },`
            );
        }
        
        fs.writeFileSync(migrationPath, content);
        this.fixes.push('Fixed migration issues');
        
        console.log('  ✅ Migration issues fixed');
    }

    async cleanupUnusedImports() {
        console.log('🧹 Cleaning up unused imports...');
        
        const commandsPath = path.join(this.srcPath, 'commands');
        await this.processDirectory(commandsPath, (filePath) => {
            if (!filePath.endsWith('.js')) return;
            
            let content = fs.readFileSync(filePath, 'utf8');
            const originalContent = content;
            
            // Remove unused Shop imports
            if (content.includes('Shop') && !content.includes('Shop.')) {
                content = content.replace(/, Shop/g, '');
                content = content.replace(/Shop, /g, '');
                content = content.replace(/\{ Shop \}/g, '{}');
            }
            
            if (content !== originalContent) {
                fs.writeFileSync(filePath, content);
                this.fixes.push(`Cleaned imports: ${path.relative(this.srcPath, filePath)}`);
            }
        });
        
        console.log('  ✅ Unused imports cleaned');
    }

    async processDirectory(dir, processor) {
        const items = fs.readdirSync(dir);
        
        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                await this.processDirectory(fullPath, processor);
            } else {
                await processor(fullPath);
            }
        }
    }

    async validateFixes() {
        console.log('✅ Validating fixes...');
        
        // Try to require all models
        try {
            const { User, Character, Item } = require('../src/database/database');
            console.log('  ✅ Database models load successfully');
        } catch (error) {
            console.error('  ❌ Database models have issues:', error.message);
            this.errors.push('Database models validation failed');
        }
        
        // Check if commands can be loaded
        const commandsPath = path.join(this.srcPath, 'commands');
        let commandCount = 0;
        let errorCount = 0;
        
        await this.processDirectory(commandsPath, (filePath) => {
            if (!filePath.endsWith('.js')) return;
            
            try {
                delete require.cache[require.resolve(filePath)];
                const command = require(filePath);
                if (command.data && command.execute) {
                    commandCount++;
                } else {
                    errorCount++;
                }
            } catch (error) {
                console.error(`  ❌ Command error: ${path.relative(this.srcPath, filePath)} - ${error.message}`);
                errorCount++;
            }
        });
        
        console.log(`  ✅ ${commandCount} commands validated, ${errorCount} errors found`);
    }

    generateReport() {
        console.log('\n📋 Fix Report:');
        console.log(`✅ Applied ${this.fixes.length} fixes:`);
        this.fixes.forEach(fix => console.log(`  • ${fix}`));
        
        if (this.errors.length > 0) {
            console.log(`\n❌ ${this.errors.length} remaining issues:`);
            this.errors.forEach(error => console.log(`  • ${error}`));
        } else {
            console.log('\n🎉 All issues have been resolved!');
        }
        
        console.log('\n💡 Next steps:');
        console.log('  1. Run: npm restart');
        console.log('  2. Test commands: ihelp, iprofile, ishop');
        console.log('  3. Check logs for any remaining errors');
    }
}

// Run if called directly
if (require.main === module) {
    const fixer = new ProjectFixer();
    fixer.fixAllErrors();
}

module.exports = ProjectFixer;

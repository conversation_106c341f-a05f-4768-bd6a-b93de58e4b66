const { User, Character, UserItem, Item } = require('../../database/database');
const GameEmbedBuilder = require('../../utils/embedBuilder');

module.exports = {
    data: {
        name: 'equip',
        aliases: ['e', 'wear', 'use'],
        description: 'Equip an item from your inventory',
        usage: '!equip <item_name>',
        cooldown: 3
    },
    async execute(message, args) {
        try {
            if (args.length === 0) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Missing Item Name',
                    'Please specify the item you want to equip.\n**Usage:** `!equip <item_name>`'
                );
                return message.reply({ embeds: [embed] });
            }
            
            // Get user and character
            const user = await User.findByDiscordId(message.author.id);
            const character = await Character.findOne({
                where: { user_id: user.id }
            });
            
            const itemName = args.join(' ').toLowerCase();
            
            // Find the item in user's inventory
            const userItems = await UserItem.findAll({
                where: {
                    user_id: user.id,
                    quantity: { [require('sequelize').Op.gt]: 0 }
                },
                include: [{
                    model: Item,
                    as: 'item',
                    where: require('sequelize').where(
                        require('sequelize').fn('LOWER', require('sequelize').col('item.name')),
                        { [require('sequelize').Op.like]: `%${itemName.toLowerCase()}%` }
                    )
                }]
            });
            
            if (userItems.length === 0) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Item Not Found',
                    `You don't have any item matching "${itemName}" in your inventory.`
                );
                return message.reply({ embeds: [embed] });
            }
            
            // If multiple matches, show options
            if (userItems.length > 1) {
                const embed = GameEmbedBuilder.createInfoEmbed(
                    'Multiple Items Found',
                    'Multiple items match your search. Please be more specific:'
                );
                
                let itemList = '';
                userItems.forEach((userItem, index) => {
                    const item = userItem.item;
                    const rarityInfo = item.getRarityInfo();
                    const equipped = userItem.is_equipped ? ' ✅' : '';
                    const upgrade = userItem.upgrade_level > 0 ? ` +${userItem.upgrade_level}` : '';
                    
                    itemList += `**${index + 1}.** ${rarityInfo.emoji} ${item.name}${upgrade}${equipped}\n`;
                    itemList += `*${item.type} • Level ${item.level_requirement}*\n\n`;
                });
                
                embed.setDescription(itemList);
                embed.setFooter({ text: 'Use the exact item name to equip it' });
                
                return message.reply({ embeds: [embed] });
            }
            
            const userItem = userItems[0];
            const item = userItem.item;
            
            // Check if item is already equipped
            if (userItem.is_equipped) {
                const embed = GameEmbedBuilder.createWarningEmbed(
                    'Item Already Equipped',
                    `**${item.name}** is already equipped!`
                );
                return message.reply({ embeds: [embed] });
            }
            
            // Check if item can be equipped (level requirement)
            if (!item.canBeEquippedBy(character)) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Level Requirement Not Met',
                    `You need to be level **${item.level_requirement}** to equip **${item.name}**.\n` +
                    `Your current level: **${character.level}**`
                );
                return message.reply({ embeds: [embed] });
            }
            
            // Check if item type can be equipped
            const equipmentSlots = ['weapon', 'armor', 'helmet', 'boots', 'ring', 'necklace'];
            if (!equipmentSlots.includes(item.type)) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Cannot Equip Item',
                    `**${item.name}** is a ${item.type} and cannot be equipped.`
                );
                return message.reply({ embeds: [embed] });
            }
            
            // Get the equipment slot
            const slotName = `${item.type}_id`;
            
            // Check if there's already an item equipped in this slot
            let previousItem = null;
            if (character[slotName]) {
                // Unequip the previous item
                const previousUserItem = await UserItem.findOne({
                    where: {
                        user_id: user.id,
                        item_id: character[slotName],
                        is_equipped: true
                    },
                    include: ['item']
                });
                
                if (previousUserItem) {
                    previousUserItem.is_equipped = false;
                    await previousUserItem.save();
                    previousItem = previousUserItem.item;
                }
            }
            
            // Equip the new item
            character[slotName] = item.id;
            userItem.is_equipped = true;
            
            // Save changes
            await Promise.all([
                character.save(),
                userItem.save()
            ]);
            
            // Create success embed
            const rarityInfo = item.getRarityInfo();
            const embed = GameEmbedBuilder.createSuccessEmbed(
                '⚔️ Item Equipped!',
                `Successfully equipped **${rarityInfo.emoji} ${item.name}**!`
            );
            
            // Show item stats
            const stats = userItem.getTotalStats();
            let statsText = '';
            if (stats.attack > 0) statsText += `**Attack:** +${stats.attack}\n`;
            if (stats.defense > 0) statsText += `**Defense:** +${stats.defense}\n`;
            if (stats.health > 0) statsText += `**Health:** +${stats.health}\n`;
            if (stats.crit_rate > 0) statsText += `**Crit Rate:** +${stats.crit_rate}%\n`;
            if (stats.crit_damage > 0) statsText += `**Crit Damage:** +${stats.crit_damage}%\n`;
            if (stats.luck > 0) statsText += `**Luck:** +${stats.luck}\n`;
            
            if (statsText) {
                embed.addFields({
                    name: '📊 Item Stats',
                    value: statsText,
                    inline: true
                });
            }
            
            // Show slot information
            const typeInfo = item.getTypeInfo();
            embed.addFields({
                name: '🎯 Equipment Slot',
                value: `${typeInfo.emoji} ${typeInfo.name} slot`,
                inline: true
            });
            
            // Show upgrade level if applicable
            if (userItem.upgrade_level > 0) {
                embed.addFields({
                    name: '⬆️ Upgrade Level',
                    value: `+${userItem.upgrade_level}`,
                    inline: true
                });
            }
            
            // Show previous item if any
            if (previousItem) {
                embed.addFields({
                    name: '🔄 Replaced Item',
                    value: `Unequipped: **${previousItem.name}**`,
                    inline: false
                });
            }
            
            // Calculate new total stats
            const newTotalStats = character.getTotalStats();
            embed.addFields({
                name: '💪 Your New Stats',
                value: `**Attack:** ${newTotalStats.attack}\n**Defense:** ${newTotalStats.defense}\n**Health:** ${newTotalStats.max_health}`,
                inline: false
            });
            
            embed.setFooter({ text: 'Use !profile to see all your equipment and stats' });
            
            await message.reply({ embeds: [embed] });
            
        } catch (error) {
            console.error('Error in equip command:', error);
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Equip Error',
                'An error occurred while equipping the item. Please try again.'
            );
            await message.reply({ embeds: [embed] });
        }
    }
};

const { Character, User } = require('../../src/database/database');
const { sequelize } = require('../../src/database/database');

describe('Character Model', () => {
    let testUser;
    let testCharacter;

    beforeAll(async () => {
        await sequelize.sync({ force: true });
    });

    beforeEach(async () => {
        // Create test user
        testUser = await User.create({
            discord_id: '123456789',
            username: 'testuser',
            discriminator: '0001'
        });

        // Create test character
        testCharacter = await Character.create({
            user_id: testUser.id,
            level: 1,
            experience: 0,
            health: 100,
            max_health: 100,
            attack: 10,
            defense: 5,
            gold: 100,
            gems: 0
        });
    });

    afterEach(async () => {
        await Character.destroy({ where: {} });
        await User.destroy({ where: {} });
    });

    afterAll(async () => {
        await sequelize.close();
    });

    describe('Level System', () => {
        test('should calculate required XP correctly', () => {
            expect(testCharacter.getRequiredXP()).toBe(100); // Level 1 requires 100 XP
            
            testCharacter.level = 5;
            expect(testCharacter.getRequiredXP()).toBe(2500); // Level 5 requires 2500 XP
        });

        test('should check if character can level up', () => {
            testCharacter.experience = 50;
            expect(testCharacter.canLevelUp()).toBe(false);

            testCharacter.experience = 150;
            expect(testCharacter.canLevelUp()).toBe(true);
        });

        test('should level up correctly', () => {
            testCharacter.experience = 150;
            const oldLevel = testCharacter.level;
            const oldAttack = testCharacter.attack;
            const oldDefense = testCharacter.defense;
            const oldMaxHealth = testCharacter.max_health;

            testCharacter.levelUp();

            expect(testCharacter.level).toBe(oldLevel + 1);
            expect(testCharacter.attack).toBe(oldAttack + 2);
            expect(testCharacter.defense).toBe(oldDefense + 1);
            expect(testCharacter.max_health).toBe(oldMaxHealth + 10);
            expect(testCharacter.health).toBe(testCharacter.max_health); // Health restored on level up
        });

        test('should handle multiple level ups', () => {
            testCharacter.experience = 5000; // Enough for multiple levels
            const initialLevel = testCharacter.level;

            while (testCharacter.canLevelUp()) {
                testCharacter.levelUp();
            }

            expect(testCharacter.level).toBeGreaterThan(initialLevel);
        });
    });

    describe('Combat Stats', () => {
        test('should calculate total stats correctly', () => {
            const stats = testCharacter.getTotalStats();

            expect(stats.attack).toBe(testCharacter.attack);
            expect(stats.defense).toBe(testCharacter.defense);
            expect(stats.max_health).toBe(testCharacter.max_health);
            expect(stats.crit_rate).toBe(testCharacter.crit_rate);
            expect(stats.crit_damage).toBe(testCharacter.crit_damage);
        });

        test('should handle health changes', () => {
            testCharacter.takeDamage(30);
            expect(testCharacter.health).toBe(70);

            testCharacter.heal(20);
            expect(testCharacter.health).toBe(90);

            // Should not exceed max health
            testCharacter.heal(50);
            expect(testCharacter.health).toBe(testCharacter.max_health);
        });

        test('should handle death correctly', () => {
            testCharacter.takeDamage(150); // More than max health
            expect(testCharacter.health).toBe(0);
            expect(testCharacter.isDead()).toBe(true);
        });
    });

    describe('Cooldown System', () => {
        test('should check cooldowns correctly', () => {
            // No cooldown initially
            expect(testCharacter.isOnCooldown('fight')).toBe(false);

            // Set cooldown
            testCharacter.last_fight = new Date();
            expect(testCharacter.isOnCooldown('fight')).toBe(true);

            // Old cooldown should be expired
            testCharacter.last_fight = new Date(Date.now() - 60000); // 1 minute ago
            expect(testCharacter.isOnCooldown('fight')).toBe(false);
        });

        test('should calculate remaining cooldown time', () => {
            testCharacter.last_fight = new Date();
            const remaining = testCharacter.getCooldownRemaining('fight');
            
            expect(remaining).toBeGreaterThan(0);
            expect(remaining).toBeLessThanOrEqual(30000); // 30 seconds max
        });
    });

    describe('VIP System', () => {
        test('should handle VIP benefits correctly', () => {
            testCharacter.vip_level = 0;
            expect(testCharacter.getVipBenefits().name).toBe('Free Player');

            testCharacter.vip_level = 3;
            const vipBenefits = testCharacter.getVipBenefits();
            expect(vipBenefits.name).toBe('Gold VIP');
            expect(vipBenefits.cooldown_reduction).toBe(0.45);
        });
    });

    describe('Validation', () => {
        test('should validate character data', async () => {
            // Test invalid level
            await expect(Character.create({
                user_id: testUser.id,
                level: -1
            })).rejects.toThrow();

            // Test invalid stats
            await expect(Character.create({
                user_id: testUser.id,
                attack: -5
            })).rejects.toThrow();
        });

        test('should require user_id', async () => {
            await expect(Character.create({
                level: 1
            })).rejects.toThrow();
        });
    });

    describe('Database Relations', () => {
        test('should associate with user correctly', async () => {
            const character = await Character.findOne({
                where: { id: testCharacter.id },
                include: ['user']
            });

            expect(character.user).toBeDefined();
            expect(character.user.discord_id).toBe(testUser.discord_id);
        });
    });
});

{"name": "discord-afk-game-bot", "version": "1.0.0", "description": "Discord AFK Game Bot with combat, adventure, pets, and monetization features", "main": "src/bot.js", "scripts": {"start": "node src/bot.js", "dev": "nodemon src/bot.js", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:integration": "node scripts/run-tests.js --integration", "test:performance": "node scripts/run-tests.js --performance", "test:all": "node scripts/run-tests.js --integration --performance", "db:init": "node scripts/init-database.js", "db:reset": "node scripts/reset-database.js", "db:status": "node scripts/database-status.js", "db:backup": "node scripts/backup-database.js", "lint": "eslint src/", "lint:fix": "eslint src/ --fix", "performance": "node -e \"const {performanceMonitor} = require('./src/utils/performance'); console.log(performanceMonitor.generateReport());\"", "pm2:start": "pm2 start ecosystem.config.js", "pm2:stop": "pm2 stop afk-game-bot", "pm2:restart": "pm2 restart afk-game-bot", "pm2:reload": "pm2 reload afk-game-bot", "pm2:delete": "pm2 delete afk-game-bot", "pm2:logs": "pm2 logs afk-game-bot", "pm2:monit": "pm2 monit", "pm2:status": "pm2 status", "pm2:setup": "node scripts/pm2-setup.js", "update-embed-colors": "node scripts/update-embed-colors.js", "health-check": "node scripts/health-check.js"}, "keywords": ["discord", "bot", "game", "afk", "rpg"], "author": "Your Name", "license": "MIT", "dependencies": {"axios": "^1.6.2", "discord.js": "^14.14.1", "dotenv": "^16.3.1", "lodash": "^4.17.21", "moment": "^2.29.4", "node-cron": "^3.0.3", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.35.2", "sqlite3": "^5.1.7"}, "devDependencies": {"eslint": "^8.56.0", "eslint-config-standard": "^17.1.0", "eslint-plugin-import": "^2.29.1", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "jest": "^29.7.0", "nodemon": "^3.0.2"}, "engines": {"node": ">=18.0.0"}}
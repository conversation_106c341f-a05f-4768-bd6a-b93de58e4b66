const { User, Character } = require('../../database/database');
const GameEmbedBuilder = require('../../utils/embedBuilder');
const AFKSystem = require('../../utils/afkSystem');

module.exports = {
    data: {
        name: 'afk',
        aliases: ['farm', 'auto', 'offline'],
        description: 'Start or stop AFK farming to earn rewards while offline',
        usage: '!afk [start|stop|status] [location]',
        cooldown: 5
    },
    async execute(message, args) {
        try {
            // Get user and character
            const user = await User.findByDiscordId(message.author.id);
            const character = await Character.findOne({
                where: { user_id: user.id }
            });
            
            // Parse arguments
            let action = 'status';
            let location = 'forest';
            
            if (args.length > 0) {
                const firstArg = args[0].toLowerCase();
                if (['start', 'stop', 'status', 'locations'].includes(firstArg)) {
                    action = firstArg;
                    if (args.length > 1) {
                        location = args[1].toLowerCase();
                    }
                } else {
                    // First argument might be location
                    location = firstArg;
                    if (args.length > 1 && ['start', 'stop'].includes(args[1].toLowerCase())) {
                        action = args[1].toLowerCase();
                    } else {
                        action = 'start'; // Default to start if location is specified
                    }
                }
            }
            
            // Handle different actions
            switch (action) {
                case 'start':
                    await this.handleStartAFK(message, character, location);
                    break;
                case 'stop':
                    await this.handleStopAFK(message, character, user);
                    break;
                case 'status':
                    await this.handleAFKStatus(message, character);
                    break;
                case 'locations':
                    await this.handleShowLocations(message, character);
                    break;
                default:
                    await this.handleAFKStatus(message, character);
            }
            
        } catch (error) {
            console.error('Error in afk command:', error);
            const embed = GameEmbedBuilder.createErrorEmbed(
                'AFK System Error',
                'An error occurred while processing your AFK request. Please try again.'
            );
            await message.reply({ embeds: [embed] });
        }
    },
    
    async handleStartAFK(message, character, location) {
        // Check if already AFK
        if (AFKSystem.isAFK(character)) {
            const currentDuration = AFKSystem.getAFKDuration(character);
            const embed = GameEmbedBuilder.createWarningEmbed(
                '⚠️ Already AFK Farming',
                `You are already AFK farming in **${character.afk_location}**!\n` +
                `Duration: ${AFKSystem.formatDuration(currentDuration)}\n\n` +
                `Use \`!afk stop\` to collect your rewards.`
            );
            return message.reply({ embeds: [embed] });
        }
        
        // Check health
        if (character.health <= 0) {
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Cannot Start AFK',
                'You have no health left! Rest to recover before starting AFK farming.'
            );
            return message.reply({ embeds: [embed] });
        }
        
        // Get available locations
        const availableLocations = AFKSystem.getAvailableLocations(character.level);
        
        // Validate location
        if (!availableLocations[location]) {
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Invalid Location',
                `Location "${location}" is not available for your level.\n` +
                `Use \`!afk locations\` to see available locations.`
            );
            return message.reply({ embeds: [embed] });
        }
        
        // Start AFK farming
        const result = await AFKSystem.startAFK(character, location);
        
        if (result.success) {
            const locationData = availableLocations[location];
            const embed = GameEmbedBuilder.createSuccessEmbed(
                '🌙 AFK Farming Started!',
                `You have started AFK farming in **${locationData.name}**!`
            );
            
            embed.addFields(
                {
                    name: '📍 Location Info',
                    value: `**${locationData.name}**\n*${locationData.description}*\nMin Level: ${locationData.minLevel}`,
                    inline: true
                },
                {
                    name: '💰 Hourly Rates',
                    value: `**EXP:** ~${locationData.exp}/hour\n**Gold:** ~${locationData.gold}/hour 🪙\n**Gems:** ~${locationData.gems}/hour 💎`,
                    inline: true
                }
            );
            
            // Show VIP benefits
            if (character.vip_level > 0) {
                const vipInfo = character.getVipBenefits();
                embed.addFields({
                    name: '👑 VIP Benefits',
                    value: `**${vipInfo.name}**\n+${Math.round(vipInfo.gold_bonus * 100)}% Gold bonus`,
                    inline: false
                });
            }
            
            embed.addFields({
                name: '⚡ Quick Tips',
                value: [
                    '• Use `!afk status` to check your progress',
                    '• Use `!afk stop` to collect rewards',
                    '• AFK farming continues even when offline!',
                    '• Higher level locations give better rewards'
                ].join('\n'),
                inline: false
            });
            
            embed.setFooter({ text: 'Happy farming! Come back later to collect your rewards.' });
            
            await message.reply({ embeds: [embed] });
        } else {
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Failed to Start AFK',
                result.reason || 'Unknown error occurred.'
            );
            await message.reply({ embeds: [embed] });
        }
    },
    
    async handleStopAFK(message, character, user) {
        if (!AFKSystem.isAFK(character)) {
            const embed = GameEmbedBuilder.createWarningEmbed(
                '⚠️ Not AFK Farming',
                'You are not currently AFK farming!\nUse `!afk start [location]` to begin farming.'
            );
            return message.reply({ embeds: [embed] });
        }
        
        // Process AFK rewards
        const result = await AFKSystem.processAFKRewards(character, user.id);
        
        if (result.success) {
            const embed = GameEmbedBuilder.createSuccessEmbed(
                '🎁 AFK Rewards Collected!',
                `You have collected your AFK farming rewards!`
            );
            
            embed.addFields(
                {
                    name: '⏱️ Farming Duration',
                    value: `**${AFKSystem.formatDuration(result.duration)}**\nLocation: ${result.rewards.location}`,
                    inline: true
                },
                {
                    name: '🎁 Total Rewards',
                    value: `**EXP:** +${result.rewards.totalExp}\n**Gold:** +${result.rewards.totalGold} 🪙\n**Gems:** +${result.rewards.totalGems} 💎`,
                    inline: true
                }
            );
            
            if (result.rewards.battlesCount > 0) {
                const victories = result.rewards.battles.filter(b => b.result === 'victory').length;
                const winRate = Math.round((victories / result.rewards.battles.length) * 100);
                
                embed.addFields({
                    name: '⚔️ Battle Summary',
                    value: `**Battles:** ${result.rewards.battlesCount}\n**Victories:** ${victories}\n**Win Rate:** ${winRate}%`,
                    inline: false
                });
            }
            
            if (result.leveledUp) {
                embed.addFields({
                    name: '🎉 Level Up!',
                    value: `Congratulations! You are now **Level ${character.level}**!`,
                    inline: false
                });
            }
            
            embed.addFields({
                name: '💰 Current Stats',
                value: `**Gold:** ${character.gold.toLocaleString()} 🪙\n**Gems:** ${character.gems.toLocaleString()} 💎\n**Level:** ${character.level}`,
                inline: false
            });
            
            embed.setFooter({ text: 'Use !afk start to begin farming again!' });
            
            await message.reply({ embeds: [embed] });
        } else {
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Failed to Stop AFK',
                result.reason || 'Unknown error occurred.'
            );
            await message.reply({ embeds: [embed] });
        }
    },
    
    async handleAFKStatus(message, character) {
        if (!AFKSystem.isAFK(character)) {
            // Show AFK system overview
            const embed = GameEmbedBuilder.createInfoEmbed(
                '🌙 AFK Farming System',
                'Earn rewards automatically while offline!'
            );
            
            const availableLocations = AFKSystem.getAvailableLocations(character.level);
            let locationsText = '';
            
            Object.entries(availableLocations).forEach(([key, location]) => {
                locationsText += `**${location.name}** (Level ${location.minLevel}+)\n`;
                locationsText += `*${location.description}*\n`;
                locationsText += `EXP: ~${location.exp}/h • Gold: ~${location.gold}/h 🪙\n\n`;
            });
            
            embed.addFields({
                name: '📍 Available Locations',
                value: locationsText || 'No locations available for your level.',
                inline: false
            });
            
            embed.addFields({
                name: '🚀 Getting Started',
                value: '`!afk start [location]` - Start AFK farming\n`!afk locations` - View all locations\n`!afk status` - Check current status',
                inline: false
            });
            
            await message.reply({ embeds: [embed] });
        } else {
            // Show current AFK status
            const duration = AFKSystem.getAFKDuration(character);
            const locationData = AFKSystem.getLocationRates(character.afk_location, character.level);
            const hours = duration / (1000 * 60 * 60);
            const estimatedRewards = AFKSystem.getEstimatedRewards(character, character.afk_location, hours);
            
            const embed = GameEmbedBuilder.createInfoEmbed(
                '🌙 Currently AFK Farming',
                `You are farming in **${locationData.name}**`
            );
            
            embed.addFields(
                {
                    name: '⏱️ Duration',
                    value: AFKSystem.formatDuration(duration),
                    inline: true
                },
                {
                    name: '📍 Location',
                    value: `**${locationData.name}**\n*${locationData.description}*`,
                    inline: true
                },
                {
                    name: '🎁 Estimated Rewards',
                    value: `**EXP:** ~${estimatedRewards.exp}\n**Gold:** ~${estimatedRewards.gold} 🪙\n**Gems:** ~${estimatedRewards.gems} 💎`,
                    inline: false
                }
            );
            
            if (estimatedRewards.battles > 0) {
                embed.addFields({
                    name: '⚔️ Estimated Battles',
                    value: `~${estimatedRewards.battles} battles fought`,
                    inline: true
                });
            }
            
            embed.addFields({
                name: '⚡ Actions',
                value: '`!afk stop` - Collect rewards and stop farming\n`!afk status` - Refresh status',
                inline: false
            });
            
            embed.setFooter({ text: 'Farming continues even when you\'re offline!' });
            
            await message.reply({ embeds: [embed] });
        }
    },
    
    async handleShowLocations(message, character) {
        const availableLocations = AFKSystem.getAvailableLocations(character.level);
        const allLocations = {
            forest: AFKSystem.getLocationRates('forest', character.level),
            cave: AFKSystem.getLocationRates('cave', character.level),
            mountain: AFKSystem.getLocationRates('mountain', character.level),
            dungeon: AFKSystem.getLocationRates('dungeon', character.level)
        };
        
        const embed = GameEmbedBuilder.createInfoEmbed(
            '📍 AFK Farming Locations',
            'Choose your farming location wisely!'
        );
        
        Object.entries(allLocations).forEach(([key, location]) => {
            const isAvailable = availableLocations[key];
            const status = isAvailable ? '✅ Available' : `🔒 Requires Level ${location.minLevel}`;
            
            let locationText = `*${location.description}*\n`;
            locationText += `**Min Level:** ${location.minLevel}\n`;
            locationText += `**Difficulty:** ${'⭐'.repeat(Math.ceil(location.difficulty))}\n`;
            
            if (isAvailable) {
                locationText += `**Hourly Rates:**\n`;
                locationText += `• EXP: ~${location.exp}\n`;
                locationText += `• Gold: ~${location.gold} 🪙\n`;
                locationText += `• Gems: ~${location.gems} 💎\n`;
                locationText += `• Battles: ~${location.battlesPerHour}/hour`;
            }
            
            embed.addFields({
                name: `${location.name} ${status}`,
                value: locationText,
                inline: false
            });
        });
        
        // Show VIP benefits
        if (character.vip_level > 0) {
            const vipInfo = character.getVipBenefits();
            embed.addFields({
                name: '👑 Your VIP Benefits',
                value: `**${vipInfo.name}**\n+${Math.round(vipInfo.gold_bonus * 100)}% Gold bonus on all locations`,
                inline: false
            });
        }
        
        embed.addFields({
            name: '🚀 Usage',
            value: '`!afk start <location>` - Start farming at a location\nExample: `!afk start cave`',
            inline: false
        });
        
        embed.setFooter({ text: 'Higher difficulty locations give better rewards but are more dangerous!' });
        
        await message.reply({ embeds: [embed] });
    }
};

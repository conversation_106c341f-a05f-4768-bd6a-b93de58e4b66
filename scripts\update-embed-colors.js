#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

class EmbedColorUpdater {
    constructor() {
        this.commandsDir = path.join(__dirname, '../src/commands');
        this.updatedFiles = [];
    }

    async updateAllEmbeds() {
        console.log('🎨 Updating embed colors in all command files...\n');

        try {
            await this.processDirectory(this.commandsDir);
            
            console.log(`\n✅ Updated ${this.updatedFiles.length} files:`);
            this.updatedFiles.forEach(file => {
                console.log(`  📄 ${file}`);
            });
            
        } catch (error) {
            console.error('❌ Error updating embed colors:', error);
        }
    }

    async processDirectory(dir) {
        const items = fs.readdirSync(dir);

        for (const item of items) {
            const fullPath = path.join(dir, item);
            const stat = fs.statSync(fullPath);

            if (stat.isDirectory()) {
                await this.processDirectory(fullPath);
            } else if (item.endsWith('.js')) {
                await this.processFile(fullPath);
            }
        }
    }

    async processFile(filePath) {
        try {
            let content = fs.readFileSync(filePath, 'utf8');
            const originalContent = content;

            // Skip if file doesn't contain embed creation
            if (!content.includes('EmbedBuilder') && !content.includes('createBaseEmbed')) {
                return;
            }

            console.log(`🔍 Processing: ${path.relative(process.cwd(), filePath)}`);

            // Replace direct EmbedBuilder color usage
            content = this.updateDirectEmbedColors(content);

            // Replace GameEmbedBuilder calls with hardcoded colors
            content = this.updateGameEmbedBuilderCalls(content);

            // Only write if content changed
            if (content !== originalContent) {
                fs.writeFileSync(filePath, content);
                this.updatedFiles.push(path.relative(process.cwd(), filePath));
                console.log(`  ✅ Updated`);
            } else {
                console.log(`  ⏭️ No changes needed`);
            }

        } catch (error) {
            console.error(`  ❌ Error processing ${filePath}:`, error.message);
        }
    }

    updateDirectEmbedColors(content) {
        // Replace direct .setColor() calls with common colors
        const colorReplacements = [
            { pattern: /\.setColor\(['"`]#[0-9A-Fa-f]{6}['"`]\)/g, replacement: '.setColor(DEFAULT_COLOR)' },
            { pattern: /\.setColor\(0x[0-9A-Fa-f]{6}\)/g, replacement: '.setColor(DEFAULT_COLOR)' },
            { pattern: /\.setColor\(\d{7,8}\)/g, replacement: '.setColor(DEFAULT_COLOR)' }
        ];

        colorReplacements.forEach(({ pattern, replacement }) => {
            // Don't replace success (green), error (red), or warning (yellow) colors
            const matches = content.match(pattern);
            if (matches) {
                matches.forEach(match => {
                    const colorValue = match.match(/(['"`]#[0-9A-Fa-f]{6}['"`]|0x[0-9A-Fa-f]{6}|\d{7,8})/)[0];
                    
                    // Skip common status colors
                    if (this.isStatusColor(colorValue)) {
                        return;
                    }
                    
                    content = content.replace(match, replacement);
                });
            }
        });

        // Add DEFAULT_COLOR import if needed
        if (content.includes('DEFAULT_COLOR') && !content.includes('const DEFAULT_COLOR')) {
            const embedBuilderImport = content.match(/const.*GameEmbedBuilder.*require.*embedBuilder/);
            if (embedBuilderImport) {
                content = content.replace(
                    embedBuilderImport[0],
                    embedBuilderImport[0] + '\nconst DEFAULT_COLOR = 0x8D689E;'
                );
            }
        }

        return content;
    }

    updateGameEmbedBuilderCalls(content) {
        // Replace GameEmbedBuilder.createBaseEmbed calls with hardcoded colors
        const patterns = [
            /GameEmbedBuilder\.createBaseEmbed\([^,)]+,\s*[^,)]*,\s*['"`]#[0-9A-Fa-f]{6}['"`]\)/g,
            /GameEmbedBuilder\.createBaseEmbed\([^,)]+,\s*[^,)]*,\s*0x[0-9A-Fa-f]{6}\)/g,
            /GameEmbedBuilder\.createBaseEmbed\([^,)]+,\s*[^,)]*,\s*\d{7,8}\)/g
        ];

        patterns.forEach(pattern => {
            content = content.replace(pattern, (match) => {
                // Extract the color value
                const colorMatch = match.match(/(['"`]#[0-9A-Fa-f]{6}['"`]|0x[0-9A-Fa-f]{6}|\d{7,8})/);
                if (colorMatch && !this.isStatusColor(colorMatch[0])) {
                    // Replace the color with DEFAULT_COLOR
                    return match.replace(colorMatch[0], 'DEFAULT_COLOR');
                }
                return match;
            });
        });

        return content;
    }

    isStatusColor(colorValue) {
        // Clean the color value
        let cleanColor = colorValue.replace(/['"`]/g, '');
        
        if (cleanColor.startsWith('#')) {
            cleanColor = '0x' + cleanColor.slice(1);
        }
        
        const statusColors = [
            '0x00FF00', '0x00ff00', // Green (success)
            '0xFF0000', '0xff0000', // Red (error)  
            '0xFFFF00', '0xffff00', // Yellow (warning)
            '65280',    // Green decimal
            '16711680', // Red decimal
            '16776960'  // Yellow decimal
        ];

        return statusColors.includes(cleanColor) || statusColors.includes(colorValue);
    }
}

// Run if called directly
if (require.main === module) {
    const updater = new EmbedColorUpdater();
    updater.updateAllEmbeds();
}

module.exports = EmbedColorUpdater;

const logger = require('../utils/logger');

module.exports = {
    name: 'ready',
    once: true,
    async execute(client) {
        logger.success(`<PERSON><PERSON> is ready! Logged in as ${client.user.tag}`);
        logger.info(`Serving ${client.guilds.cache.size} guilds with ${client.users.cache.size} users`);
        
        // Set bot activity
        client.user.setActivity('!help | AFK Game Bot', { type: 'PLAYING' });
        
        // Log some statistics
        logger.info(`Commands loaded: ${client.commands.size}`);
        
        // Start any background tasks here if needed
        logger.info('Bot initialization completed successfully!');
    }
};

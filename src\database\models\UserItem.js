const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
    const UserItem = sequelize.define('UserItem', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id'
            },
            onDelete: 'CASCADE'
        },
        item_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'items',
                key: 'id'
            },
            onDelete: 'CASCADE'
        },
        quantity: {
            type: DataTypes.INTEGER,
            defaultValue: 1,
            validate: {
                min: 0
            }
        },
        upgrade_level: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0,
                max: 20
            }
        },
        is_equipped: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        },
        acquired_at: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW
        },
        acquired_from: {
            type: DataTypes.STRING,
            allowNull: true // 'shop', 'monster', 'gacha', 'daily', 'admin', etc.
        },
        // For unique items with special properties
        unique_properties: {
            type: DataTypes.JSONB,
            defaultValue: {}
        },
        // Durability system (optional)
        durability: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                min: 0,
                max: 100
            }
        },
        max_durability: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                min: 1,
                max: 100
            }
        }
    }, {
        tableName: 'user_items',
        indexes: [
            {
                fields: ['user_id']
            },
            {
                fields: ['item_id']
            },
            {
                fields: ['user_id', 'item_id']
            },
            {
                fields: ['is_equipped']
            }
        ]
    });

    // Instance methods
    UserItem.prototype.getTotalStats = function() {
        // Get item stats with upgrade bonus
        if (!this.item) return {};
        
        return this.item.getTotalStats(this.upgrade_level);
    };

    UserItem.prototype.getUpgradeCost = function() {
        if (!this.item) return null;
        return this.item.getUpgradeCost(this.upgrade_level);
    };

    UserItem.prototype.canUpgrade = function() {
        if (!this.item) return false;
        if (this.upgrade_level >= this.item.max_upgrade_level) return false;
        if (!this.item.is_upgradeable) return false;
        return true;
    };

    UserItem.prototype.upgrade = function() {
        if (!this.canUpgrade()) return false;
        
        this.upgrade_level += 1;
        return true;
    };

    UserItem.prototype.getFormattedName = function() {
        if (!this.item) return 'Unknown Item';
        
        let name = this.item.getFormattedName();
        
        if (this.upgrade_level > 0) {
            name += ` +${this.upgrade_level}`;
        }
        
        if (this.is_equipped) {
            name += ' ✅';
        }
        
        return name;
    };

    UserItem.prototype.getSellPrice = function() {
        if (!this.item || !this.item.is_sellable) return 0;
        
        let basePrice = this.item.sell_price || Math.floor(this.item.base_price * 0.5);
        
        // Upgrade bonus
        const upgradeMultiplier = 1 + (this.upgrade_level * 0.2);
        
        return Math.floor(basePrice * upgradeMultiplier);
    };

    UserItem.prototype.isDamaged = function() {
        if (!this.durability || !this.max_durability) return false;
        return this.durability < this.max_durability;
    };

    UserItem.prototype.isBroken = function() {
        if (!this.durability) return false;
        return this.durability <= 0;
    };

    UserItem.prototype.repair = function(amount = null) {
        if (!this.durability || !this.max_durability) return false;
        
        if (amount === null) {
            this.durability = this.max_durability;
        } else {
            this.durability = Math.min(this.max_durability, this.durability + amount);
        }
        
        return true;
    };

    UserItem.prototype.takeDamage = function(amount = 1) {
        if (!this.durability) return false;
        
        this.durability = Math.max(0, this.durability - amount);
        return true;
    };

    // Static methods
    UserItem.findByUserAndItem = function(userId, itemId) {
        return this.findOne({
            where: {
                user_id: userId,
                item_id: itemId
            },
            include: ['item']
        });
    };

    UserItem.findEquippedByUser = function(userId) {
        return this.findAll({
            where: {
                user_id: userId,
                is_equipped: true
            },
            include: ['item']
        });
    };

    UserItem.findInventoryByUser = function(userId, options = {}) {
        const whereClause = {
            user_id: userId,
            quantity: { [sequelize.Sequelize.Op.gt]: 0 }
        };

        if (options.type) {
            whereClause['$item.type$'] = options.type;
        }

        if (options.rarity) {
            whereClause['$item.rarity$'] = options.rarity;
        }

        return this.findAll({
            where: whereClause,
            include: ['item'],
            order: [
                ['is_equipped', 'DESC'],
                ['item', 'rarity', 'DESC'],
                ['upgrade_level', 'DESC'],
                ['acquired_at', 'DESC']
            ],
            limit: options.limit || 50,
            offset: options.offset || 0
        });
    };

    return UserItem;
};

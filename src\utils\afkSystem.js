const { Character, Monster, Transaction, BattleLog } = require('../database/database');
const CombatEngine = require('./combatEngine');

class AFKSystem {
    static async startAFK(character, location = 'forest') {
        // Set AFK start time and location
        character.afk_start_time = new Date();
        character.afk_location = location;
        await character.save();
        
        return {
            success: true,
            startTime: character.afk_start_time,
            location: location
        };
    }
    
    static async stopAFK(character) {
        if (!character.afk_start_time) {
            return {
                success: false,
                reason: 'Not currently AFK farming'
            };
        }
        
        const afkDuration = Date.now() - new Date(character.afk_start_time).getTime();
        const rewards = await this.calculateAFKRewards(character, afkDuration);
        
        // Apply rewards
        character.experience += rewards.totalExp;
        character.gold += rewards.totalGold;
        character.gems += rewards.totalGems;
        
        // Check for level ups
        let leveledUp = false;
        while (character.canLevelUp()) {
            character.levelUp();
            leveledUp = true;
        }
        
        // Clear AFK status
        character.afk_start_time = null;
        character.afk_location = null;
        await character.save();
        
        return {
            success: true,
            duration: afkDuration,
            rewards: rewards,
            leveledUp: leveledUp
        };
    }
    
    static async calculateAFKRewards(character, duration) {
        const hours = duration / (1000 * 60 * 60);
        const location = character.afk_location || 'forest';
        
        // Base rates per hour
        const baseRates = this.getLocationRates(location, character.level);
        
        // VIP multipliers
        const vipInfo = require('../database/models/VipLevel').getBenefits(character.vip_level);
        const vipMultiplier = 1 + vipInfo.gold_bonus;
        
        // Calculate total rewards
        const totalExp = Math.floor(baseRates.exp * hours);
        const totalGold = Math.floor(baseRates.gold * hours * vipMultiplier);
        const totalGems = Math.floor(baseRates.gems * hours);
        
        // Simulate battles for more detailed rewards
        const battlesSimulated = Math.floor(hours * baseRates.battlesPerHour);
        const battles = [];
        
        for (let i = 0; i < Math.min(battlesSimulated, 100); i++) { // Limit to 100 battles for performance
            const monster = await this.getRandomAFKMonster(character, location);
            if (monster) {
                const battleResult = await CombatEngine.simulateBattle(character, monster, 'afk');
                battles.push(battleResult);
            }
        }
        
        return {
            totalExp,
            totalGold,
            totalGems,
            battles,
            battlesCount: battlesSimulated,
            hoursAfk: hours,
            location
        };
    }
    
    static getLocationRates(location, characterLevel) {
        const locations = {
            forest: {
                name: 'Peaceful Forest',
                description: 'A safe area for beginners',
                minLevel: 1,
                exp: 20 + (characterLevel * 2),
                gold: 15 + (characterLevel * 1.5),
                gems: 0.1,
                battlesPerHour: 6,
                difficulty: 0.8
            },
            cave: {
                name: 'Dark Cave',
                description: 'Dangerous but rewarding',
                minLevel: 10,
                exp: 35 + (characterLevel * 3),
                gold: 25 + (characterLevel * 2.5),
                gems: 0.2,
                battlesPerHour: 4,
                difficulty: 1.2
            },
            mountain: {
                name: 'Frozen Mountain',
                description: 'Harsh environment with great rewards',
                minLevel: 25,
                exp: 50 + (characterLevel * 4),
                gold: 40 + (characterLevel * 3.5),
                gems: 0.3,
                battlesPerHour: 3,
                difficulty: 1.5
            },
            dungeon: {
                name: 'Ancient Dungeon',
                description: 'High risk, high reward',
                minLevel: 50,
                exp: 80 + (characterLevel * 6),
                gold: 70 + (characterLevel * 5),
                gems: 0.5,
                battlesPerHour: 2,
                difficulty: 2.0
            }
        };
        
        return locations[location] || locations.forest;
    }
    
    static async getRandomAFKMonster(character, location) {
        const locationData = this.getLocationRates(location, character.level);
        const monsterLevel = Math.max(1, Math.floor(character.level * locationData.difficulty));
        
        const monsters = await Monster.findAll({
            where: {
                type: 'normal',
                level: {
                    [require('sequelize').Op.between]: [
                        Math.max(1, monsterLevel - 2),
                        monsterLevel + 2
                    ]
                },
                is_active: true
            },
            order: require('sequelize').literal('RANDOM()'),
            limit: 1
        });
        
        return monsters[0] || null;
    }
    
    static getAvailableLocations(characterLevel) {
        const allLocations = {
            forest: this.getLocationRates('forest', characterLevel),
            cave: this.getLocationRates('cave', characterLevel),
            mountain: this.getLocationRates('mountain', characterLevel),
            dungeon: this.getLocationRates('dungeon', characterLevel)
        };
        
        const availableLocations = {};
        
        Object.entries(allLocations).forEach(([key, location]) => {
            if (characterLevel >= location.minLevel) {
                availableLocations[key] = location;
            }
        });
        
        return availableLocations;
    }
    
    static isAFK(character) {
        return character.afk_start_time !== null;
    }
    
    static getAFKDuration(character) {
        if (!character.afk_start_time) return 0;
        return Date.now() - new Date(character.afk_start_time).getTime();
    }
    
    static formatDuration(milliseconds) {
        const hours = Math.floor(milliseconds / (1000 * 60 * 60));
        const minutes = Math.floor((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((milliseconds % (1000 * 60)) / 1000);
        
        if (hours > 0) {
            return `${hours}h ${minutes}m ${seconds}s`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds}s`;
        } else {
            return `${seconds}s`;
        }
    }
    
    static async processAFKRewards(character, userId) {
        const afkResult = await this.stopAFK(character);
        
        if (!afkResult.success) {
            return afkResult;
        }
        
        // Create transaction record
        if (afkResult.rewards.totalGold > 0 || afkResult.rewards.totalGems > 0) {
            await Transaction.createReward(
                userId,
                afkResult.rewards.totalGold,
                afkResult.rewards.totalGems,
                character.gold,
                character.gems,
                `AFK farming for ${this.formatDuration(afkResult.duration)}`,
                'afk',
                null
            );
        }
        
        // Create battle logs for significant battles
        const significantBattles = afkResult.rewards.battles.filter(battle => 
            battle.result === 'victory' && battle.rewards.exp > 50
        ).slice(0, 10); // Limit to 10 most significant battles
        
        for (const battle of significantBattles) {
            await BattleLog.create({
                user_id: userId,
                monster_id: battle.monster.id,
                battle_type: 'afk',
                result: battle.result,
                player_level: character.level,
                player_attack: character.attack,
                player_defense: character.defense,
                player_health_before: character.health,
                player_health_after: character.health,
                monster_level: battle.monster.level,
                monster_health: battle.monster.health,
                monster_attack: battle.monster.attack,
                monster_defense: battle.monster.defense,
                turns_taken: battle.turns,
                damage_dealt: battle.damageDealt,
                damage_received: battle.damageReceived,
                critical_hits: battle.criticalHits,
                exp_gained: battle.rewards.exp,
                gold_gained: battle.rewards.gold,
                gems_gained: battle.rewards.gems,
                location: afkResult.rewards.location
            });
        }
        
        return afkResult;
    }
    
    static getEstimatedRewards(character, location, hours) {
        const locationData = this.getLocationRates(location, character.level);
        const vipInfo = require('../database/models/VipLevel').getBenefits(character.vip_level);
        const vipMultiplier = 1 + vipInfo.gold_bonus;
        
        return {
            exp: Math.floor(locationData.exp * hours),
            gold: Math.floor(locationData.gold * hours * vipMultiplier),
            gems: Math.floor(locationData.gems * hours),
            battles: Math.floor(locationData.battlesPerHour * hours)
        };
    }
}

module.exports = AFKSystem;

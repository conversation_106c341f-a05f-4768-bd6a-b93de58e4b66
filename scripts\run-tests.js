#!/usr/bin/env node

const { spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

class TestRunner {
    constructor() {
        this.testResults = {
            passed: 0,
            failed: 0,
            total: 0,
            coverage: null,
            duration: 0
        };
    }

    async runTests(options = {}) {
        console.log('🧪 Starting AFK Game Bot Test Suite\n');
        
        const startTime = Date.now();
        
        try {
            // Check if test database is available
            await this.checkTestDatabase();
            
            // Run different test suites
            if (options.unit !== false) {
                await this.runUnitTests();
            }
            
            if (options.integration) {
                await this.runIntegrationTests();
            }
            
            if (options.performance) {
                await this.runPerformanceTests();
            }
            
            // Generate final report
            this.testResults.duration = Date.now() - startTime;
            this.generateReport();
            
        } catch (error) {
            console.error('❌ Test suite failed:', error);
            process.exit(1);
        }
    }

    async checkTestDatabase() {
        console.log('🔍 Checking test database...');
        
        try {
            const { sequelize } = require('../src/database/database');
            await sequelize.authenticate();
            console.log('✅ Test database connection successful\n');
        } catch (error) {
            console.error('❌ Test database connection failed:', error.message);
            console.log('💡 Make sure your test database is running and configured correctly');
            throw error;
        }
    }

    async runUnitTests() {
        console.log('🔬 Running Unit Tests...');
        
        return new Promise((resolve, reject) => {
            const jest = spawn('npx', ['jest', '--testPathPattern=tests/', '--coverage'], {
                stdio: 'pipe',
                shell: true
            });

            let output = '';
            let errorOutput = '';

            jest.stdout.on('data', (data) => {
                const text = data.toString();
                output += text;
                process.stdout.write(text);
            });

            jest.stderr.on('data', (data) => {
                const text = data.toString();
                errorOutput += text;
                process.stderr.write(text);
            });

            jest.on('close', (code) => {
                this.parseJestOutput(output);
                
                if (code === 0) {
                    console.log('✅ Unit tests completed successfully\n');
                    resolve();
                } else {
                    console.log('❌ Unit tests failed\n');
                    reject(new Error(`Jest exited with code ${code}`));
                }
            });
        });
    }

    async runIntegrationTests() {
        console.log('🔗 Running Integration Tests...');
        
        // Integration tests would test full command flows
        const integrationTests = [
            this.testUserRegistration,
            this.testCombatFlow,
            this.testEconomyFlow,
            this.testVIPSystem
        ];

        let passed = 0;
        let failed = 0;

        for (const test of integrationTests) {
            try {
                await test.call(this);
                passed++;
                console.log(`  ✅ ${test.name}`);
            } catch (error) {
                failed++;
                console.log(`  ❌ ${test.name}: ${error.message}`);
            }
        }

        console.log(`\n📊 Integration Tests: ${passed} passed, ${failed} failed\n`);
        
        if (failed > 0) {
            throw new Error('Integration tests failed');
        }
    }

    async runPerformanceTests() {
        console.log('⚡ Running Performance Tests...');
        
        const { performanceMonitor } = require('../src/utils/performance');
        const { User, Character } = require('../src/database/database');
        
        // Test database query performance
        console.log('  📊 Testing database performance...');
        
        const iterations = 100;
        performanceMonitor.startTimer('db_bulk_operations');
        
        for (let i = 0; i < iterations; i++) {
            await User.findAll({ limit: 10 });
        }
        
        const dbTime = performanceMonitor.endTimer('db_bulk_operations');
        console.log(`    Database queries: ${(dbTime / iterations).toFixed(2)}ms average`);
        
        // Test combat simulation performance
        console.log('  ⚔️ Testing combat performance...');
        
        const CombatEngine = require('../src/utils/combatEngine');
        
        performanceMonitor.startTimer('combat_simulation');
        
        for (let i = 0; i < 50; i++) {
            CombatEngine.calculateDamage(25, 10, false);
        }
        
        const combatTime = performanceMonitor.endTimer('combat_simulation');
        console.log(`    Combat calculations: ${(combatTime / 50).toFixed(2)}ms average`);
        
        // Memory usage test
        const memoryUsage = performanceMonitor.getMemoryUsage();
        console.log(`  💾 Memory usage: ${memoryUsage.heapUsed}MB heap used`);
        
        console.log('✅ Performance tests completed\n');
    }

    // Integration test methods
    async testUserRegistration() {
        const { User, Character } = require('../src/database/database');
        
        // Clean up any existing test user
        await User.destroy({ where: { discord_id: 'test-integration-user' } });
        
        // Test user creation
        const user = await User.create({
            discord_id: 'test-integration-user',
            username: 'testuser',
            discriminator: '0001'
        });
        
        // Test character creation
        const character = await Character.create({
            user_id: user.id
        });
        
        // Verify defaults
        if (character.level !== 1) throw new Error('Default level incorrect');
        if (character.gold !== 100) throw new Error('Default gold incorrect');
        
        // Clean up
        await Character.destroy({ where: { user_id: user.id } });
        await User.destroy({ where: { id: user.id } });
    }

    async testCombatFlow() {
        const CombatEngine = require('../src/utils/combatEngine');
        const { User, Character, Monster } = require('../src/database/database');
        
        // Create test data
        const user = await User.create({
            discord_id: 'test-combat-user',
            username: 'combatuser',
            discriminator: '0001'
        });
        
        const character = await Character.create({
            user_id: user.id,
            level: 5,
            attack: 20,
            defense: 10,
            health: 100,
            max_health: 100
        });
        
        const monster = await Monster.create({
            name: 'Test Monster',
            type: 'normal',
            level: 3,
            health: 50,
            attack: 10,
            defense: 5,
            base_exp: 25,
            base_gold: 15,
            is_active: true
        });
        
        // Test combat simulation
        const result = await CombatEngine.simulateBattle(character, monster, 'fight');
        
        if (!result.result) throw new Error('Combat result missing');
        if (!['victory', 'defeat'].includes(result.result)) throw new Error('Invalid combat result');
        
        // Clean up
        await Monster.destroy({ where: { id: monster.id } });
        await Character.destroy({ where: { id: character.id } });
        await User.destroy({ where: { id: user.id } });
    }

    async testEconomyFlow() {
        const { User, Character, Item, UserItem } = require('../src/database/database');
        
        // Create test data
        const user = await User.create({
            discord_id: 'test-economy-user',
            username: 'economyuser',
            discriminator: '0001'
        });
        
        const character = await Character.create({
            user_id: user.id,
            gold: 1000,
            gems: 50
        });
        
        const item = await Item.create({
            name: 'Test Sword',
            type: 'weapon',
            rarity: 'common',
            attack_bonus: 10,
            base_price: 100
        });
        
        // Test item purchase simulation
        const userItem = await UserItem.create({
            user_id: user.id,
            item_id: item.id,
            quantity: 1,
            acquired_from: 'test'
        });
        
        // Test item stats
        const stats = item.getTotalStats();
        if (stats.attack !== 10) throw new Error('Item stats calculation failed');
        
        // Clean up
        await UserItem.destroy({ where: { id: userItem.id } });
        await Item.destroy({ where: { id: item.id } });
        await Character.destroy({ where: { id: character.id } });
        await User.destroy({ where: { id: user.id } });
    }

    async testVIPSystem() {
        const { User, Character, VipLevel } = require('../src/database/database');
        
        // Create test data
        const user = await User.create({
            discord_id: 'test-vip-user',
            username: 'vipuser',
            discriminator: '0001',
            total_spent: 100.00
        });
        
        const character = await Character.create({
            user_id: user.id,
            vip_level: 3
        });
        
        // Test VIP benefits
        const vipBenefits = character.getVipBenefits();
        if (!vipBenefits) throw new Error('VIP benefits not found');
        if (vipBenefits.level !== 3) throw new Error('VIP level mismatch');
        
        // Clean up
        await Character.destroy({ where: { id: character.id } });
        await User.destroy({ where: { id: user.id } });
    }

    parseJestOutput(output) {
        // Parse Jest output for test results
        const lines = output.split('\n');
        
        for (const line of lines) {
            if (line.includes('Tests:')) {
                const match = line.match(/(\d+) passed.*?(\d+) total/);
                if (match) {
                    this.testResults.passed = parseInt(match[1]);
                    this.testResults.total = parseInt(match[2]);
                    this.testResults.failed = this.testResults.total - this.testResults.passed;
                }
            }
            
            if (line.includes('All files')) {
                const coverageMatch = line.match(/(\d+\.?\d*)%/);
                if (coverageMatch) {
                    this.testResults.coverage = parseFloat(coverageMatch[1]);
                }
            }
        }
    }

    generateReport() {
        console.log('📋 Test Suite Report');
        console.log('===================');
        console.log(`⏱️  Duration: ${(this.testResults.duration / 1000).toFixed(2)}s`);
        console.log(`✅ Passed: ${this.testResults.passed}`);
        console.log(`❌ Failed: ${this.testResults.failed}`);
        console.log(`📊 Total: ${this.testResults.total}`);
        
        if (this.testResults.coverage !== null) {
            console.log(`📈 Coverage: ${this.testResults.coverage}%`);
        }
        
        const successRate = this.testResults.total > 0 
            ? ((this.testResults.passed / this.testResults.total) * 100).toFixed(1)
            : 0;
        
        console.log(`🎯 Success Rate: ${successRate}%`);
        
        if (this.testResults.failed === 0) {
            console.log('\n🎉 All tests passed! Your bot is ready for deployment.');
        } else {
            console.log('\n⚠️  Some tests failed. Please review and fix the issues before deployment.');
        }
    }
}

// CLI interface
if (require.main === module) {
    const args = process.argv.slice(2);
    const options = {
        unit: !args.includes('--no-unit'),
        integration: args.includes('--integration'),
        performance: args.includes('--performance')
    };
    
    const runner = new TestRunner();
    runner.runTests(options).catch(error => {
        console.error('Test runner failed:', error);
        process.exit(1);
    });
}

module.exports = TestRunner;

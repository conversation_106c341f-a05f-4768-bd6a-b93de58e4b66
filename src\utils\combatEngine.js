const { BattleLog } = require('../database/database');
const { performanceMonitor } = require('./performance');

class CombatEngine {
    static async simulateBattle(character, monster, battleType = 'fight') {
        performanceMonitor.startTimer('combat_simulation');

        try {
            // Get character stats (including equipment bonuses)
            const playerStats = character.getTotalStats();
        
        // Initialize battle state
        let playerHealth = character.health;
        let monsterHealth = monster.health;
        let turns = 0;
        let playerDamageDealt = 0;
        let playerDamageReceived = 0;
        let criticalHits = 0;
        
        const battleDetails = [];
        
        // Battle loop
        while (playerHealth > 0 && monsterHealth > 0 && turns < 50) {
            turns++;
            
            // Player turn
            const playerDamage = this.calculateDamage(
                playerStats.attack, 
                monster.defense, 
                playerStats.crit_rate, 
                playerStats.crit_damage
            );
            
            if (playerDamage.isCritical) {
                criticalHits++;
            }
            
            monsterHealth -= playerDamage.damage;
            playerDamageDealt += playerDamage.damage;
            
            battleDetails.push({
                turn: turns,
                actor: 'player',
                damage: playerDamage.damage,
                isCritical: playerDamage.isCritical,
                targetHealthAfter: Math.max(0, monsterHealth)
            });
            
            if (monsterHealth <= 0) break;
            
            // Monster turn
            const monsterDamage = this.calculateDamage(
                monster.attack, 
                playerStats.defense, 
                monster.crit_rate, 
                monster.crit_damage
            );
            
            playerHealth -= monsterDamage.damage;
            playerDamageReceived += monsterDamage.damage;
            
            battleDetails.push({
                turn: turns,
                actor: 'monster',
                damage: monsterDamage.damage,
                isCritical: monsterDamage.isCritical,
                targetHealthAfter: Math.max(0, playerHealth)
            });
        }
        
        // Determine result
        const result = playerHealth > 0 ? 'victory' : 'defeat';
        
        // Calculate rewards (only for victory)
        let rewards = { exp: 0, gold: 0, gems: 0 };
        let drops = [];
        
        if (result === 'victory') {
            rewards = monster.calculateRewards(character.level, character.vip_level);
            drops = monster.rollDrops(character.luck);
        }
        
        // Update character health
        character.health = Math.max(0, playerHealth);
        
        // Create battle log
        const battleLog = await BattleLog.create({
            user_id: character.user_id,
            monster_id: monster.id,
            battle_type: battleType,
            result: result,
            player_level: character.level,
            player_attack: playerStats.attack,
            player_defense: playerStats.defense,
            player_health_before: character.health + playerDamageReceived,
            player_health_after: character.health,
            monster_level: monster.level,
            monster_health: monster.health,
            monster_attack: monster.attack,
            monster_defense: monster.defense,
            turns_taken: turns,
            damage_dealt: playerDamageDealt,
            damage_received: playerDamageReceived,
            critical_hits: criticalHits,
            exp_gained: rewards.exp,
            gold_gained: rewards.gold,
            gems_gained: rewards.gems,
            items_dropped: drops,
            battle_details: battleDetails,
            duration_seconds: Math.floor(turns * 2) // Approximate 2 seconds per turn
        });
        
        const duration = performanceMonitor.endTimer('combat_simulation');
        performanceMonitor.logSlowOperation('combat_simulation', duration, 100);

        return {
            result: result,
            turns: turns,
            damageDealt: playerDamageDealt,
            damageReceived: playerDamageReceived,
            criticalHits: criticalHits,
            rewards: rewards,
            drops: drops,
            monster: monster,
            battleLog: battleLog,
            battleDetails: battleDetails
        };
        } catch (error) {
            performanceMonitor.endTimer('combat_simulation');
            throw error;
        }
    }
    
    static calculateDamage(attack, defense, critRate, critDamage) {
        // Base damage calculation
        const baseDamage = Math.max(1, attack - Math.floor(defense * 0.5));
        
        // Add random variance (±20%)
        const variance = 0.8 + (Math.random() * 0.4);
        let finalDamage = Math.floor(baseDamage * variance);
        
        // Check for critical hit
        const isCritical = Math.random() * 100 < critRate;
        if (isCritical) {
            finalDamage = Math.floor(finalDamage * (critDamage / 100));
        }
        
        return {
            damage: Math.max(1, finalDamage),
            isCritical: isCritical
        };
    }
    
    static async applyBattleRewards(character, rewards, drops) {
        // Apply currency rewards
        character.experience += rewards.exp;
        character.gold += rewards.gold;
        character.gems += rewards.gems;
        
        // Check for level up
        let leveledUp = false;
        while (character.canLevelUp()) {
            character.levelUp();
            leveledUp = true;
        }
        
        // Save character changes
        await character.save();
        
        // Handle item drops
        const droppedItems = [];
        if (drops && drops.length > 0) {
            const { UserItem, Item } = require('../database/database');
            
            for (const drop of drops) {
                const item = await Item.findByPk(drop.item_id);
                if (item) {
                    // Check if user already has this item
                    let userItem = await UserItem.findOne({
                        where: {
                            user_id: character.user_id,
                            item_id: drop.item_id
                        }
                    });
                    
                    if (userItem) {
                        // Stack if stackable, otherwise create new entry
                        if (item.type === 'consumable' || item.type === 'material') {
                            userItem.quantity += drop.quantity;
                            await userItem.save();
                        } else {
                            // Create new entry for equipment
                            userItem = await UserItem.create({
                                user_id: character.user_id,
                                item_id: drop.item_id,
                                quantity: drop.quantity,
                                acquired_from: 'monster'
                            });
                        }
                    } else {
                        // Create new item
                        userItem = await UserItem.create({
                            user_id: character.user_id,
                            item_id: drop.item_id,
                            quantity: drop.quantity,
                            acquired_from: 'monster'
                        });
                    }
                    
                    droppedItems.push({
                        item: item,
                        quantity: drop.quantity,
                        userItem: userItem
                    });
                }
            }
        }
        
        return {
            leveledUp: leveledUp,
            droppedItems: droppedItems
        };
    }
    
    static getRandomMonster(character, monsterType = 'normal') {
        const { Monster } = require('../database/database');
        
        // Find monsters suitable for character level
        return Monster.findAll({
            where: {
                type: monsterType,
                level: {
                    [require('sequelize').Op.between]: [
                        Math.max(1, character.level - 3),
                        character.level + 2
                    ]
                },
                min_level_requirement: {
                    [require('sequelize').Op.lte]: character.level
                },
                vip_requirement: {
                    [require('sequelize').Op.lte]: character.vip_level
                },
                is_active: true
            },
            order: require('sequelize').literal('RANDOM()'),
            limit: 1
        }).then(monsters => monsters[0] || null);
    }
    
    static async canCharacterFight(character) {
        // Check health
        if (character.health <= 0) {
            return { canFight: false, reason: 'You have no health left! Rest to recover.' };
        }
        
        // Check cooldown
        if (character.isOnCooldown('fight')) {
            const remaining = character.getCooldownRemaining('fight');
            return { canFight: false, reason: 'cooldown', remainingTime: remaining };
        }
        
        return { canFight: true };
    }
    
    static calculateVipCooldownReduction(vipLevel) {
        const { VipLevel } = require('../database/database');
        const vipBenefits = VipLevel.getBenefits(vipLevel);
        return vipBenefits.cooldown_reduction;
    }
}

module.exports = CombatEngine;

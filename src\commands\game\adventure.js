const { User, Character, Transaction } = require('../../database/database');
const GameEmbedBuilder = require('../../utils/embedBuilder');
const CombatEngine = require('../../utils/combatEngine');

module.exports = {
    data: {
        name: 'adventure',
        aliases: ['adv', 'explore', 'quest'],
        description: 'Go on an adventure to discover treasures and fight monsters',
        usage: '!adventure',
        cooldown: 300 // 5 minutes
    },
    async execute(message, args) {
        try {
            // Get user and character
            const user = await User.findByDiscordId(message.author.id);
            const character = await Character.findOne({
                where: { user_id: user.id }
            });
            
            // Check cooldown
            if (character.isOnCooldown('adventure')) {
                const remaining = character.getCooldownRemaining('adventure');
                const embed = GameEmbedBuilder.createCooldownEmbed('adventure', remaining);
                return message.reply({ embeds: [embed] });
            }
            
            // Check health
            if (character.health <= 0) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Cannot Adventure',
                    'You have no health left! Rest to recover before going on adventures.'
                );
                return message.reply({ embeds: [embed] });
            }
            
            // Send initial adventure message
            const startEmbed = GameEmbedBuilder.createInfoEmbed(
                '🗺️ Adventure Started!',
                'You venture into the unknown...\nSearching for treasures and monsters...'
            );
            const adventureMessage = await message.reply({ embeds: [startEmbed] });
            
            // Simulate adventure delay
            await new Promise(resolve => setTimeout(resolve, 2000));
            
            // Generate random adventure event
            const adventureResult = await this.generateAdventureEvent(character);
            
            // Apply VIP cooldown reduction
            const vipReduction = CombatEngine.calculateVipCooldownReduction(character.vip_level);
            const baseCooldown = 300000; // 5 minutes
            const actualCooldown = Math.floor(baseCooldown * (1 - vipReduction));
            
            // Update character cooldown
            character.last_adventure = new Date();
            
            // Process adventure result
            let resultEmbed;
            let levelUpMessage = '';
            
            switch (adventureResult.type) {
                case 'treasure':
                    resultEmbed = await this.handleTreasureEvent(character, adventureResult, user);
                    break;
                case 'monster':
                    const battleResult = await this.handleMonsterEvent(character, adventureResult);
                    resultEmbed = GameEmbedBuilder.createCombatResultEmbed(battleResult);
                    
                    if (battleResult.result === 'victory') {
                        const rewardResult = await CombatEngine.applyBattleRewards(
                            character, 
                            battleResult.rewards, 
                            battleResult.drops
                        );
                        
                        if (rewardResult.leveledUp) {
                            levelUpMessage = `\n🎉 **LEVEL UP!** You are now level ${character.level}!`;
                        }
                    }
                    break;
                case 'event':
                    resultEmbed = await this.handleSpecialEvent(character, adventureResult, user);
                    break;
                case 'nothing':
                    resultEmbed = this.handleNothingEvent(adventureResult);
                    break;
            }
            
            // Add level up message if applicable
            if (levelUpMessage) {
                const currentDescription = resultEmbed.data.description || '';
                resultEmbed.setDescription(currentDescription + levelUpMessage);
            }
            
            // Add VIP cooldown information
            if (actualCooldown < baseCooldown) {
                const savedTime = Math.floor((baseCooldown - actualCooldown) / 1000 / 60);
                resultEmbed.addFields({
                    name: '👑 VIP Benefit',
                    value: `Cooldown reduced by ${savedTime}m (${Math.round(vipReduction * 100)}% VIP bonus)`,
                    inline: false
                });
            }
            
            // Save character
            await character.save();
            
            // Update the adventure message with results
            await adventureMessage.edit({ embeds: [resultEmbed] });
            
        } catch (error) {
            console.error('Error in adventure command:', error);
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Adventure Error',
                'An error occurred during your adventure. Please try again.'
            );
            await message.reply({ embeds: [embed] });
        }
    },
    
    async generateAdventureEvent(character) {
        const random = Math.random();
        
        // Event probabilities based on character level and luck
        const luckBonus = character.luck / 100;
        
        if (random < 0.3 + luckBonus) {
            // Treasure event (30% + luck bonus)
            return this.generateTreasureEvent(character);
        } else if (random < 0.6) {
            // Monster encounter (30%)
            return this.generateMonsterEvent(character);
        } else if (random < 0.8) {
            // Special event (20%)
            return this.generateSpecialEvent(character);
        } else {
            // Nothing found (20% - luck bonus)
            return this.generateNothingEvent();
        }
    },
    
    generateTreasureEvent(character) {
        const treasures = [
            { name: 'Ancient Chest', goldMin: 50, goldMax: 200, gemsMin: 0, gemsMax: 5 },
            { name: 'Hidden Cache', goldMin: 100, goldMax: 300, gemsMin: 1, gemsMax: 3 },
            { name: 'Mysterious Pouch', goldMin: 20, goldMax: 100, gemsMin: 0, gemsMax: 2 },
            { name: 'Abandoned Treasure', goldMin: 150, goldMax: 400, gemsMin: 2, gemsMax: 8 }
        ];
        
        const treasure = treasures[Math.floor(Math.random() * treasures.length)];
        const levelMultiplier = 1 + (character.level * 0.1);
        
        const gold = Math.floor((treasure.goldMin + Math.random() * (treasure.goldMax - treasure.goldMin)) * levelMultiplier);
        const gems = treasure.gemsMin + Math.floor(Math.random() * (treasure.gemsMax - treasure.gemsMin + 1));
        
        return {
            type: 'treasure',
            treasure: treasure,
            rewards: { gold, gems, exp: Math.floor(gold * 0.5) }
        };
    },
    
    async generateMonsterEvent(character) {
        const monster = await CombatEngine.getRandomMonster(character, 'normal');
        return {
            type: 'monster',
            monster: monster
        };
    },
    
    generateSpecialEvent(character) {
        const events = [
            {
                name: 'Wise Hermit',
                description: 'You meet a wise hermit who shares knowledge with you.',
                rewards: { exp: character.level * 20, gold: 0, gems: 0 }
            },
            {
                name: 'Lucky Find',
                description: 'You stumble upon a lucky charm!',
                rewards: { exp: 0, gold: character.level * 10, gems: 1 }
            },
            {
                name: 'Ancient Shrine',
                description: 'You discover an ancient shrine that blesses you.',
                rewards: { exp: character.level * 15, gold: character.level * 5, gems: 0 }
            }
        ];
        
        const event = events[Math.floor(Math.random() * events.length)];
        
        return {
            type: 'event',
            event: event
        };
    },
    
    generateNothingEvent() {
        const messages = [
            'You search thoroughly but find nothing of value.',
            'Your adventure leads to a dead end.',
            'You explore for hours but come back empty-handed.',
            'The path you chose was uneventful.',
            'You find only rocks and dust on this adventure.'
        ];
        
        return {
            type: 'nothing',
            message: messages[Math.floor(Math.random() * messages.length)]
        };
    },
    
    async handleTreasureEvent(character, adventureResult, user) {
        const { treasure, rewards } = adventureResult;
        
        // Apply rewards
        character.experience += rewards.exp;
        character.gold += rewards.gold;
        character.gems += rewards.gems;
        
        // Check for level up
        while (character.canLevelUp()) {
            character.levelUp();
        }
        
        // Create transaction record
        await Transaction.createReward(
            user.id,
            rewards.gold,
            rewards.gems,
            character.gold,
            character.gems,
            `Adventure treasure: ${treasure.name}`,
            'adventure',
            null
        );
        
        const embed = GameEmbedBuilder.createSuccessEmbed(
            '💰 Treasure Found!',
            `You discovered a **${treasure.name}**!`
        );
        
        let rewardsText = '';
        if (rewards.exp > 0) rewardsText += `**EXP:** +${rewards.exp}\n`;
        if (rewards.gold > 0) rewardsText += `**Gold:** +${rewards.gold} 🪙\n`;
        if (rewards.gems > 0) rewardsText += `**Gems:** +${rewards.gems} 💎\n`;
        
        embed.addFields({
            name: '🎁 Rewards',
            value: rewardsText,
            inline: false
        });
        
        return embed;
    },
    
    async handleMonsterEvent(character, adventureResult) {
        const { monster } = adventureResult;
        
        if (!monster) {
            return GameEmbedBuilder.createErrorEmbed(
                'Adventure Error',
                'No suitable monsters found for encounter.'
            );
        }
        
        // Simulate battle
        return await CombatEngine.simulateBattle(character, monster, 'adventure');
    },
    
    async handleSpecialEvent(character, adventureResult, user) {
        const { event } = adventureResult;
        
        // Apply rewards
        character.experience += event.rewards.exp;
        character.gold += event.rewards.gold;
        character.gems += event.rewards.gems;
        
        // Check for level up
        while (character.canLevelUp()) {
            character.levelUp();
        }
        
        // Create transaction record if there are currency rewards
        if (event.rewards.gold > 0 || event.rewards.gems > 0) {
            await Transaction.createReward(
                user.id,
                event.rewards.gold,
                event.rewards.gems,
                character.gold,
                character.gems,
                `Adventure event: ${event.name}`,
                'adventure',
                null
            );
        }
        
        const embed = GameEmbedBuilder.createInfoEmbed(
            `✨ ${event.name}`,
            event.description
        );
        
        let rewardsText = '';
        if (event.rewards.exp > 0) rewardsText += `**EXP:** +${event.rewards.exp}\n`;
        if (event.rewards.gold > 0) rewardsText += `**Gold:** +${event.rewards.gold} 🪙\n`;
        if (event.rewards.gems > 0) rewardsText += `**Gems:** +${event.rewards.gems} 💎\n`;
        
        if (rewardsText) {
            embed.addFields({
                name: '🎁 Rewards',
                value: rewardsText,
                inline: false
            });
        }
        
        return embed;
    },
    
    handleNothingEvent(adventureResult) {
        return GameEmbedBuilder.createWarningEmbed(
            '🚶 Uneventful Adventure',
            adventureResult.message + '\n\nBetter luck next time!'
        );
    }
};

const fs = require('fs');
const path = require('path');

// Ensure logs directory exists
const logsDir = path.join(__dirname, '../../logs');
if (!fs.existsSync(logsDir)) {
    fs.mkdirSync(logsDir, { recursive: true });
}

class Logger {
    constructor() {
        this.logFile = path.join(logsDir, `bot-${new Date().toISOString().split('T')[0]}.log`);
    }

    formatMessage(level, message, ...args) {
        const timestamp = new Date().toISOString();
        const formattedArgs = args.length > 0 ? ' ' + args.map(arg => 
            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
        ).join(' ') : '';
        
        return `[${timestamp}] [${level.toUpperCase()}] ${message}${formattedArgs}`;
    }

    writeToFile(formattedMessage) {
        try {
            fs.appendFileSync(this.logFile, formattedMessage + '\n');
        } catch (error) {
            console.error('Failed to write to log file:', error);
        }
    }

    info(message, ...args) {
        const formatted = this.formatMessage('info', message, ...args);
        console.log('\x1b[36m%s\x1b[0m', formatted); // Cyan
        this.writeToFile(formatted);
    }

    warn(message, ...args) {
        const formatted = this.formatMessage('warn', message, ...args);
        console.log('\x1b[33m%s\x1b[0m', formatted); // Yellow
        this.writeToFile(formatted);
    }

    error(message, ...args) {
        const formatted = this.formatMessage('error', message, ...args);
        console.log('\x1b[31m%s\x1b[0m', formatted); // Red
        this.writeToFile(formatted);
    }

    debug(message, ...args) {
        if (process.env.NODE_ENV === 'development') {
            const formatted = this.formatMessage('debug', message, ...args);
            console.log('\x1b[35m%s\x1b[0m', formatted); // Magenta
            this.writeToFile(formatted);
        }
    }

    success(message, ...args) {
        const formatted = this.formatMessage('success', message, ...args);
        console.log('\x1b[32m%s\x1b[0m', formatted); // Green
        this.writeToFile(formatted);
    }
}

module.exports = new Logger();

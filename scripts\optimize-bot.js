#!/usr/bin/env node

const { sequelize } = require('../src/database/database');
const { performanceMonitor } = require('../src/utils/performance');

class BotOptimizer {
    constructor() {
        this.optimizations = [];
    }

    async runOptimizations() {
        console.log('🚀 Running Bot Optimizations\n');
        
        try {
            await this.optimizeDatabase();
            await this.optimizeCache();
            await this.optimizeMemory();
            await this.generateOptimizationReport();
            
            console.log('\n✅ All optimizations completed successfully!');
            
        } catch (error) {
            console.error('❌ Optimization failed:', error);
            process.exit(1);
        } finally {
            await sequelize.close();
        }
    }

    async optimizeDatabase() {
        console.log('🗄️ Optimizing Database...');
        
        try {
            // Analyze table sizes
            const tableStats = await this.analyzeTableSizes();
            console.log('  📊 Table analysis completed');
            
            // Optimize indexes
            await this.optimizeIndexes();
            console.log('  🔍 Index optimization completed');
            
            // Clean up old data
            await this.cleanupOldData();
            console.log('  🧹 Data cleanup completed');
            
            // Update table statistics
            await this.updateTableStatistics();
            console.log('  📈 Statistics updated');
            
            this.optimizations.push({
                type: 'database',
                status: 'completed',
                details: tableStats
            });
            
        } catch (error) {
            console.error('  ❌ Database optimization failed:', error);
            this.optimizations.push({
                type: 'database',
                status: 'failed',
                error: error.message
            });
        }
    }

    async analyzeTableSizes() {
        const query = `
            SELECT 
                schemaname,
                tablename,
                attname,
                n_distinct,
                correlation
            FROM pg_stats 
            WHERE schemaname = 'public'
            ORDER BY tablename, attname;
        `;
        
        try {
            const [results] = await sequelize.query(query);
            return results;
        } catch (error) {
            console.warn('  ⚠️ Could not analyze table sizes (PostgreSQL specific)');
            return [];
        }
    }

    async optimizeIndexes() {
        // Check for missing indexes on frequently queried columns
        const indexChecks = [
            {
                table: 'battle_logs',
                column: 'created_at',
                reason: 'Frequently queried for recent battles'
            },
            {
                table: 'transactions',
                column: 'user_id, created_at',
                reason: 'User transaction history queries'
            },
            {
                table: 'user_items',
                column: 'user_id, is_equipped',
                reason: 'Equipment queries'
            }
        ];

        for (const check of indexChecks) {
            try {
                const indexName = `idx_${check.table}_${check.column.replace(/[, ]/g, '_')}`;
                const query = `
                    CREATE INDEX IF NOT EXISTS ${indexName} 
                    ON ${check.table} (${check.column});
                `;
                
                await sequelize.query(query);
                console.log(`    ✅ Index created/verified: ${indexName}`);
                
            } catch (error) {
                console.warn(`    ⚠️ Could not create index for ${check.table}.${check.column}:`, error.message);
            }
        }
    }

    async cleanupOldData() {
        const cleanupTasks = [
            {
                name: 'Old Battle Logs',
                query: `DELETE FROM battle_logs WHERE created_at < NOW() - INTERVAL '30 days'`,
                description: 'Remove battle logs older than 30 days'
            },
            {
                name: 'Expired Transactions',
                query: `DELETE FROM transactions WHERE created_at < NOW() - INTERVAL '90 days' AND type = 'temp'`,
                description: 'Remove temporary transactions older than 90 days'
            },
            {
                name: 'Inactive Users',
                query: `UPDATE users SET is_banned = false WHERE ban_expires < NOW()`,
                description: 'Clear expired bans'
            }
        ];

        for (const task of cleanupTasks) {
            try {
                const [results, metadata] = await sequelize.query(task.query);
                const affectedRows = metadata.rowCount || 0;
                console.log(`    ✅ ${task.name}: ${affectedRows} rows affected`);
                
            } catch (error) {
                console.warn(`    ⚠️ ${task.name} failed:`, error.message);
            }
        }
    }

    async updateTableStatistics() {
        try {
            await sequelize.query('ANALYZE;');
            console.log('    ✅ Table statistics updated');
        } catch (error) {
            console.warn('    ⚠️ Could not update table statistics:', error.message);
        }
    }

    async optimizeCache() {
        console.log('💾 Optimizing Cache...');
        
        try {
            const { gameCache } = require('../src/utils/performance');
            
            // Get cache statistics
            const cacheSize = gameCache.size();
            console.log(`  📊 Current cache size: ${cacheSize} entries`);
            
            // Clean up expired entries
            gameCache.cleanup();
            const newCacheSize = gameCache.size();
            const cleaned = cacheSize - newCacheSize;
            
            console.log(`  🧹 Cleaned ${cleaned} expired cache entries`);
            console.log(`  📊 New cache size: ${newCacheSize} entries`);
            
            this.optimizations.push({
                type: 'cache',
                status: 'completed',
                details: {
                    originalSize: cacheSize,
                    newSize: newCacheSize,
                    cleaned: cleaned
                }
            });
            
        } catch (error) {
            console.error('  ❌ Cache optimization failed:', error);
            this.optimizations.push({
                type: 'cache',
                status: 'failed',
                error: error.message
            });
        }
    }

    async optimizeMemory() {
        console.log('🧠 Optimizing Memory...');
        
        try {
            // Get initial memory usage
            const initialMemory = process.memoryUsage();
            
            // Force garbage collection if available
            if (global.gc) {
                global.gc();
                console.log('  🗑️ Garbage collection triggered');
            } else {
                console.log('  ⚠️ Garbage collection not available (run with --expose-gc)');
            }
            
            // Get final memory usage
            const finalMemory = process.memoryUsage();
            
            const memoryFreed = {
                rss: initialMemory.rss - finalMemory.rss,
                heapUsed: initialMemory.heapUsed - finalMemory.heapUsed,
                heapTotal: initialMemory.heapTotal - finalMemory.heapTotal
            };
            
            console.log(`  📊 Memory usage:`);
            console.log(`    RSS: ${(finalMemory.rss / 1024 / 1024).toFixed(2)} MB`);
            console.log(`    Heap Used: ${(finalMemory.heapUsed / 1024 / 1024).toFixed(2)} MB`);
            console.log(`    Heap Total: ${(finalMemory.heapTotal / 1024 / 1024).toFixed(2)} MB`);
            
            if (memoryFreed.heapUsed > 0) {
                console.log(`  ♻️ Freed ${(memoryFreed.heapUsed / 1024 / 1024).toFixed(2)} MB heap memory`);
            }
            
            this.optimizations.push({
                type: 'memory',
                status: 'completed',
                details: {
                    initial: initialMemory,
                    final: finalMemory,
                    freed: memoryFreed
                }
            });
            
        } catch (error) {
            console.error('  ❌ Memory optimization failed:', error);
            this.optimizations.push({
                type: 'memory',
                status: 'failed',
                error: error.message
            });
        }
    }

    async generateOptimizationReport() {
        console.log('\n📋 Optimization Report');
        console.log('======================');
        
        const completed = this.optimizations.filter(opt => opt.status === 'completed').length;
        const failed = this.optimizations.filter(opt => opt.status === 'failed').length;
        
        console.log(`✅ Completed: ${completed}`);
        console.log(`❌ Failed: ${failed}`);
        console.log(`📊 Total: ${this.optimizations.length}`);
        
        // Detailed results
        for (const optimization of this.optimizations) {
            console.log(`\n${optimization.status === 'completed' ? '✅' : '❌'} ${optimization.type.toUpperCase()}`);
            
            if (optimization.status === 'failed') {
                console.log(`  Error: ${optimization.error}`);
            } else if (optimization.details) {
                if (optimization.type === 'cache') {
                    console.log(`  Cleaned: ${optimization.details.cleaned} entries`);
                    console.log(`  Size: ${optimization.details.originalSize} → ${optimization.details.newSize}`);
                } else if (optimization.type === 'memory') {
                    const freed = optimization.details.freed.heapUsed;
                    if (freed > 0) {
                        console.log(`  Memory freed: ${(freed / 1024 / 1024).toFixed(2)} MB`);
                    }
                } else if (optimization.type === 'database') {
                    console.log(`  Tables analyzed: ${optimization.details.length}`);
                }
            }
        }
        
        // Performance metrics
        console.log('\n⚡ Performance Metrics:');
        const metrics = performanceMonitor.getAllMetrics();
        
        if (Object.keys(metrics).length > 0) {
            for (const [name, metric] of Object.entries(metrics)) {
                console.log(`  ${name}: ${metric.average}ms avg (${metric.count} calls)`);
            }
        } else {
            console.log('  No performance metrics available');
        }
        
        // Recommendations
        console.log('\n💡 Recommendations:');
        
        if (failed > 0) {
            console.log('  • Review failed optimizations and fix underlying issues');
        }
        
        console.log('  • Run optimizations regularly (weekly recommended)');
        console.log('  • Monitor performance metrics during peak usage');
        console.log('  • Consider upgrading hardware if memory usage is consistently high');
        console.log('  • Implement database connection pooling for high-traffic scenarios');
    }
}

// CLI interface
if (require.main === module) {
    const optimizer = new BotOptimizer();
    optimizer.runOptimizations().catch(error => {
        console.error('Optimizer failed:', error);
        process.exit(1);
    });
}

module.exports = BotOptimizer;

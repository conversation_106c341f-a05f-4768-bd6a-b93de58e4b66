module.exports = {
    up: async (queryInterface, Sequelize) => {
        // Convert arrays to JSON strings for SQLite compatibility
        const monsters = [
            // Level 1-5 Monsters
            {
                name: 'Forest Slime',
                description: 'A harmless green blob that bounces around',
                type: 'normal',
                level: 1,
                health: 25,
                attack: 3,
                defense: 1,
                base_exp: 10,
                base_gold: 5,
                emoji: '🟢',
                location: 'forest',
                drop_table: JSON.stringify([
                    { item_id: 16, chance: 0.3, quantity: 1 }, // Iron Ore
                    { item_id: 17, chance: 0.2, quantity: 1 }  // Leather Scraps
                ]),
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                name: 'Wild Rabbit',
                description: 'A quick and agile forest creature',
                type: 'normal',
                level: 2,
                health: 35,
                attack: 5,
                defense: 2,
                base_exp: 15,
                base_gold: 8,
                emoji: '🐰',
                location: 'forest',
                drop_table: [
                    { item_id: 17, chance: 0.4, quantity: 2 }, // Leather Scraps
                    { item_id: 14, chance: 0.1, quantity: 1 }  // Health Potion
                ],
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                name: 'Goblin Scout',
                description: 'A small but cunning goblin warrior',
                type: 'normal',
                level: 3,
                health: 50,
                attack: 8,
                defense: 3,
                crit_rate: 8.00,
                base_exp: 25,
                base_gold: 12,
                emoji: '👺',
                location: 'forest',
                drop_table: [
                    { item_id: 1, chance: 0.05, quantity: 1 }, // Rusty Sword
                    { item_id: 9, chance: 0.08, quantity: 1 }, // Worn Boots
                    { item_id: 16, chance: 0.3, quantity: 2 }  // Iron Ore
                ],
                created_at: new Date(),
                updated_at: new Date()
            },
            
            // Level 5-10 Monsters
            {
                name: 'Cave Bat',
                description: 'A swift flying creature that dwells in dark caves',
                type: 'normal',
                level: 5,
                health: 40,
                attack: 12,
                defense: 2,
                crit_rate: 15.00,
                base_exp: 35,
                base_gold: 18,
                emoji: '🦇',
                location: 'cave',
                min_level_requirement: 5,
                drop_table: [
                    { item_id: 14, chance: 0.2, quantity: 1 }, // Health Potion
                    { item_id: 15, chance: 0.15, quantity: 1 } // Mana Potion
                ],
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                name: 'Stone Golem',
                description: 'A massive creature made of living rock',
                type: 'elite',
                level: 8,
                health: 120,
                attack: 15,
                defense: 12,
                base_exp: 80,
                base_gold: 40,
                gem_reward: 1,
                emoji: '🗿',
                location: 'cave',
                min_level_requirement: 7,
                drop_table: [
                    { item_id: 16, chance: 0.6, quantity: 5 }, // Iron Ore
                    { item_id: 6, chance: 0.1, quantity: 1 }   // Chain Mail
                ],
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                name: 'Orc Warrior',
                description: 'A fierce orc wielding a crude axe',
                type: 'normal',
                level: 10,
                health: 85,
                attack: 20,
                defense: 8,
                crit_rate: 10.00,
                crit_damage: 180.00,
                base_exp: 60,
                base_gold: 30,
                emoji: '👹',
                location: 'cave',
                min_level_requirement: 8,
                drop_table: [
                    { item_id: 2, chance: 0.08, quantity: 1 }, // Iron Sword
                    { item_id: 5, chance: 0.12, quantity: 1 }, // Leather Armor
                    { item_id: 11, chance: 0.06, quantity: 1 }  // Copper Ring
                ],
                created_at: new Date(),
                updated_at: new Date()
            },
            
            // Level 15-20 Monsters
            {
                name: 'Ice Wolf',
                description: 'A fierce wolf adapted to frozen environments',
                type: 'normal',
                level: 15,
                health: 110,
                attack: 28,
                defense: 10,
                crit_rate: 20.00,
                base_exp: 100,
                base_gold: 50,
                emoji: '🐺',
                location: 'mountain',
                min_level_requirement: 12,
                drop_table: [
                    { item_id: 17, chance: 0.5, quantity: 3 }, // Leather Scraps
                    { item_id: 10, chance: 0.15, quantity: 1 }  // Leather Boots
                ],
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                name: 'Frost Giant',
                description: 'A towering giant wielding the power of ice',
                type: 'boss',
                level: 20,
                health: 400,
                attack: 45,
                defense: 25,
                crit_rate: 15.00,
                crit_damage: 200.00,
                base_exp: 300,
                base_gold: 150,
                gem_reward: 5,
                emoji: '🧊',
                location: 'mountain',
                min_level_requirement: 18,
                drop_table: [
                    { item_id: 3, chance: 0.2, quantity: 1 },  // Steel Blade
                    { item_id: 12, chance: 0.15, quantity: 1 }, // Silver Ring
                    { item_id: 14, chance: 0.8, quantity: 3 }   // Health Potion
                ],
                rare_drop_table: [
                    { item_id: 3, chance: 0.05, quantity: 1, upgrade_level: 1 } // Steel Blade +1
                ],
                created_at: new Date(),
                updated_at: new Date()
            },
            
            // High Level Monsters
            {
                name: 'Shadow Assassin',
                description: 'A deadly assassin that strikes from the shadows',
                type: 'elite',
                level: 25,
                health: 180,
                attack: 55,
                defense: 15,
                crit_rate: 35.00,
                crit_damage: 250.00,
                base_exp: 200,
                base_gold: 100,
                gem_reward: 2,
                emoji: '🥷',
                location: 'dungeon',
                min_level_requirement: 22,
                drop_table: [
                    { item_id: 11, chance: 0.3, quantity: 1 }, // Copper Ring
                    { item_id: 12, chance: 0.2, quantity: 1 }, // Silver Ring
                    { item_id: 13, chance: 0.25, quantity: 1 } // Wooden Pendant
                ],
                created_at: new Date(),
                updated_at: new Date()
            },
            {
                name: 'Ancient Dragon',
                description: 'A legendary dragon of immense power',
                type: 'raid_boss',
                level: 50,
                health: 2000,
                attack: 120,
                defense: 60,
                crit_rate: 25.00,
                crit_damage: 300.00,
                base_exp: 1500,
                base_gold: 1000,
                gem_reward: 50,
                emoji: '🐉',
                location: 'dungeon',
                min_level_requirement: 45,
                vip_requirement: 3,
                drop_table: [
                    { item_id: 3, chance: 0.8, quantity: 1 },  // Steel Blade
                    { item_id: 6, chance: 0.6, quantity: 1 },  // Chain Mail
                    { item_id: 12, chance: 0.4, quantity: 1 }, // Silver Ring
                    { item_id: 14, chance: 1.0, quantity: 10 } // Health Potion
                ],
                rare_drop_table: [
                    { item_id: 3, chance: 0.1, quantity: 1, upgrade_level: 3 }, // Steel Blade +3
                    { item_id: 12, chance: 0.05, quantity: 1, upgrade_level: 2 } // Silver Ring +2
                ],
                special_abilities: [
                    { name: 'Fire Breath', description: 'Deals massive fire damage', cooldown: 3 },
                    { name: 'Wing Buffet', description: 'Knocks back enemies', cooldown: 2 }
                ],
                created_at: new Date(),
                updated_at: new Date()
            },
            
            // Event Monsters
            {
                name: 'Holiday Elf',
                description: 'A festive elf spreading holiday cheer',
                type: 'normal',
                level: 10,
                health: 60,
                attack: 15,
                defense: 5,
                base_exp: 80,
                base_gold: 50,
                gem_reward: 3,
                emoji: '🧝',
                location: 'event',
                is_event_monster: true,
                drop_table: [
                    { item_id: 14, chance: 0.5, quantity: 2 }, // Health Potion
                    { item_id: 15, chance: 0.3, quantity: 1 }  // Mana Potion
                ],
                created_at: new Date(),
                updated_at: new Date()
            }
        ];

        // Convert arrays to JSON strings for SQLite
        const processedMonsters = monsters.map(monster => ({
            ...monster,
            drop_table: typeof monster.drop_table === 'string' ? monster.drop_table : JSON.stringify(monster.drop_table || []),
            rare_drop_table: typeof monster.rare_drop_table === 'string' ? monster.rare_drop_table : JSON.stringify(monster.rare_drop_table || []),
            special_abilities: typeof monster.special_abilities === 'string' ? monster.special_abilities : JSON.stringify(monster.special_abilities || [])
        }));

        await queryInterface.bulkInsert('monsters', processedMonsters);
    },

    down: async (queryInterface, Sequelize) => {
        await queryInterface.bulkDelete('monsters', null, {});
    }
};

const { DataTypes } = require('sequelize');

module.exports = {
    up: async (queryInterface, Sequelize) => {
        // Create users table
        await queryInterface.createTable('users', {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            discord_id: {
                type: DataTypes.STRING,
                allowNull: false,
                unique: true
            },
            username: {
                type: DataTypes.STRING,
                allowNull: false
            },
            discriminator: {
                type: DataTypes.STRING,
                allowNull: true,
                validate: {
                    len: [1, 4] // Support both old (#1234) and new (0) Discord systems
                }
            },
            avatar_url: {
                type: DataTypes.STRING,
                allowNull: true
            },
            is_banned: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            ban_reason: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            ban_expires_at: {
                type: DataTypes.DATE,
                allowNull: true
            },
            referral_code: {
                type: DataTypes.STRING(10),
                allowNull: true,
                unique: true
            },
            referred_by: {
                type: DataTypes.INTEGER,
                allowNull: true,
                references: {
                    model: 'users',
                    key: 'id'
                }
            },
            total_spent: {
                type: DataTypes.DECIMAL(10, 2),
                defaultValue: 0.00
            },
            settings: {
                type: DataTypes.TEXT,
                defaultValue: '{}'
            },
            last_seen: {
                type: DataTypes.DATE,
                defaultValue: DataTypes.NOW
            },
            created_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            },
            updated_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            }
        });

        // Create characters table
        await queryInterface.createTable('characters', {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            user_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'users',
                    key: 'id'
                },
                onDelete: 'CASCADE'
            },
            level: {
                type: DataTypes.INTEGER,
                defaultValue: 1
            },
            experience: {
                type: DataTypes.BIGINT,
                defaultValue: 0
            },
            prestige_level: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            health: {
                type: DataTypes.INTEGER,
                defaultValue: 100
            },
            max_health: {
                type: DataTypes.INTEGER,
                defaultValue: 100
            },
            attack: {
                type: DataTypes.INTEGER,
                defaultValue: 10
            },
            defense: {
                type: DataTypes.INTEGER,
                defaultValue: 5
            },
            crit_rate: {
                type: DataTypes.DECIMAL(5, 2),
                defaultValue: 5.00
            },
            crit_damage: {
                type: DataTypes.DECIMAL(6, 2),
                defaultValue: 150.00
            },
            luck: {
                type: DataTypes.DECIMAL(5, 2),
                defaultValue: 1.00
            },
            gold: {
                type: DataTypes.BIGINT,
                defaultValue: 100
            },
            gems: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            vip_level: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            weapon_id: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            armor_id: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            helmet_id: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            boots_id: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            ring_id: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            necklace_id: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            last_fight: {
                type: DataTypes.DATE,
                allowNull: true
            },
            last_adventure: {
                type: DataTypes.DATE,
                allowNull: true
            },
            last_boss_fight: {
                type: DataTypes.DATE,
                allowNull: true
            },
            last_daily: {
                type: DataTypes.DATE,
                allowNull: true
            },
            daily_streak: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            afk_start_time: {
                type: DataTypes.DATE,
                allowNull: true
            },
            afk_location: {
                type: DataTypes.STRING,
                allowNull: true
            },
            created_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            },
            updated_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            }
        });

        // Create vip_levels table
        await queryInterface.createTable('vip_levels', {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            level: {
                type: DataTypes.INTEGER,
                allowNull: false,
                unique: true
            },
            name: {
                type: DataTypes.STRING,
                allowNull: false
            },
            required_spending: {
                type: DataTypes.DECIMAL(10, 2),
                allowNull: false
            },
            cooldown_reduction: {
                type: DataTypes.DECIMAL(3, 2),
                defaultValue: 0.00
            },
            gold_bonus: {
                type: DataTypes.DECIMAL(3, 2),
                defaultValue: 0.00
            },
            daily_gems: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            gacha_discount: {
                type: DataTypes.DECIMAL(3, 2),
                defaultValue: 0.00
            },
            special_features: {
                type: DataTypes.JSONB,
                defaultValue: []
            },
            color: {
                type: DataTypes.STRING(7),
                defaultValue: '#FFFFFF'
            },
            icon: {
                type: DataTypes.STRING,
                allowNull: true
            },
            description: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            created_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            },
            updated_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            }
        });

        // Create items table
        await queryInterface.createTable('items', {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            name: {
                type: DataTypes.STRING,
                allowNull: false
            },
            description: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            type: {
                type: DataTypes.ENUM('weapon', 'armor', 'helmet', 'boots', 'ring', 'necklace', 'consumable', 'material'),
                allowNull: false
            },
            rarity: {
                type: DataTypes.ENUM('common', 'rare', 'epic', 'legendary', 'mythic'),
                allowNull: false,
                defaultValue: 'common'
            },
            level_requirement: {
                type: DataTypes.INTEGER,
                defaultValue: 1
            },
            attack_bonus: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            defense_bonus: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            health_bonus: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            crit_rate_bonus: {
                type: DataTypes.DECIMAL(5, 2),
                defaultValue: 0.00
            },
            crit_damage_bonus: {
                type: DataTypes.DECIMAL(6, 2),
                defaultValue: 0.00
            },
            luck_bonus: {
                type: DataTypes.DECIMAL(5, 2),
                defaultValue: 0.00
            },
            base_price: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            sell_price: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            gem_price: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            is_tradeable: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            is_sellable: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            is_upgradeable: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            max_upgrade_level: {
                type: DataTypes.INTEGER,
                defaultValue: 10
            },
            icon: {
                type: DataTypes.STRING,
                allowNull: true
            },
            color: {
                type: DataTypes.STRING(7),
                allowNull: true
            },
            special_effects: {
                type: DataTypes.JSONB,
                defaultValue: []
            },
            drop_sources: {
                type: DataTypes.JSONB,
                defaultValue: []
            },
            drop_rate: {
                type: DataTypes.DECIMAL(5, 4),
                defaultValue: 0.0000
            },
            created_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            },
            updated_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            }
        });

        // Add indexes
        await queryInterface.addIndex('users', ['discord_id']);
        await queryInterface.addIndex('characters', ['user_id']);
        await queryInterface.addIndex('characters', ['level']);
        await queryInterface.addIndex('vip_levels', ['level']);
        await queryInterface.addIndex('items', ['type']);
        await queryInterface.addIndex('items', ['rarity']);
        await queryInterface.addIndex('items', ['level_requirement']);
    },

    down: async (queryInterface, Sequelize) => {
        await queryInterface.dropTable('items');
        await queryInterface.dropTable('vip_levels');
        await queryInterface.dropTable('characters');
        await queryInterface.dropTable('users');
    }
};

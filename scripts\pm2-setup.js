#!/usr/bin/env node

const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

class PM2Setup {
    constructor() {
        this.logDir = path.join(__dirname, '../logs');
    }

    async setupPM2() {
        console.log('🚀 Setting up PM2 for AFK Game Bot...\n');

        try {
            // Check if PM2 is installed
            await this.checkPM2Installation();
            
            // Create logs directory
            await this.createLogsDirectory();
            
            // Initialize database if needed
            await this.initializeDatabase();
            
            // Start the bot with PM2
            await this.startBot();
            
            console.log('\n✅ PM2 setup completed successfully!');
            console.log('\n📋 Useful PM2 commands:');
            console.log('  npm run pm2:status   - Check bot status');
            console.log('  npm run pm2:logs     - View bot logs');
            console.log('  npm run pm2:restart  - Restart bot');
            console.log('  npm run pm2:stop     - Stop bot');
            console.log('  npm run pm2:monit    - Monitor bot');
            
        } catch (error) {
            console.error('❌ PM2 setup failed:', error.message);
            process.exit(1);
        }
    }

    async checkPM2Installation() {
        console.log('🔍 Checking PM2 installation...');
        
        return new Promise((resolve, reject) => {
            exec('pm2 --version', (error, stdout, stderr) => {
                if (error) {
                    console.log('❌ PM2 not found. Installing PM2...');
                    this.installPM2().then(resolve).catch(reject);
                } else {
                    console.log(`✅ PM2 found: v${stdout.trim()}`);
                    resolve();
                }
            });
        });
    }

    async installPM2() {
        return new Promise((resolve, reject) => {
            console.log('📦 Installing PM2 globally...');
            exec('npm install -g pm2', (error, stdout, stderr) => {
                if (error) {
                    reject(new Error(`Failed to install PM2: ${error.message}`));
                } else {
                    console.log('✅ PM2 installed successfully');
                    resolve();
                }
            });
        });
    }

    async createLogsDirectory() {
        console.log('📁 Creating logs directory...');
        
        if (!fs.existsSync(this.logDir)) {
            fs.mkdirSync(this.logDir, { recursive: true });
            console.log('✅ Logs directory created');
        } else {
            console.log('✅ Logs directory already exists');
        }
    }

    async initializeDatabase() {
        console.log('🗄️ Checking database...');
        
        const dbPath = path.join(__dirname, '../database.sqlite');
        
        if (!fs.existsSync(dbPath)) {
            console.log('📊 Initializing database...');
            
            return new Promise((resolve, reject) => {
                exec('npm run db:init', (error, stdout, stderr) => {
                    if (error) {
                        console.warn('⚠️ Database initialization failed, but continuing...');
                        console.warn('You may need to run "npm run db:init" manually');
                        resolve(); // Don't fail the setup
                    } else {
                        console.log('✅ Database initialized');
                        resolve();
                    }
                });
            });
        } else {
            console.log('✅ Database already exists');
        }
    }

    async startBot() {
        console.log('🤖 Starting bot with PM2...');
        
        return new Promise((resolve, reject) => {
            // Stop existing instance if running
            exec('pm2 delete afk-game-bot', () => {
                // Start new instance
                exec('pm2 start ecosystem.config.js', (error, stdout, stderr) => {
                    if (error) {
                        reject(new Error(`Failed to start bot: ${error.message}`));
                    } else {
                        console.log('✅ Bot started with PM2');
                        
                        // Save PM2 configuration
                        exec('pm2 save', (saveError) => {
                            if (saveError) {
                                console.warn('⚠️ Failed to save PM2 configuration');
                            } else {
                                console.log('✅ PM2 configuration saved');
                            }
                            resolve();
                        });
                    }
                });
            });
        });
    }

    async showStatus() {
        console.log('\n📊 Current PM2 Status:');
        
        return new Promise((resolve) => {
            exec('pm2 status', (error, stdout, stderr) => {
                if (error) {
                    console.log('❌ Could not get PM2 status');
                } else {
                    console.log(stdout);
                }
                resolve();
            });
        });
    }
}

// CLI interface
if (require.main === module) {
    const setup = new PM2Setup();
    setup.setupPM2().then(() => {
        setup.showStatus();
    }).catch(error => {
        console.error('Setup failed:', error);
        process.exit(1);
    });
}

module.exports = PM2Setup;

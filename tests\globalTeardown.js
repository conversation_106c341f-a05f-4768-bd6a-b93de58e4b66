const { sequelize } = require('../src/database/database');

module.exports = async () => {
    console.log('🧹 Cleaning up test environment...');
    
    try {
        // Close database connection
        await sequelize.close();
        console.log('✅ Test database connection closed');
        
    } catch (error) {
        console.error('❌ Test cleanup failed:', error);
        // Don't throw error in teardown to avoid masking test failures
    }
};

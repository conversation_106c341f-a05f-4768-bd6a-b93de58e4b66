const { sequelize } = require('../src/database/database');

module.exports = async () => {
    console.log('🔧 Setting up test environment...');
    
    try {
        // Test database connection
        await sequelize.authenticate();
        console.log('✅ Test database connection established');
        
        // Create test database schema
        await sequelize.sync({ force: true });
        console.log('✅ Test database schema created');
        
        // Set global test variables
        global.__TEST_DATABASE_READY__ = true;
        
    } catch (error) {
        console.error('❌ Test setup failed:', error);
        throw error;
    }
};

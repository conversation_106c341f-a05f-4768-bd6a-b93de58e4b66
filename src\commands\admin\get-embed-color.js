const { SlashCommandBuilder } = require('discord.js');
const GameEmbedBuilder = require('../../utils/embedBuilder');
const fs = require('fs');
const path = require('path');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('get-embed-color')
        .setDescription('Show current embed color settings'),

    async execute(interaction) {
        // Check if user is admin
        const adminIds = process.env.ADMIN_IDS?.split(',') || [];
        if (!adminIds.includes(interaction.user.id)) {
            return await interaction.reply({
                content: '❌ You do not have permission to use this command.',
                ephemeral: true
            });
        }

        try {
            // Read current color from embedBuilder.js
            const embedBuilderPath = path.join(__dirname, '../../utils/embedBuilder.js');
            const content = fs.readFileSync(embedBuilderPath, 'utf8');
            
            // Extract DEFAULT_COLOR value
            const colorMatch = content.match(/const\s+DEFAULT_COLOR\s*=\s*([^;]+);/);
            let currentColor = '0x8D689E'; // fallback
            
            if (colorMatch) {
                currentColor = colorMatch[1].trim();
            }

            // Convert to different formats
            let colorDecimal, colorHex;
            
            if (currentColor.startsWith('0x')) {
                colorDecimal = parseInt(currentColor, 16);
                colorHex = '#' + currentColor.slice(2).toUpperCase();
            } else {
                colorDecimal = parseInt(currentColor);
                colorHex = '#' + colorDecimal.toString(16).toUpperCase().padStart(6, '0');
            }

            // Create info embed with current color
            const embed = GameEmbedBuilder.createInfoEmbed(
                '🎨 Current Embed Color Settings',
                `**Current Color Information:**\n\n` +
                `🎯 **Hex Code:** ${colorHex}\n` +
                `🔢 **Decimal:** ${colorDecimal}\n` +
                `⚙️ **Raw Value:** ${currentColor}\n\n` +
                `**Color Preview:**\n` +
                `This embed is using the current default color!\n\n` +
                `**Usage:**\n` +
                `Use \`/set-embed-color\` to change the color.\n` +
                `Example: \`/set-embed-color color:#FF5733\``
            );

            // Force current color
            embed.setColor(colorDecimal);

            // Add color preview field
            embed.addFields([
                {
                    name: '🌈 Color Categories',
                    value: 
                        '🟢 **Success:** Green (unchanged)\n' +
                        '🔴 **Error:** Red (unchanged)\n' +
                        '🟡 **Warning:** Yellow (unchanged)\n' +
                        `🎨 **Info/Default:** ${colorHex} (customizable)`,
                    inline: false
                },
                {
                    name: '📋 Quick Color Codes',
                    value: 
                        '`#FF0000` - Red\n' +
                        '`#00FF00` - Green\n' +
                        '`#0099FF` - Blue\n' +
                        '`#8D689E` - Purple (default)\n' +
                        '`#FF6B6B` - Light Red\n' +
                        '`#4ECDC4` - Teal',
                    inline: true
                }
            ]);

            await interaction.reply({
                embeds: [embed],
                ephemeral: true
            });

        } catch (error) {
            console.error('Error getting embed color:', error);
            await interaction.reply({
                content: '❌ Failed to get embed color information.',
                ephemeral: true
            });
        }
    }
};

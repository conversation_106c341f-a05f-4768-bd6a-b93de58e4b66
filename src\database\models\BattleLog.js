const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
    const BattleLog = sequelize.define('BattleLog', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id'
            },
            onDelete: 'CASCADE'
        },
        monster_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'monsters',
                key: 'id'
            }
        },
        battle_type: {
            type: DataTypes.ENUM,
            values: ['fight', 'boss', 'adventure', 'afk'],
            allowNull: false,
            defaultValue: 'fight'
        },
        result: {
            type: DataTypes.ENUM,
            values: ['victory', 'defeat', 'flee'],
            allowNull: false
        },
        // Battle stats
        player_level: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        player_attack: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        player_defense: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        player_health_before: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        player_health_after: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        monster_level: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        monster_health: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        monster_attack: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        monster_defense: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        // Battle details
        turns_taken: {
            type: DataTypes.INTEGER,
            defaultValue: 0
        },
        damage_dealt: {
            type: DataTypes.INTEGER,
            defaultValue: 0
        },
        damage_received: {
            type: DataTypes.INTEGER,
            defaultValue: 0
        },
        critical_hits: {
            type: DataTypes.INTEGER,
            defaultValue: 0
        },
        // Rewards
        exp_gained: {
            type: DataTypes.INTEGER,
            defaultValue: 0
        },
        gold_gained: {
            type: DataTypes.INTEGER,
            defaultValue: 0
        },
        gems_gained: {
            type: DataTypes.INTEGER,
            defaultValue: 0
        },
        items_dropped: {
            type: DataTypes.JSONB,
            defaultValue: []
            // Format: [{ item_id: 1, quantity: 1, rarity: 'rare' }]
        },
        // Battle log details
        battle_details: {
            type: DataTypes.JSONB,
            defaultValue: {}
            // Detailed turn-by-turn log if needed
        },
        duration_seconds: {
            type: DataTypes.INTEGER,
            defaultValue: 0
        },
        // Location/context
        location: {
            type: DataTypes.STRING,
            allowNull: true
        },
        // Special events during battle
        special_events: {
            type: DataTypes.JSONB,
            defaultValue: []
            // Format: [{ event: 'critical_hit', turn: 3, description: '...' }]
        }
    }, {
        tableName: 'battle_logs',
        indexes: [
            {
                fields: ['user_id']
            },
            {
                fields: ['monster_id']
            },
            {
                fields: ['battle_type']
            },
            {
                fields: ['result']
            },
            {
                fields: ['created_at']
            },
            {
                fields: ['user_id', 'created_at']
            }
        ]
    });

    // Static methods
    BattleLog.getRecentBattles = function(userId, limit = 10) {
        return this.findAll({
            where: { user_id: userId },
            include: [
                {
                    association: 'monster',
                    attributes: ['name', 'emoji', 'type', 'level']
                }
            ],
            order: [['created_at', 'DESC']],
            limit: limit
        });
    };

    BattleLog.getBattleStats = function(userId, timeframe = '7d') {
        const timeMap = {
            '1d': 1,
            '7d': 7,
            '30d': 30,
            '90d': 90
        };

        const days = timeMap[timeframe] || 7;
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);

        return this.findAll({
            where: {
                user_id: userId,
                created_at: {
                    [sequelize.Sequelize.Op.gte]: startDate
                }
            },
            attributes: [
                'result',
                'battle_type',
                [sequelize.fn('COUNT', '*'), 'count'],
                [sequelize.fn('SUM', sequelize.col('exp_gained')), 'total_exp'],
                [sequelize.fn('SUM', sequelize.col('gold_gained')), 'total_gold'],
                [sequelize.fn('SUM', sequelize.col('gems_gained')), 'total_gems'],
                [sequelize.fn('SUM', sequelize.col('damage_dealt')), 'total_damage_dealt'],
                [sequelize.fn('SUM', sequelize.col('damage_received')), 'total_damage_received'],
                [sequelize.fn('SUM', sequelize.col('critical_hits')), 'total_crits']
            ],
            group: ['result', 'battle_type'],
            raw: true
        });
    };

    BattleLog.getTopPlayers = function(timeframe = '7d', limit = 10) {
        const timeMap = {
            '1d': 1,
            '7d': 7,
            '30d': 30,
            '90d': 90
        };

        const days = timeMap[timeframe] || 7;
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);

        return this.findAll({
            where: {
                created_at: {
                    [sequelize.Sequelize.Op.gte]: startDate
                },
                result: 'victory'
            },
            attributes: [
                'user_id',
                [sequelize.fn('COUNT', '*'), 'victories'],
                [sequelize.fn('SUM', sequelize.col('exp_gained')), 'total_exp'],
                [sequelize.fn('SUM', sequelize.col('gold_gained')), 'total_gold'],
                [sequelize.fn('SUM', sequelize.col('damage_dealt')), 'total_damage']
            ],
            include: [
                {
                    association: 'user',
                    attributes: ['username', 'discriminator']
                }
            ],
            group: ['user_id', 'user.id', 'user.username', 'user.discriminator'],
            order: [[sequelize.literal('victories'), 'DESC']],
            limit: limit,
            raw: false
        });
    };

    // Instance methods
    BattleLog.prototype.getFormattedResult = function() {
        const resultEmojis = {
            victory: '✅',
            defeat: '❌',
            flee: '🏃'
        };

        const typeNames = {
            fight: 'Fight',
            boss: 'Boss Battle',
            adventure: 'Adventure',
            afk: 'AFK Farm'
        };

        return `${resultEmojis[this.result]} ${typeNames[this.battle_type]}`;
    };

    BattleLog.prototype.getRewardsSummary = function() {
        const rewards = [];
        
        if (this.exp_gained > 0) {
            rewards.push(`${this.exp_gained} EXP`);
        }
        
        if (this.gold_gained > 0) {
            rewards.push(`${this.gold_gained} Gold`);
        }
        
        if (this.gems_gained > 0) {
            rewards.push(`${this.gems_gained} Gems`);
        }

        if (this.items_dropped && this.items_dropped.length > 0) {
            rewards.push(`${this.items_dropped.length} Items`);
        }

        return rewards.join(', ') || 'No rewards';
    };

    BattleLog.prototype.getBattleSummary = function() {
        const efficiency = this.damage_received > 0 ? 
            (this.damage_dealt / this.damage_received).toFixed(2) : 
            'Perfect';

        return {
            turns: this.turns_taken,
            damageDealt: this.damage_dealt,
            damageReceived: this.damage_received,
            criticalHits: this.critical_hits,
            efficiency: efficiency,
            duration: this.duration_seconds
        };
    };

    return BattleLog;
};

const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
    const Transaction = sequelize.define('Transaction', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            references: {
                model: 'users',
                key: 'id'
            },
            onDelete: 'CASCADE'
        },
        type: {
            type: DataTypes.ENUM,
            values: [
                'purchase', 'sale', 'reward', 'penalty', 'transfer', 
                'gacha', 'upgrade', 'repair', 'daily', 'admin', 
                'payment', 'refund', 'vip_purchase'
            ],
            allowNull: false
        },
        // Currency changes
        gold_change: {
            type: DataTypes.INTEGER,
            defaultValue: 0
        },
        gems_change: {
            type: DataTypes.INTEGER,
            defaultValue: 0
        },
        // Balances after transaction
        gold_balance_after: {
            type: DataTypes.BIGINT,
            allowNull: false
        },
        gems_balance_after: {
            type: DataTypes.INTEGER,
            allowNull: false
        },
        // Transaction details
        description: {
            type: DataTypes.STRING,
            allowNull: false
        },
        reference_type: {
            type: DataTypes.STRING,
            allowNull: true // 'item', 'monster', 'shop', 'gacha', etc.
        },
        reference_id: {
            type: DataTypes.INTEGER,
            allowNull: true
        },
        // Items involved in transaction
        items_involved: {
            type: DataTypes.JSONB,
            defaultValue: []
            // Format: [{ item_id: 1, quantity: 1, action: 'gained/lost' }]
        },
        // Payment information (for real money transactions)
        payment_method: {
            type: DataTypes.STRING,
            allowNull: true // 'momo', 'zalopay', 'paypal', etc.
        },
        payment_id: {
            type: DataTypes.STRING,
            allowNull: true
        },
        payment_amount: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: true
        },
        payment_currency: {
            type: DataTypes.STRING(3),
            allowNull: true // 'VND', 'USD', etc.
        },
        payment_status: {
            type: DataTypes.ENUM,
            values: ['pending', 'completed', 'failed', 'refunded'],
            allowNull: true
        },
        // Additional metadata
        metadata: {
            type: DataTypes.JSONB,
            defaultValue: {}
        },
        // Admin information
        admin_id: {
            type: DataTypes.STRING,
            allowNull: true // Discord ID of admin who made the transaction
        },
        admin_reason: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        // Status
        status: {
            type: DataTypes.ENUM,
            values: ['pending', 'completed', 'failed', 'cancelled'],
            defaultValue: 'completed'
        },
        // IP and location for security
        ip_address: {
            type: DataTypes.INET,
            allowNull: true
        },
        user_agent: {
            type: DataTypes.TEXT,
            allowNull: true
        }
    }, {
        tableName: 'transactions',
        indexes: [
            {
                fields: ['user_id']
            },
            {
                fields: ['type']
            },
            {
                fields: ['status']
            },
            {
                fields: ['payment_status']
            },
            {
                fields: ['created_at']
            },
            {
                fields: ['user_id', 'created_at']
            },
            {
                fields: ['reference_type', 'reference_id']
            }
        ]
    });

    // Static methods
    Transaction.createPurchase = function(userId, itemId, goldCost, gemsCost, goldBalance, gemsBalance, description) {
        return this.create({
            user_id: userId,
            type: 'purchase',
            gold_change: -goldCost,
            gems_change: -gemsCost,
            gold_balance_after: goldBalance,
            gems_balance_after: gemsBalance,
            description: description,
            reference_type: 'item',
            reference_id: itemId,
            items_involved: [{ item_id: itemId, quantity: 1, action: 'gained' }]
        });
    };

    Transaction.createSale = function(userId, itemId, goldGain, goldBalance, gemsBalance, description) {
        return this.create({
            user_id: userId,
            type: 'sale',
            gold_change: goldGain,
            gems_change: 0,
            gold_balance_after: goldBalance,
            gems_balance_after: gemsBalance,
            description: description,
            reference_type: 'item',
            reference_id: itemId,
            items_involved: [{ item_id: itemId, quantity: 1, action: 'lost' }]
        });
    };

    Transaction.createReward = function(userId, goldGain, gemsGain, goldBalance, gemsBalance, description, referenceType = null, referenceId = null) {
        return this.create({
            user_id: userId,
            type: 'reward',
            gold_change: goldGain,
            gems_change: gemsGain,
            gold_balance_after: goldBalance,
            gems_balance_after: gemsBalance,
            description: description,
            reference_type: referenceType,
            reference_id: referenceId
        });
    };

    Transaction.createPayment = function(userId, gemsGain, paymentAmount, paymentCurrency, paymentMethod, paymentId, goldBalance, gemsBalance) {
        return this.create({
            user_id: userId,
            type: 'payment',
            gold_change: 0,
            gems_change: gemsGain,
            gold_balance_after: goldBalance,
            gems_balance_after: gemsBalance,
            description: `Purchased ${gemsGain} gems`,
            payment_method: paymentMethod,
            payment_id: paymentId,
            payment_amount: paymentAmount,
            payment_currency: paymentCurrency,
            payment_status: 'pending'
        });
    };

    Transaction.getUserTransactions = function(userId, options = {}) {
        const whereClause = { user_id: userId };
        
        if (options.type) {
            whereClause.type = options.type;
        }
        
        if (options.status) {
            whereClause.status = options.status;
        }

        if (options.dateFrom) {
            whereClause.created_at = {
                [sequelize.Sequelize.Op.gte]: options.dateFrom
            };
        }

        return this.findAll({
            where: whereClause,
            order: [['created_at', 'DESC']],
            limit: options.limit || 50,
            offset: options.offset || 0
        });
    };

    Transaction.getSpendingStats = function(userId, timeframe = '30d') {
        const timeMap = {
            '1d': 1,
            '7d': 7,
            '30d': 30,
            '90d': 90,
            '365d': 365
        };

        const days = timeMap[timeframe] || 30;
        const startDate = new Date();
        startDate.setDate(startDate.getDate() - days);

        return this.findAll({
            where: {
                user_id: userId,
                created_at: {
                    [sequelize.Sequelize.Op.gte]: startDate
                },
                status: 'completed'
            },
            attributes: [
                'type',
                [sequelize.fn('COUNT', '*'), 'count'],
                [sequelize.fn('SUM', sequelize.col('gold_change')), 'total_gold_change'],
                [sequelize.fn('SUM', sequelize.col('gems_change')), 'total_gems_change'],
                [sequelize.fn('SUM', sequelize.col('payment_amount')), 'total_spent']
            ],
            group: ['type'],
            raw: true
        });
    };

    // Instance methods
    Transaction.prototype.getFormattedAmount = function() {
        const parts = [];
        
        if (this.gold_change !== 0) {
            const sign = this.gold_change > 0 ? '+' : '';
            parts.push(`${sign}${this.gold_change.toLocaleString()} 🪙`);
        }
        
        if (this.gems_change !== 0) {
            const sign = this.gems_change > 0 ? '+' : '';
            parts.push(`${sign}${this.gems_change.toLocaleString()} 💎`);
        }
        
        return parts.join(', ') || 'No currency change';
    };

    Transaction.prototype.getTypeEmoji = function() {
        const typeEmojis = {
            purchase: '🛒',
            sale: '💰',
            reward: '🎁',
            penalty: '⚠️',
            transfer: '↔️',
            gacha: '🎰',
            upgrade: '⬆️',
            repair: '🔧',
            daily: '📅',
            admin: '👑',
            payment: '💳',
            refund: '↩️',
            vip_purchase: '⭐'
        };

        return typeEmojis[this.type] || '❓';
    };

    Transaction.prototype.getFormattedDescription = function() {
        return `${this.getTypeEmoji()} ${this.description}`;
    };

    Transaction.prototype.isPaymentTransaction = function() {
        return ['payment', 'vip_purchase'].includes(this.type);
    };

    Transaction.prototype.markPaymentCompleted = function() {
        if (this.isPaymentTransaction()) {
            this.payment_status = 'completed';
            this.status = 'completed';
            return this.save();
        }
        return false;
    };

    Transaction.prototype.markPaymentFailed = function(reason = null) {
        if (this.isPaymentTransaction()) {
            this.payment_status = 'failed';
            this.status = 'failed';
            if (reason) {
                this.metadata = { ...this.metadata, failure_reason: reason };
            }
            return this.save();
        }
        return false;
    };

    return Transaction;
};

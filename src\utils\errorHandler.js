const GameEmbedBuilder = require('./embedBuilder');

class ErrorHandler {
    constructor() {
        this.errorCounts = new Map();
        this.lastErrors = [];
        this.maxLastErrors = 50;
    }

    // Handle Discord.js errors
    handleDiscordError(error, context = {}) {
        console.error('Discord Error:', error);
        
        this.recordError('discord', error, context);
        
        // Common Discord error handling
        if (error.code === 50013) {
            console.warn('Missing permissions for action:', context);
            return this.createPermissionErrorEmbed();
        }
        
        if (error.code === 50001) {
            console.warn('Missing access for action:', context);
            return this.createAccessErrorEmbed();
        }
        
        if (error.code === 10008) {
            console.warn('Unknown message:', context);
            return null; // Don't show error for unknown messages
        }
        
        return this.createGenericErrorEmbed();
    }

    // Handle database errors
    handleDatabaseError(error, context = {}) {
        console.error('Database Error:', error);
        
        this.recordError('database', error, context);
        
        // Common database error handling
        if (error.name === 'SequelizeConnectionError') {
            console.error('Database connection lost');
            return this.createDatabaseConnectionErrorEmbed();
        }
        
        if (error.name === 'SequelizeValidationError') {
            console.warn('Database validation error:', error.errors);
            return this.createValidationErrorEmbed(error.errors);
        }
        
        if (error.name === 'SequelizeUniqueConstraintError') {
            console.warn('Database unique constraint error:', error.errors);
            return this.createUniqueConstraintErrorEmbed();
        }
        
        return this.createDatabaseErrorEmbed();
    }

    // Handle command errors
    handleCommandError(error, commandName, context = {}) {
        console.error(`Command Error (${commandName}):`, error);
        
        this.recordError('command', error, { commandName, ...context });
        
        // Check for specific command errors
        if (error.message.includes('cooldown')) {
            return this.createCooldownErrorEmbed(error.message);
        }
        
        if (error.message.includes('permission')) {
            return this.createPermissionErrorEmbed();
        }
        
        if (error.message.includes('not found')) {
            return this.createNotFoundErrorEmbed(error.message);
        }
        
        return this.createCommandErrorEmbed(commandName);
    }

    // Handle combat errors
    handleCombatError(error, context = {}) {
        console.error('Combat Error:', error);
        
        this.recordError('combat', error, context);
        
        if (error.message.includes('no monsters')) {
            return GameEmbedBuilder.createWarningEmbed(
                '🚫 No Monsters Available',
                'There are no monsters available for combat right now. Please try again later.'
            );
        }
        
        if (error.message.includes('health')) {
            return GameEmbedBuilder.createErrorEmbed(
                '💔 Cannot Fight',
                'You need to heal before you can fight again. Use a health potion or wait for natural regeneration.'
            );
        }
        
        return this.createCombatErrorEmbed();
    }

    // Record error for tracking
    recordError(type, error, context = {}) {
        const errorKey = `${type}:${error.name || 'Unknown'}`;
        
        // Increment error count
        const currentCount = this.errorCounts.get(errorKey) || 0;
        this.errorCounts.set(errorKey, currentCount + 1);
        
        // Add to recent errors
        this.lastErrors.push({
            type,
            error: {
                name: error.name,
                message: error.message,
                stack: error.stack
            },
            context,
            timestamp: new Date()
        });
        
        // Keep only recent errors
        if (this.lastErrors.length > this.maxLastErrors) {
            this.lastErrors.shift();
        }
        
        // Log critical errors
        if (currentCount > 10) {
            console.error(`⚠️ High error frequency detected: ${errorKey} (${currentCount} times)`);
        }
    }

    // Create error embeds
    createPermissionErrorEmbed() {
        return GameEmbedBuilder.createErrorEmbed(
            '🚫 Permission Denied',
            'I don\'t have the necessary permissions to perform this action. Please check my role permissions.'
        );
    }

    createAccessErrorEmbed() {
        return GameEmbedBuilder.createErrorEmbed(
            '🚫 Access Denied',
            'I don\'t have access to perform this action in this channel.'
        );
    }

    createDatabaseConnectionErrorEmbed() {
        return GameEmbedBuilder.createErrorEmbed(
            '🗄️ Database Connection Error',
            'Unable to connect to the database. Please try again in a few moments.'
        );
    }

    createValidationErrorEmbed(errors) {
        const errorMessages = errors.map(err => `• ${err.message}`).join('\n');
        return GameEmbedBuilder.createErrorEmbed(
            '❌ Validation Error',
            `The following validation errors occurred:\n${errorMessages}`
        );
    }

    createUniqueConstraintErrorEmbed() {
        return GameEmbedBuilder.createErrorEmbed(
            '⚠️ Duplicate Entry',
            'This action would create a duplicate entry. Please try a different approach.'
        );
    }

    createDatabaseErrorEmbed() {
        return GameEmbedBuilder.createErrorEmbed(
            '🗄️ Database Error',
            'A database error occurred. Please try again later.'
        );
    }

    createCooldownErrorEmbed(message) {
        return GameEmbedBuilder.createWarningEmbed(
            '⏰ Command Cooldown',
            message || 'This command is on cooldown. Please wait before using it again.'
        );
    }

    createNotFoundErrorEmbed(message) {
        return GameEmbedBuilder.createWarningEmbed(
            '🔍 Not Found',
            message || 'The requested item or resource was not found.'
        );
    }

    createCommandErrorEmbed(commandName) {
        return GameEmbedBuilder.createErrorEmbed(
            '⚠️ Command Error',
            `An error occurred while executing the ${commandName} command. Please try again.`
        );
    }

    createCombatErrorEmbed() {
        return GameEmbedBuilder.createErrorEmbed(
            '⚔️ Combat Error',
            'An error occurred during combat. Please try again.'
        );
    }

    createGenericErrorEmbed() {
        return GameEmbedBuilder.createErrorEmbed(
            '❌ Error',
            'An unexpected error occurred. Please try again later.'
        );
    }

    // Get error statistics
    getErrorStats() {
        const stats = {
            totalErrors: this.lastErrors.length,
            errorCounts: Object.fromEntries(this.errorCounts),
            recentErrors: this.lastErrors.slice(-10).map(err => ({
                type: err.type,
                name: err.error.name,
                message: err.error.message,
                timestamp: err.timestamp
            }))
        };
        
        return stats;
    }

    // Generate error report
    generateErrorReport() {
        const stats = this.getErrorStats();
        
        let report = '🚨 Error Report\n';
        report += '===============\n\n';
        
        report += `📊 Total Errors: ${stats.totalErrors}\n\n`;
        
        if (Object.keys(stats.errorCounts).length > 0) {
            report += '📈 Error Counts:\n';
            for (const [errorType, count] of Object.entries(stats.errorCounts)) {
                report += `  ${errorType}: ${count}\n`;
            }
            report += '\n';
        }
        
        if (stats.recentErrors.length > 0) {
            report += '🕒 Recent Errors:\n';
            stats.recentErrors.forEach((error, index) => {
                report += `  ${index + 1}. [${error.type}] ${error.name}: ${error.message}\n`;
                report += `     Time: ${error.timestamp.toISOString()}\n`;
            });
        }
        
        return report;
    }

    // Clear error history
    clearErrors() {
        this.errorCounts.clear();
        this.lastErrors = [];
    }

    // Safe async wrapper
    async safeAsync(asyncFn, errorHandler = null) {
        try {
            return await asyncFn();
        } catch (error) {
            if (errorHandler) {
                return errorHandler(error);
            }
            
            console.error('Unhandled async error:', error);
            return null;
        }
    }

    // Safe message reply wrapper
    async safeReply(message, content) {
        try {
            return await message.reply(content);
        } catch (error) {
            console.error('Failed to reply to message:', error);
            
            // Try to send to channel instead
            try {
                return await message.channel.send(content);
            } catch (channelError) {
                console.error('Failed to send to channel:', channelError);
                return null;
            }
        }
    }

    // Safe interaction reply wrapper
    async safeInteractionReply(interaction, content) {
        try {
            if (interaction.replied || interaction.deferred) {
                return await interaction.followUp(content);
            } else {
                return await interaction.reply(content);
            }
        } catch (error) {
            console.error('Failed to reply to interaction:', error);
            return null;
        }
    }
}

// Global error handler instance
const errorHandler = new ErrorHandler();

// Global error handlers
process.on('uncaughtException', (error) => {
    console.error('Uncaught Exception:', error);
    errorHandler.recordError('uncaught', error);
});

process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
    errorHandler.recordError('unhandled', reason);
});

module.exports = {
    ErrorHandler,
    errorHandler
};

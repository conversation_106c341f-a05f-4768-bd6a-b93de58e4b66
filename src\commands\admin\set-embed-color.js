const { SlashCommandBuilder } = require('discord.js');
const GameEmbedBuilder = require('../../utils/embedBuilder');
const fs = require('fs');
const path = require('path');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('set-embed-color')
        .setDescription('Change the default embed color for the bot')
        .addStringOption(option =>
            option.setName('color')
                .setDescription('Hex color code (e.g., #FF0000 for red)')
                .setRequired(true)
        )
        .addBooleanOption(option =>
            option.setName('apply-to-all')
                .setDescription('Apply to all existing embed types')
                .setRequired(false)
        ),

    async execute(interaction) {
        // Check if user is admin
        const adminIds = process.env.ADMIN_IDS?.split(',') || [];
        if (!adminIds.includes(interaction.user.id)) {
            return await interaction.reply({
                content: '❌ You do not have permission to use this command.',
                ephemeral: true
            });
        }

        const colorInput = interaction.options.getString('color');
        const applyToAll = interaction.options.getBoolean('apply-to-all') ?? true;

        // Validate hex color format
        const hexColorRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/;
        if (!hexColorRegex.test(colorInput)) {
            return await interaction.reply({
                content: '❌ Invalid color format! Please use hex format like #FF0000 or #F00',
                ephemeral: true
            });
        }

        // Convert to full hex if short format
        let hexColor = colorInput;
        if (hexColor.length === 4) {
            hexColor = '#' + hexColor[1] + hexColor[1] + hexColor[2] + hexColor[2] + hexColor[3] + hexColor[3];
        }

        // Convert hex to decimal for Discord
        const colorDecimal = parseInt(hexColor.replace('#', ''), 16);

        try {
            await interaction.deferReply({ ephemeral: true });

            // Update embed builder with new color
            await this.updateEmbedBuilder(hexColor, colorDecimal, applyToAll);

            // Create preview embed with new color
            const previewEmbed = GameEmbedBuilder.createInfoEmbed(
                '🎨 Embed Color Updated',
                `Successfully changed bot embed color to **${hexColor.toUpperCase()}**\n\n` +
                `**Settings:**\n` +
                `• Color: ${hexColor.toUpperCase()}\n` +
                `• Decimal: ${colorDecimal}\n` +
                `• Apply to all embeds: ${applyToAll ? 'Yes' : 'No'}\n\n` +
                `This embed is using the new color!`
            );

            // Force the new color for this embed
            previewEmbed.setColor(colorDecimal);

            await interaction.editReply({
                embeds: [previewEmbed]
            });

            console.log(`🎨 Admin ${interaction.user.username} changed embed color to ${hexColor}`);

        } catch (error) {
            console.error('Error updating embed color:', error);
            await interaction.editReply({
                content: '❌ Failed to update embed color. Please check the logs.',
                ephemeral: true
            });
        }
    },

    async updateEmbedBuilder(hexColor, colorDecimal, applyToAll) {
        const embedBuilderPath = path.join(__dirname, '../../utils/embedBuilder.js');
        
        try {
            let content = fs.readFileSync(embedBuilderPath, 'utf8');

            if (applyToAll) {
                // Replace all color values with the new color
                const colorPatterns = [
                    /0x[0-9A-Fa-f]{6}/g,  // Hex format like 0xFF0000
                    /#[0-9A-Fa-f]{6}/g,   // Hex format like #FF0000
                    /\d{7,8}/g            // Decimal format like 16711680
                ];

                // Replace hex patterns
                content = content.replace(/0x[0-9A-Fa-f]{6}/g, `0x${hexColor.replace('#', '')}`);
                content = content.replace(/#[0-9A-Fa-f]{6}/g, hexColor);
                
                // Replace common decimal color values (but be careful not to replace other numbers)
                const commonColors = [
                    '0x00FF00', '0xFF0000', '0x0099FF', '0xFFFF00', '0xFF6B6B', 
                    '0x4ECDC4', '0x45B7D1', '0x96CEB4', '0xFECEA8', '0xFF9FF3',
                    '0x9932CC', '0x8D689E'
                ];
                
                commonColors.forEach(oldColor => {
                    const oldDecimal = parseInt(oldColor.replace('0x', ''), 16);
                    content = content.replace(new RegExp(`\\b${oldDecimal}\\b`, 'g'), colorDecimal.toString());
                });

            } else {
                // Only replace the default color constant if it exists
                content = content.replace(
                    /const\s+DEFAULT_COLOR\s*=\s*[^;]+;/g,
                    `const DEFAULT_COLOR = ${colorDecimal};`
                );
            }

            // Add default color constant if it doesn't exist
            if (!content.includes('DEFAULT_COLOR')) {
                const classStart = content.indexOf('class GameEmbedBuilder');
                if (classStart !== -1) {
                    const insertPos = content.lastIndexOf('\n', classStart);
                    content = content.slice(0, insertPos) + 
                             `\nconst DEFAULT_COLOR = ${colorDecimal};\n` + 
                             content.slice(insertPos);
                }
            }

            fs.writeFileSync(embedBuilderPath, content);
            
            // Clear require cache to reload the module
            delete require.cache[require.resolve('../../utils/embedBuilder')];
            
        } catch (error) {
            console.error('Error updating embedBuilder.js:', error);
            throw error;
        }
    }
};

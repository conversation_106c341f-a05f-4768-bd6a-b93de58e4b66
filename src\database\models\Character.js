const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
    const Character = sequelize.define('Character', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        user_id: {
            type: DataTypes.INTEGER,
            allowNull: false,
            unique: true,
            references: {
                model: 'users',
                key: 'id'
            },
            onDelete: 'CASCADE'
        },
        level: {
            type: DataTypes.INTEGER,
            defaultValue: 1,
            validate: {
                min: 1,
                max: 100
            }
        },
        experience: {
            type: DataTypes.BIGINT,
            defaultValue: 0,
            validate: {
                min: 0
            }
        },
        gold: {
            type: DataTypes.BIGINT,
            defaultValue: 100,
            validate: {
                min: 0
            }
        },
        gems: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0
            }
        },
        health: {
            type: DataTypes.INTEGER,
            defaultValue: 100,
            validate: {
                min: 0
            }
        },
        max_health: {
            type: DataTypes.INTEGER,
            defaultValue: 100,
            validate: {
                min: 1
            }
        },
        attack: {
            type: DataTypes.INTEGER,
            defaultValue: 10,
            validate: {
                min: 1
            }
        },
        defense: {
            type: DataTypes.INTEGER,
            defaultValue: 5,
            validate: {
                min: 0
            }
        },
        crit_rate: {
            type: DataTypes.DECIMAL(5, 2),
            defaultValue: 5.00,
            validate: {
                min: 0,
                max: 100
            }
        },
        crit_damage: {
            type: DataTypes.DECIMAL(6, 2),
            defaultValue: 150.00,
            validate: {
                min: 100
            }
        },
        luck: {
            type: DataTypes.DECIMAL(5, 2),
            defaultValue: 1.00,
            validate: {
                min: 0
            }
        },
        vip_level: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0,
                max: 5
            }
        },
        prestige_level: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0
            }
        },
        // Equipment slots
        weapon_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'items',
                key: 'id'
            }
        },
        armor_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'items',
                key: 'id'
            }
        },
        helmet_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'items',
                key: 'id'
            }
        },
        boots_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'items',
                key: 'id'
            }
        },
        ring_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'items',
                key: 'id'
            }
        },
        necklace_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'items',
                key: 'id'
            }
        },
        // Cooldowns
        last_fight: {
            type: DataTypes.DATE,
            allowNull: true
        },
        last_adventure: {
            type: DataTypes.DATE,
            allowNull: true
        },
        last_boss_fight: {
            type: DataTypes.DATE,
            allowNull: true
        },
        last_daily: {
            type: DataTypes.DATE,
            allowNull: true
        },
        daily_streak: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0
            }
        },
        // AFK farming
        afk_start_time: {
            type: DataTypes.DATE,
            allowNull: true
        },
        afk_location: {
            type: DataTypes.STRING,
            allowNull: true
        }
    }, {
        tableName: 'characters',
        indexes: [
            {
                unique: true,
                fields: ['user_id']
            },
            {
                fields: ['level']
            },
            {
                fields: ['vip_level']
            }
        ]
    });

    // Instance methods
    Character.prototype.getRequiredXP = function() {
        const baseXP = parseInt(process.env.BASE_XP_REQUIREMENT) || 100;
        const multiplier = parseFloat(process.env.XP_MULTIPLIER) || 1.5;
        return Math.floor(baseXP * Math.pow(multiplier, this.level - 1));
    };

    Character.prototype.canLevelUp = function() {
        return this.experience >= this.getRequiredXP();
    };

    Character.prototype.levelUp = function() {
        if (!this.canLevelUp()) return false;
        
        this.level += 1;
        this.experience -= this.getRequiredXP();
        
        // Increase base stats on level up
        this.max_health += 10;
        this.health = this.max_health; // Full heal on level up
        this.attack += 2;
        this.defense += 1;
        
        return true;
    };

    Character.prototype.getTotalStats = function() {
        // Base stats + equipment bonuses
        return {
            attack: this.attack,
            defense: this.defense,
            health: this.health,
            max_health: this.max_health,
            crit_rate: this.crit_rate,
            crit_damage: this.crit_damage,
            luck: this.luck
        };
    };

    Character.prototype.isOnCooldown = function(action) {
        const cooldowns = {
            fight: 30000, // 30 seconds
            adventure: 300000, // 5 minutes
            boss: 3600000, // 1 hour
            daily: 86400000 // 24 hours
        };

        const lastAction = this[`last_${action}`];
        if (!lastAction) return false;

        const timePassed = Date.now() - new Date(lastAction).getTime();
        return timePassed < cooldowns[action];
    };

    Character.prototype.getCooldownRemaining = function(action) {
        const cooldowns = {
            fight: 30000,
            adventure: 300000,
            boss: 3600000,
            daily: 86400000
        };

        const lastAction = this[`last_${action}`];
        if (!lastAction) return 0;

        const timePassed = Date.now() - new Date(lastAction).getTime();
        const remaining = cooldowns[action] - timePassed;
        return Math.max(0, remaining);
    };

    Character.prototype.getVipBenefits = function() {
        const vipLevels = [
            { level: 0, name: 'Free Player', gold_bonus: 0.00, daily_gems: 0, cooldown_reduction: 0.00 },
            { level: 1, name: 'Bronze VIP', gold_bonus: 0.10, daily_gems: 5, cooldown_reduction: 0.25 },
            { level: 2, name: 'Silver VIP', gold_bonus: 0.20, daily_gems: 10, cooldown_reduction: 0.35 },
            { level: 3, name: 'Gold VIP', gold_bonus: 0.30, daily_gems: 20, cooldown_reduction: 0.45 },
            { level: 4, name: 'Platinum VIP', gold_bonus: 0.50, daily_gems: 35, cooldown_reduction: 0.55 },
            { level: 5, name: 'Diamond VIP', gold_bonus: 1.00, daily_gems: 75, cooldown_reduction: 0.65 }
        ];

        return vipLevels.find(vip => vip.level === this.vip_level) || vipLevels[0];
    };

    return Character;
};

const { User, Character, Monster } = require('../../src/database/database');
const { sequelize } = require('../../src/database/database');
const fightCommand = require('../../src/commands/game/fight');

// Mock Discord message
const createMockMessage = (authorId = '123456789', content = '!fight') => ({
    author: {
        id: authorId,
        username: 'testuser',
        discriminator: '0001',
        displayAvatarURL: () => 'https://example.com/avatar.png'
    },
    content,
    reply: jest.fn().mockResolvedValue({
        react: jest.fn().mockResolvedValue(true),
        edit: jest.fn().mockResolvedValue(true),
        delete: jest.fn().mockResolvedValue(true)
    }),
    channel: {
        send: jest.fn().mockResolvedValue(true)
    }
});

describe('Fight Command', () => {
    let testUser;
    let testCharacter;
    let testMonster;

    beforeAll(async () => {
        await sequelize.sync({ force: true });
    });

    beforeEach(async () => {
        // Create test user
        testUser = await User.create({
            discord_id: '123456789',
            username: 'testuser',
            discriminator: '0001'
        });

        // Create test character
        testCharacter = await Character.create({
            user_id: testUser.id,
            level: 5,
            experience: 0,
            health: 100,
            max_health: 100,
            attack: 15,
            defense: 8,
            gold: 50,
            gems: 0
        });

        // Create test monster
        testMonster = await Monster.create({
            name: 'Test Slime',
            type: 'normal',
            level: 3,
            health: 40,
            attack: 8,
            defense: 2,
            base_exp: 25,
            base_gold: 15,
            is_active: true
        });
    });

    afterEach(async () => {
        await Monster.destroy({ where: {} });
        await Character.destroy({ where: {} });
        await User.destroy({ where: {} });
    });

    afterAll(async () => {
        await sequelize.close();
    });

    describe('Command Execution', () => {
        test('should execute fight command successfully', async () => {
            const message = createMockMessage();
            
            await fightCommand.execute(message, []);
            
            expect(message.reply).toHaveBeenCalled();
            
            // Check if reply was called with embed
            const replyCall = message.reply.mock.calls[0][0];
            expect(replyCall).toHaveProperty('embeds');
            expect(Array.isArray(replyCall.embeds)).toBe(true);
        });

        test('should handle user not found', async () => {
            const message = createMockMessage('999999999'); // Non-existent user
            
            await fightCommand.execute(message, []);
            
            expect(message.reply).toHaveBeenCalled();
            
            // Should create new user automatically
            const newUser = await User.findByDiscordId('999999999');
            expect(newUser).toBeDefined();
        });

        test('should handle character on cooldown', async () => {
            // Set recent fight time
            testCharacter.last_fight = new Date();
            await testCharacter.save();
            
            const message = createMockMessage();
            
            await fightCommand.execute(message, []);
            
            expect(message.reply).toHaveBeenCalled();
            
            const replyCall = message.reply.mock.calls[0][0];
            expect(replyCall.embeds[0].data.title).toContain('Cooldown');
        });

        test('should handle character with no health', async () => {
            testCharacter.health = 0;
            await testCharacter.save();
            
            const message = createMockMessage();
            
            await fightCommand.execute(message, []);
            
            expect(message.reply).toHaveBeenCalled();
            
            const replyCall = message.reply.mock.calls[0][0];
            expect(replyCall.embeds[0].data.title).toContain('Cannot Fight');
        });

        test('should handle no available monsters', async () => {
            // Remove all monsters
            await Monster.destroy({ where: {} });
            
            const message = createMockMessage();
            
            await fightCommand.execute(message, []);
            
            expect(message.reply).toHaveBeenCalled();
            
            const replyCall = message.reply.mock.calls[0][0];
            expect(replyCall.embeds[0].data.title).toContain('No Monsters');
        });
    });

    describe('Battle Results', () => {
        test('should update character stats after victory', async () => {
            // Make character much stronger to ensure victory
            testCharacter.attack = 50;
            await testCharacter.save();
            
            const oldExp = testCharacter.experience;
            const oldGold = testCharacter.gold;
            
            const message = createMockMessage();
            
            await fightCommand.execute(message, []);
            
            // Reload character from database
            await testCharacter.reload();
            
            // Character should have gained something (unless it was a defeat)
            const replyCall = message.reply.mock.calls[0][0];
            const embedTitle = replyCall.embeds[0].data.title;
            
            if (embedTitle.includes('Victory')) {
                expect(testCharacter.experience).toBeGreaterThan(oldExp);
                expect(testCharacter.gold).toBeGreaterThan(oldGold);
            }
        });

        test('should set cooldown after fight', async () => {
            const message = createMockMessage();
            
            await fightCommand.execute(message, []);
            
            // Reload character
            await testCharacter.reload();
            
            expect(testCharacter.last_fight).toBeDefined();
            expect(testCharacter.last_fight).toBeInstanceOf(Date);
        });

        test('should handle level up during fight', async () => {
            // Set character close to level up
            testCharacter.experience = testCharacter.getRequiredXP() - 10;
            testCharacter.attack = 50; // Ensure victory
            await testCharacter.save();
            
            const oldLevel = testCharacter.level;
            
            const message = createMockMessage();
            
            await fightCommand.execute(message, []);
            
            // Check if level up message is included
            const replyCall = message.reply.mock.calls[0][0];
            const embedDescription = replyCall.embeds[0].data.description;
            
            if (embedDescription && embedDescription.includes('LEVEL UP')) {
                await testCharacter.reload();
                expect(testCharacter.level).toBeGreaterThan(oldLevel);
            }
        });
    });

    describe('VIP Benefits', () => {
        test('should apply VIP gold bonus', async () => {
            testCharacter.vip_level = 3;
            testCharacter.attack = 50; // Ensure victory
            await testCharacter.save();
            
            const message = createMockMessage();
            
            await fightCommand.execute(message, []);
            
            const replyCall = message.reply.mock.calls[0][0];
            
            // Should mention VIP benefits if victory
            if (replyCall.embeds[0].data.title.includes('Victory')) {
                const fields = replyCall.embeds[0].data.fields || [];
                const vipField = fields.find(field => field.name.includes('VIP'));
                
                if (vipField) {
                    expect(vipField.value).toContain('bonus');
                }
            }
        });

        test('should show reduced cooldown for VIP', async () => {
            testCharacter.vip_level = 2;
            await testCharacter.save();
            
            const message = createMockMessage();
            
            await fightCommand.execute(message, []);
            
            const replyCall = message.reply.mock.calls[0][0];
            const footer = replyCall.embeds[0].data.footer;
            
            if (footer && footer.text.includes('cooldown')) {
                // VIP should have reduced cooldown time mentioned
                expect(footer.text).toBeDefined();
            }
        });
    });

    describe('Error Handling', () => {
        test('should handle database errors gracefully', async () => {
            // Mock database error
            const originalFindByDiscordId = User.findByDiscordId;
            User.findByDiscordId = jest.fn().mockRejectedValue(new Error('Database error'));
            
            const message = createMockMessage();
            
            await fightCommand.execute(message, []);
            
            expect(message.reply).toHaveBeenCalled();
            
            const replyCall = message.reply.mock.calls[0][0];
            expect(replyCall.embeds[0].data.title).toContain('Error');
            
            // Restore original function
            User.findByDiscordId = originalFindByDiscordId;
        });

        test('should handle combat engine errors', async () => {
            // Mock combat engine error
            const CombatEngine = require('../../src/utils/combatEngine');
            const originalSimulateBattle = CombatEngine.simulateBattle;
            CombatEngine.simulateBattle = jest.fn().mockRejectedValue(new Error('Combat error'));
            
            const message = createMockMessage();
            
            await fightCommand.execute(message, []);
            
            expect(message.reply).toHaveBeenCalled();
            
            // Restore original function
            CombatEngine.simulateBattle = originalSimulateBattle;
        });
    });

    describe('Command Properties', () => {
        test('should have correct command data', () => {
            expect(fightCommand.data).toBeDefined();
            expect(fightCommand.data.name).toBe('fight');
            expect(fightCommand.data.aliases).toContain('f');
            expect(fightCommand.data.description).toBeDefined();
            expect(fightCommand.data.usage).toBeDefined();
            expect(fightCommand.data.cooldown).toBeDefined();
        });

        test('should have execute function', () => {
            expect(typeof fightCommand.execute).toBe('function');
        });
    });
});

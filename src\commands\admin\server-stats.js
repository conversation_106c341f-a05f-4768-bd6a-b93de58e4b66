const { SlashCommandBuilder } = require('discord.js');
const { User, Character, BattleLog, Transaction } = require('../../database/database');
const GameEmbedBuilder = require('../../utils/embedBuilder');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('admin-server-stats')
        .setDescription('View server statistics (Admin only)')
        .addStringOption(option =>
            option.setName('timeframe')
                .setDescription('Timeframe for statistics')
                .setRequired(false)
                .addChoices(
                    { name: 'Last 24 hours', value: '1d' },
                    { name: 'Last 7 days', value: '7d' },
                    { name: 'Last 30 days', value: '30d' },
                    { name: 'All time', value: 'all' }
                )),
    
    async execute(interaction) {
        try {
            // Check if user is admin
            if (!this.isAdmin(interaction.user.id)) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    '🚫 Access Denied',
                    'You do not have permission to use this command.'
                );
                return interaction.reply({ embeds: [embed], ephemeral: true });
            }
            
            const timeframe = interaction.options.getString('timeframe') || '7d';
            
            // Defer reply as this might take a while
            await interaction.deferReply();
            
            // Calculate date range
            let startDate = null;
            if (timeframe !== 'all') {
                const timeMap = { '1d': 1, '7d': 7, '30d': 30 };
                const days = timeMap[timeframe] || 7;
                startDate = new Date();
                startDate.setDate(startDate.getDate() - days);
            }
            
            // Get basic statistics
            const totalUsers = await User.count();
            const totalCharacters = await Character.count();
            const activeUsers = startDate ? await User.count({
                where: {
                    last_seen: { [require('sequelize').Op.gte]: startDate }
                }
            }) : totalUsers;
            
            // Get level statistics
            const levelStats = await Character.findAll({
                attributes: [
                    [require('sequelize').fn('AVG', require('sequelize').col('level')), 'avg_level'],
                    [require('sequelize').fn('MAX', require('sequelize').col('level')), 'max_level'],
                    [require('sequelize').fn('MIN', require('sequelize').col('level')), 'min_level']
                ],
                raw: true
            });
            
            // Get VIP statistics
            const vipStats = await Character.findAll({
                attributes: [
                    'vip_level',
                    [require('sequelize').fn('COUNT', '*'), 'count']
                ],
                group: ['vip_level'],
                order: [['vip_level', 'ASC']],
                raw: true
            });
            
            // Get battle statistics
            const battleWhere = startDate ? { created_at: { [require('sequelize').Op.gte]: startDate } } : {};
            const battleStats = await BattleLog.findAll({
                where: battleWhere,
                attributes: [
                    [require('sequelize').fn('COUNT', '*'), 'total_battles'],
                    [require('sequelize').fn('SUM', require('sequelize').literal("CASE WHEN result = 'victory' THEN 1 ELSE 0 END")), 'victories'],
                    [require('sequelize').fn('SUM', require('sequelize').literal("CASE WHEN result = 'defeat' THEN 1 ELSE 0 END")), 'defeats'],
                    [require('sequelize').fn('SUM', 'exp_gained'), 'total_exp'],
                    [require('sequelize').fn('SUM', 'gold_gained'), 'total_gold'],
                    [require('sequelize').fn('SUM', 'gems_gained'), 'total_gems']
                ],
                raw: true
            });
            
            // Get economy statistics
            const economyStats = await Transaction.findAll({
                where: startDate ? { created_at: { [require('sequelize').Op.gte]: startDate } } : {},
                attributes: [
                    'type',
                    [require('sequelize').fn('COUNT', '*'), 'count'],
                    [require('sequelize').fn('SUM', 'gold_change'), 'total_gold_change'],
                    [require('sequelize').fn('SUM', 'gems_change'), 'total_gems_change']
                ],
                group: ['type'],
                raw: true
            });
            
            // Get top players
            const topPlayers = await Character.findAll({
                include: [{
                    model: User,
                    as: 'user',
                    attributes: ['username']
                }],
                order: [['level', 'DESC'], ['experience', 'DESC']],
                limit: 5
            });
            
            // Create statistics embed
            const timeframeName = {
                '1d': 'Last 24 Hours',
                '7d': 'Last 7 Days',
                '30d': 'Last 30 Days',
                'all': 'All Time'
            };
            
            const embed = GameEmbedBuilder.createInfoEmbed(
                '📊 Server Statistics',
                `Statistics for **${timeframeName[timeframe]}**`
            );
            
            embed.setColor('#00CED1');
            
            // User statistics
            embed.addFields({
                name: '👥 User Statistics',
                value: `**Total Users:** ${totalUsers.toLocaleString()}\n**Total Characters:** ${totalCharacters.toLocaleString()}\n**Active Users:** ${activeUsers.toLocaleString()}`,
                inline: true
            });
            
            // Level statistics
            const avgLevel = levelStats[0]?.avg_level ? parseFloat(levelStats[0].avg_level).toFixed(1) : '0';
            const maxLevel = levelStats[0]?.max_level || 0;
            const minLevel = levelStats[0]?.min_level || 0;
            
            embed.addFields({
                name: '📈 Level Statistics',
                value: `**Average Level:** ${avgLevel}\n**Highest Level:** ${maxLevel}\n**Lowest Level:** ${minLevel}`,
                inline: true
            });
            
            // VIP statistics
            let vipText = '';
            const totalVips = vipStats.reduce((sum, stat) => stat.vip_level > 0 ? sum + parseInt(stat.count) : sum, 0);
            vipText += `**Total VIP Players:** ${totalVips}\n`;
            
            vipStats.forEach(stat => {
                if (stat.vip_level > 0) {
                    vipText += `**VIP ${stat.vip_level}:** ${stat.count}\n`;
                }
            });
            
            if (vipText) {
                embed.addFields({
                    name: '👑 VIP Statistics',
                    value: vipText,
                    inline: false
                });
            }
            
            // Battle statistics
            const battle = battleStats[0] || {};
            const totalBattles = parseInt(battle.total_battles) || 0;
            const victories = parseInt(battle.victories) || 0;
            const defeats = parseInt(battle.defeats) || 0;
            const winRate = totalBattles > 0 ? Math.round((victories / totalBattles) * 100) : 0;
            
            embed.addFields({
                name: '⚔️ Battle Statistics',
                value: `**Total Battles:** ${totalBattles.toLocaleString()}\n**Victories:** ${victories.toLocaleString()}\n**Defeats:** ${defeats.toLocaleString()}\n**Win Rate:** ${winRate}%`,
                inline: true
            });
            
            // Rewards statistics
            const totalExp = parseInt(battle.total_exp) || 0;
            const totalGold = parseInt(battle.total_gold) || 0;
            const totalGems = parseInt(battle.total_gems) || 0;
            
            embed.addFields({
                name: '🎁 Rewards Distributed',
                value: `**Total EXP:** ${totalExp.toLocaleString()}\n**Total Gold:** ${totalGold.toLocaleString()} 🪙\n**Total Gems:** ${totalGems.toLocaleString()} 💎`,
                inline: true
            });
            
            // Economy statistics
            if (economyStats.length > 0) {
                let economyText = '';
                economyStats.forEach(stat => {
                    const type = stat.type.charAt(0).toUpperCase() + stat.type.slice(1);
                    economyText += `**${type}:** ${stat.count} transactions\n`;
                });
                
                embed.addFields({
                    name: '💰 Economy Activity',
                    value: economyText,
                    inline: false
                });
            }
            
            // Top players
            if (topPlayers.length > 0) {
                let topPlayersText = '';
                topPlayers.forEach((player, index) => {
                    const medal = index === 0 ? '🥇' : index === 1 ? '🥈' : index === 2 ? '🥉' : `${index + 1}.`;
                    topPlayersText += `${medal} **${player.user.username}** - Level ${player.level}\n`;
                });
                
                embed.addFields({
                    name: '🏆 Top Players',
                    value: topPlayersText,
                    inline: false
                });
            }
            
            // Server health indicators
            const healthIndicators = [];
            
            if (activeUsers / totalUsers > 0.3) {
                healthIndicators.push('🟢 High User Activity');
            } else if (activeUsers / totalUsers > 0.1) {
                healthIndicators.push('🟡 Moderate User Activity');
            } else {
                healthIndicators.push('🔴 Low User Activity');
            }
            
            if (winRate > 60) {
                healthIndicators.push('🟢 Balanced Combat');
            } else if (winRate > 40) {
                healthIndicators.push('🟡 Moderate Combat Balance');
            } else {
                healthIndicators.push('🔴 Combat Needs Balancing');
            }
            
            if (totalVips / totalUsers > 0.05) {
                healthIndicators.push('🟢 Healthy Monetization');
            } else {
                healthIndicators.push('🟡 Low VIP Conversion');
            }
            
            embed.addFields({
                name: '🏥 Server Health',
                value: healthIndicators.join('\n'),
                inline: false
            });
            
            embed.addFields({
                name: '📅 Report Info',
                value: `**Generated by:** ${interaction.user.username}\n**Timestamp:** <t:${Math.floor(Date.now() / 1000)}:F>\n**Timeframe:** ${timeframeName[timeframe]}`,
                inline: false
            });
            
            embed.setFooter({ text: 'Use different timeframes to analyze trends • Data updates in real-time' });
            
            await interaction.editReply({ embeds: [embed] });
            
        } catch (error) {
            console.error('Error in admin-server-stats command:', error);
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Command Error',
                'An error occurred while generating server statistics. Please try again.'
            );
            
            if (interaction.deferred) {
                await interaction.editReply({ embeds: [embed] });
            } else {
                await interaction.reply({ embeds: [embed], ephemeral: true });
            }
        }
    },
    
    isAdmin(userId) {
        const adminIds = process.env.ADMIN_IDS ? process.env.ADMIN_IDS.split(',') : [];
        return adminIds.includes(userId);
    }
};

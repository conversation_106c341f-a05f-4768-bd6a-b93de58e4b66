# Test Environment Configuration
NODE_ENV=test

# Discord Bot Configuration (Test)
DISCORD_TOKEN=test-token
CLIENT_ID=test-client-id
PREFIX=!

# Test Database Configuration
DATABASE_URL=postgresql://test:test@localhost:5432/afk_game_bot_test
DB_HOST=localhost
DB_PORT=5432
DB_NAME=afk_game_bot_test
DB_USER=test
DB_PASSWORD=test

# Admin Configuration (Test)
ADMIN_IDS=123456789,987654321

# Bot Configuration (Test)
LOG_LEVEL=error

# Disable external services in tests
ENABLE_VIP_SYSTEM=true
ENABLE_PET_SYSTEM=true
ENABLE_AFK_FARMING=true
ENABLE_GACHA_SYSTEM=true

# Test-specific settings
MAX_COMMANDS_PER_MINUTE=1000
MAX_BATTLES_PER_HOUR=1000

# Game Balance (Test)
BASE_EXP_MULTIPLIER=1.0
BASE_GOLD_MULTIPLIER=1.0
BASE_DROP_RATE_MULTIPLIER=1.0

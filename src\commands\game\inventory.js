const { User, UserItem, Item } = require('../../database/database');
const GameEmbedBuilder = require('../../utils/embedBuilder');

module.exports = {
    data: {
        name: 'inventory',
        aliases: ['inv', 'bag', 'items'],
        description: 'View your inventory and items',
        usage: '!inventory [page] [type]',
        cooldown: 3
    },
    async execute(message, args) {
        try {
            // Get user
            const user = await User.findByDiscordId(message.author.id);
            
            // Parse arguments
            let page = 1;
            let itemType = null;
            
            if (args.length > 0) {
                // Check if first argument is a number (page)
                const firstArg = args[0];
                if (!isNaN(firstArg) && parseInt(firstArg) > 0) {
                    page = parseInt(firstArg);
                    if (args.length > 1) {
                        itemType = args[1].toLowerCase();
                    }
                } else {
                    // First argument is item type
                    itemType = firstArg.toLowerCase();
                    if (args.length > 1 && !isNaN(args[1])) {
                        page = parseInt(args[1]);
                    }
                }
            }
            
            // Validate item type
            const validTypes = ['weapon', 'armor', 'helmet', 'boots', 'ring', 'necklace', 'consumable', 'material'];
            if (itemType && !validTypes.includes(itemType)) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Invalid Item Type',
                    `Valid types: ${validTypes.join(', ')}`
                );
                return message.reply({ embeds: [embed] });
            }
            
            // Build query options
            const itemsPerPage = 10;
            const offset = (page - 1) * itemsPerPage;
            
            const queryOptions = {
                limit: itemsPerPage,
                offset: offset
            };
            
            if (itemType) {
                queryOptions.type = itemType;
            }
            
            // Get user items
            const userItems = await UserItem.findInventoryByUser(user.id, queryOptions);
            
            // Get total count for pagination
            const whereClause = {
                user_id: user.id,
                quantity: { [require('sequelize').Op.gt]: 0 }
            };
            
            if (itemType) {
                whereClause['$item.type$'] = itemType;
            }
            
            const totalItems = await UserItem.count({
                where: whereClause,
                include: ['item']
            });
            
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            
            // Validate page number
            if (page > totalPages && totalPages > 0) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Invalid Page',
                    `Page ${page} doesn't exist. Maximum page: ${totalPages}`
                );
                return message.reply({ embeds: [embed] });
            }
            
            // Create inventory embed
            const typeText = itemType ? ` (${itemType.charAt(0).toUpperCase() + itemType.slice(1)})` : '';
            const embed = GameEmbedBuilder.createInventoryEmbed(user, userItems, page, totalPages);
            embed.setTitle(`${user.getDisplayName()}'s Inventory${typeText}`);
            
            // Add inventory statistics
            if (userItems.length > 0) {
                const stats = {
                    total: totalItems,
                    equipped: userItems.filter(item => item.is_equipped).length,
                    upgraded: userItems.filter(item => item.upgrade_level > 0).length
                };
                
                embed.addFields({
                    name: '📊 Inventory Stats',
                    value: `**Total Items:** ${stats.total}\n**Equipped:** ${stats.equipped}\n**Upgraded:** ${stats.upgraded}`,
                    inline: true
                });
                
                // Add rarity breakdown
                const rarityCount = {};
                userItems.forEach(userItem => {
                    const rarity = userItem.item.rarity;
                    rarityCount[rarity] = (rarityCount[rarity] || 0) + userItem.quantity;
                });
                
                const rarityText = Object.entries(rarityCount)
                    .map(([rarity, count]) => {
                        const rarityInfo = Item.getRarityInfo(rarity);
                        return `${rarityInfo.emoji} ${rarityInfo.name}: ${count}`;
                    })
                    .join('\n');
                
                if (rarityText) {
                    embed.addFields({
                        name: '✨ By Rarity',
                        value: rarityText,
                        inline: true
                    });
                }
            }
            
            // Add navigation help
            let footerText = `Page ${page}/${totalPages || 1}`;
            if (totalPages > 1) {
                footerText += ` | Use !inv ${page + 1} for next page`;
            }
            if (itemType) {
                footerText += ` | Showing ${itemType} items only`;
            } else {
                footerText += ` | Use !inv [type] to filter by item type`;
            }
            
            embed.setFooter({ text: footerText });
            
            // Add quick action instructions
            if (userItems.length > 0) {
                let instructions = '**Quick Actions:**\n';
                instructions += '`!equip <item name>` - Equip an item\n';
                instructions += '`!sell <item name>` - Sell an item\n';
                instructions += '`!upgrade <item name>` - Upgrade an item';
                
                embed.addFields({
                    name: '⚡ Quick Actions',
                    value: instructions,
                    inline: false
                });
            }
            
            const reply = await message.reply({ embeds: [embed] });
            
            // Add navigation reactions for multi-page inventories
            if (totalPages > 1) {
                if (page > 1) {
                    await reply.react('⬅️');
                }
                if (page < totalPages) {
                    await reply.react('➡️');
                }
                
                // Add type filter reactions
                await reply.react('⚔️'); // Weapons
                await reply.react('🛡️'); // Armor
                await reply.react('🧪'); // Consumables
                await reply.react('🔧'); // Materials
                await reply.react('📋'); // All items
                
                // Create reaction collector
                const filter = (reaction, user) => {
                    return ['⬅️', '➡️', '⚔️', '🛡️', '🧪', '🔧', '📋'].includes(reaction.emoji.name) && 
                           user.id === message.author.id;
                };
                
                const collector = reply.createReactionCollector({ filter, time: 60000 });
                
                collector.on('collect', async (reaction, user) => {
                    await reaction.users.remove(user.id);
                    
                    let newArgs = [];
                    
                    switch (reaction.emoji.name) {
                        case '⬅️':
                            if (page > 1) {
                                newArgs = [page - 1];
                                if (itemType) newArgs.push(itemType);
                            }
                            break;
                        case '➡️':
                            if (page < totalPages) {
                                newArgs = [page + 1];
                                if (itemType) newArgs.push(itemType);
                            }
                            break;
                        case '⚔️':
                            newArgs = ['weapon'];
                            break;
                        case '🛡️':
                            newArgs = ['armor'];
                            break;
                        case '🧪':
                            newArgs = ['consumable'];
                            break;
                        case '🔧':
                            newArgs = ['material'];
                            break;
                        case '📋':
                            newArgs = [page];
                            break;
                    }
                    
                    if (newArgs.length > 0) {
                        try {
                            await this.execute(message, newArgs);
                            await reply.delete();
                        } catch (error) {
                            console.error('Error in inventory navigation:', error);
                        }
                    }
                });
                
                collector.on('end', () => {
                    reply.reactions.removeAll().catch(console.error);
                });
            }
            
        } catch (error) {
            console.error('Error in inventory command:', error);
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Inventory Error',
                'An error occurred while fetching your inventory. Please try again.'
            );
            await message.reply({ embeds: [embed] });
        }
    }
};

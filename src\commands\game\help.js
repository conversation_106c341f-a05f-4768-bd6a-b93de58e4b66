const GameEmbedBuilder = require('../../utils/embedBuilder');

module.exports = {
    data: {
        name: 'help',
        aliases: ['h', 'commands', 'guide'],
        description: 'Show help information and available commands',
        usage: 'ihelp [command]',
        cooldown: 5
    },
    async execute(message, args) {
        try {
            // If specific command requested
            if (args.length > 0) {
                const commandName = args[0].toLowerCase();
                const command = message.client.commands.get(commandName) ||
                               message.client.commands.find(cmd => cmd.data.aliases && cmd.data.aliases.includes(commandName));
                
                if (!command) {
                    const embed = GameEmbedBuilder.createErrorEmbed(
                        'Command Not Found',
                        `Command \`${commandName}\` not found. Use \`ihelp\` to see all commands.`
                    );
                    return message.reply({ embeds: [embed] });
                }
                
                // Show detailed command help
                const embed = GameEmbedBuilder.createInfoEmbed(
                    `📖 Command: ${command.data.name}`,
                    command.data.description
                );
                
                embed.addFields(
                    {
                        name: '📝 Usage',
                        value: `\`${command.data.usage}\``,
                        inline: true
                    },
                    {
                        name: '⏱️ Cooldown',
                        value: `${command.data.cooldown || 3} seconds`,
                        inline: true
                    }
                );
                
                if (command.data.aliases && command.data.aliases.length > 0) {
                    embed.addFields({
                        name: '🔄 Aliases',
                        value: command.data.aliases.map(alias => `\`!${alias}\``).join(', '),
                        inline: false
                    });
                }
                
                return message.reply({ embeds: [embed] });
            }
            
            // Show general help
            const embed = GameEmbedBuilder.createInfoEmbed(
                '🎮 AFK Game Bot - Help',
                'Welcome to the AFK Game Bot! Here are all available commands:'
            );
            
            // Game Commands
            embed.addFields({
                name: '⚔️ Combat Commands',
                value: [
                    '`ifight` (`if`) - Fight a random monster',
                    '`iadventure` (`iadv`) - Go on an adventure',
                    '`iboss` - Fight a boss (Level 20+)',
                ].join('\n'),
                inline: false
            });
            
            // Character Commands
            embed.addFields({
                name: '👤 Character Commands',
                value: [
                    '`iprofile` (`ip`) - View your character profile',
                    '`iinventory` (`iinv`) - View your items',
                    '`istats` - View detailed character stats',
                ].join('\n'),
                inline: false
            });
            
            // Economy Commands
            embed.addFields({
                name: '💰 Economy Commands',
                value: [
                    '`ishop` - Browse the item shop',
                    '`ibuy <item>` - Purchase an item',
                    '`isell <item>` - Sell an item',
                    '`idaily` - Claim daily rewards',
                ].join('\n'),
                inline: false
            });
            
            // Equipment Commands
            embed.addFields({
                name: '🛡️ Equipment Commands',
                value: [
                    '`iequip <item>` - Equip an item',
                    '`iunequip <slot>` - Unequip from slot',
                    '`iupgrade <item>` - Upgrade an item',
                ].join('\n'),
                inline: false
            });
            
            // Social Commands
            embed.addFields({
                name: '🏆 Social Commands',
                value: [
                    '`ileaderboard` (`ilb`) - View leaderboards',
                    '`icompare <user>` - Compare stats with another player',
                ].join('\n'),
                inline: false
            });
            
            // VIP & Premium
            embed.addFields({
                name: '👑 VIP & Premium',
                value: [
                    '`ivip` - View VIP information',
                    '`igacha` - Try your luck with gacha',
                    '`ipremium` - View premium features',
                ].join('\n'),
                inline: false
            });
            
            // Game Information
            embed.addFields({
                name: '📊 Game Information',
                value: [
                    '**Cooldowns:** Fight (30s), Adventure (5m), Boss (1h), Daily (24h)',
                    '**VIP Benefits:** Reduced cooldowns, bonus gold, daily gems',
                    '**Equipment Slots:** Weapon, Armor, Helmet, Boots, Ring, Necklace',
                    '**Item Rarities:** Common → Rare → Epic → Legendary → Mythic',
                ].join('\n'),
                inline: false
            });
            
            // Tips!
            embed.addFields({
                name: '💡 Tips',
                value: [
                    '• Use `ihelp <command>` for detailed command info',
                    '• Fight monsters to gain XP and gold',
                    '• Equip better items to increase your stats',
                    '• Go on adventures for special rewards',
                    '• Check your profile regularly to track progress',
                ].join('\n'),
                inline: false
            });
            
            // Footer with additional info
            embed.setFooter({ 
                text: `Prefix: ${process.env.PREFIX || 'i'} | Use reactions for quick navigation | Bot by AFK Game Team` 
            });
            
            const helpMessage = await message.reply({ embeds: [embed] });
            
            // Add category navigation reactions
            await helpMessage.react('⚔️'); // Combat
            await helpMessage.react('👤'); // Character
            await helpMessage.react('💰'); // Economy
            await helpMessage.react('🛡️'); // Equipment
            await helpMessage.react('🏆'); // Social
            await helpMessage.react('👑'); // VIP
            await helpMessage.react('❓'); // General help
            
            // Create reaction collector for category navigation
            const filter = (reaction, user) => {
                return ['⚔️', '👤', '💰', '🛡️', '🏆', '👑', '❓'].includes(reaction.emoji.name) && 
                       user.id === message.author.id;
            };
            
            const collector = helpMessage.createReactionCollector({ filter, time: 120000 });
            
            collector.on('collect', async (reaction, user) => {
                await reaction.users.remove(user.id);
                
                let categoryEmbed;
                
                switch (reaction.emoji.name) {
                    case '⚔️':
                        categoryEmbed = this.createCombatHelpEmbed();
                        break;
                    case '👤':
                        categoryEmbed = this.createCharacterHelpEmbed();
                        break;
                    case '💰':
                        categoryEmbed = this.createEconomyHelpEmbed();
                        break;
                    case '🛡️':
                        categoryEmbed = this.createEquipmentHelpEmbed();
                        break;
                    case '🏆':
                        categoryEmbed = this.createSocialHelpEmbed();
                        break;
                    case '👑':
                        categoryEmbed = this.createVipHelpEmbed();
                        break;
                    case '❓':
                        // Return to main help
                        await this.execute(message, []);
                        await helpMessage.delete();
                        return;
                }
                
                if (categoryEmbed) {
                    await helpMessage.edit({ embeds: [categoryEmbed] });
                }
            });
            
            collector.on('end', () => {
                helpMessage.reactions.removeAll().catch(console.error);
            });
            
        } catch (error) {
            console.error('Error in help command:', error);
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Help Error',
                'An error occurred while displaying help. Please try again.'
            );
            await message.reply({ embeds: [embed] });
        }
    },
    
    createCombatHelpEmbed() {
        const embed = GameEmbedBuilder.createInfoEmbed(
            '⚔️ Combat Help',
            'Learn about combat mechanics and commands'
        );
        
        embed.addFields(
            {
                name: '🗡️ Fighting',
                value: [
                    '`ifight` - Fight random monsters for XP and gold',
                    '`iboss` - Challenge powerful bosses (Level 20+)',
                    '`iadventure` - Explore for treasures and encounters',
                ].join('\n'),
                inline: false
            },
            {
                name: '📊 Combat Stats',
                value: [
                    '**Attack** - Determines damage dealt',
                    '**Defense** - Reduces incoming damage',
                    '**Crit Rate** - Chance for critical hits',
                    '**Crit Damage** - Multiplier for critical hits',
                ].join('\n'),
                inline: false
            },
            {
                name: '⏰ Cooldowns',
                value: [
                    '**Fight:** 30 seconds',
                    '**Adventure:** 5 minutes',
                    '**Boss:** 1 hour',
                    '*VIP reduces all cooldowns*',
                ].join('\n'),
                inline: false
            }
        );
        
        return embed;
    },
    
    createCharacterHelpEmbed() {
        const embed = GameEmbedBuilder.createInfoEmbed(
            '👤 Character Help',
            'Manage your character and view statistics'
        );
        
        embed.addFields(
            {
                name: '📋 Character Commands',
                value: [
                    '`iprofile` - View your character overview',
                    '`istats` - Detailed character statistics',
                    '`iinventory` - View your items and equipment',
                ].join('\n'),
                inline: false
            },
            {
                name: '📈 Progression',
                value: [
                    '**Level:** Increases stats and unlocks content',
                    '**Experience:** Gained from combat and adventures',
                    '**Prestige:** Reset level for permanent bonuses',
                ].join('\n'),
                inline: false
            }
        );
        
        return embed;
    },
    
    createEconomyHelpEmbed() {
        const embed = GameEmbedBuilder.createInfoEmbed(
            '💰 Economy Help',
            'Learn about the game economy and trading'
        );
        
        embed.addFields(
            {
                name: '💱 Currencies',
                value: [
                    '**Gold 🪙** - Primary currency from combat',
                    '**Gems 💎** - Premium currency from purchases',
                ].join('\n'),
                inline: false
            },
            {
                name: '🏪 Shopping',
                value: [
                    '`ishop` - Browse available items',
                    '`ibuy <item>` - Purchase items',
                    '`isell <item>` - Sell items for gold',
                ].join('\n'),
                inline: false
            },
            {
                name: '🎁 Daily Rewards',
                value: [
                    '`idaily` - Claim daily login rewards',
                    'Streak bonuses for consecutive days',
                    'VIP players get bonus rewards',
                ].join('\n'),
                inline: false
            }
        );
        
        return embed;
    },
    
    createEquipmentHelpEmbed() {
        const embed = GameEmbedBuilder.createInfoEmbed(
            '🛡️ Equipment Help',
            'Learn about equipment and upgrades'
        );
        
        embed.addFields(
            {
                name: '⚔️ Equipment Slots',
                value: [
                    '**Weapon** - Increases attack',
                    '**Armor** - Increases defense',
                    '**Helmet** - Balanced stats',
                    '**Boots** - Speed and agility',
                    '**Ring** - Special bonuses',
                    '**Necklace** - Magical properties',
                ].join('\n'),
                inline: false
            },
            {
                name: '✨ Item Rarities',
                value: [
                    '⚪ **Common** - Basic items',
                    '🟢 **Rare** - Better stats',
                    '🟣 **Epic** - Great bonuses',
                    '🟠 **Legendary** - Powerful items',
                    '🔴 **Mythic** - Ultimate equipment',
                ].join('\n'),
                inline: false
            }
        );
        
        return embed;
    },
    
    createSocialHelpEmbed() {
        const embed = GameEmbedBuilder.createInfoEmbed(
            '🏆 Social Help',
            'Compete with other players'
        );
        
        embed.addFields(
            {
                name: '📊 Leaderboards',
                value: [
                    '`ileaderboard level` - Top players by level',
                    '`ileaderboard gold` - Richest players',
                    '`ileaderboard battles` - Most victories',
                ].join('\n'),
                inline: false
            }
        );
        
        return embed;
    },
    
    createVipHelpEmbed() {
        const embed = GameEmbedBuilder.createInfoEmbed(
            '👑 VIP Help',
            'Learn about VIP benefits and premium features'
        );
        
        embed.addFields(
            {
                name: '⭐ VIP Levels',
                value: [
                    '**VIP 1** - 25% cooldown reduction, 10% gold bonus',
                    '**VIP 2** - 35% cooldown reduction, 20% gold bonus',
                    '**VIP 3** - 45% cooldown reduction, 30% gold bonus',
                    '**VIP 4** - 55% cooldown reduction, 50% gold bonus',
                    '**VIP 5** - 65% cooldown reduction, 100% gold bonus',
                ].join('\n'),
                inline: false
            },
            {
                name: '🎰 Premium Features',
                value: [
                    '`igacha` - Try premium item gacha',
                    '`ivip` - View your VIP status',
                    'Daily gem rewards for VIP players',
                ].join('\n'),
                inline: false
            }
        );
        
        return embed;
    }
};

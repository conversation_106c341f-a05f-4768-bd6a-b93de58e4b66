const Discord = require('discord.js');

module.exports = {
    data: {
        name: 'testbuttons',
        aliases: ['tb'],
        description: 'Test buttons functionality',
        usage: 'itestbuttons',
        cooldown: 5
    },
    
    async execute(message, args) {
        try {
            console.log('Creating test buttons...');
            
            const button1 = new Discord.ButtonBuilder()
                .setCustomId('test_button_1')
                .setLabel('Button 1')
                .setStyle(Discord.ButtonStyle.Primary);
            
            const button2 = new Discord.ButtonBuilder()
                .setCustomId('test_button_2')
                .setLabel('Button 2')
                .setStyle(Discord.ButtonStyle.Secondary);
            
            const row = new Discord.ActionRowBuilder()
                .addComponents(button1, button2);
            
            console.log('Buttons created, sending message...');
            
            const embed = new Discord.EmbedBuilder()
                .setTitle('🧪 Button Test')
                .setDescription('This is a test for buttons functionality')
                .setColor('#00ff00');
            
            await message.reply({
                embeds: [embed],
                components: [row]
            });
            
            console.log('Test message sent with buttons');
            
        } catch (error) {
            console.error('Error in testbuttons command:', error);
            await message.reply('Error creating buttons: ' + error.message);
        }
    }
};

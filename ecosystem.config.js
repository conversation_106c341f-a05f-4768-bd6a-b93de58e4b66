module.exports = {
  apps: [
    {
      name: 'afk-game-bot',
      script: 'src/bot.js',
      instances: 1,
      autorestart: true,
      watch: false,
      max_memory_restart: '1G',
      env: {
        NODE_ENV: 'production',
        LOG_LEVEL: 'info'
      },
      env_development: {
        NODE_ENV: 'development',
        LOG_LEVEL: 'debug'
      },
      // Logging configuration
      log_file: './logs/combined.log',
      out_file: './logs/out.log',
      error_file: './logs/error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      
      // Process management
      min_uptime: '10s',
      max_restarts: 10,
      restart_delay: 4000,
      
      // Advanced features
      exec_mode: 'fork',
      kill_timeout: 5000,
      listen_timeout: 3000,
      
      // Environment variables
      node_args: '--max-old-space-size=1024',
      
      // Monitoring
      pmx: true,
      
      // Auto restart on file changes (only for development)
      ignore_watch: [
        'node_modules',
        'logs',
        'database.sqlite',
        '.git',
        '*.log'
      ],
      
      // Cron restart (optional - restart daily at 3 AM)
      cron_restart: '0 3 * * *',
      
      // Merge logs
      merge_logs: true,
      
      // Time zone
      time: true
    }
  ],

  // Deployment configuration (optional)
  deploy: {
    production: {
      user: 'node',
      host: 'your-server.com',
      ref: 'origin/main',
      repo: '**************:your-username/discord-afk-game-bot.git',
      path: '/var/www/afk-game-bot',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && npm run db:init && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};

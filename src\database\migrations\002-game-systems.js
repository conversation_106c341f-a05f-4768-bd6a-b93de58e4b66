const { DataTypes } = require('sequelize');

module.exports = {
    up: async (queryInterface, Sequelize) => {
        // Create user_items table
        await queryInterface.createTable('user_items', {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            user_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'users',
                    key: 'id'
                },
                onDelete: 'CASCADE'
            },
            item_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'items',
                    key: 'id'
                },
                onDelete: 'CASCADE'
            },
            quantity: {
                type: DataTypes.INTEGER,
                defaultValue: 1
            },
            upgrade_level: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            is_equipped: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            acquired_at: {
                type: DataTypes.DATE,
                defaultValue: DataTypes.NOW
            },
            acquired_from: {
                type: DataTypes.STRING,
                allowNull: true
            },
            unique_properties: {
                type: DataTypes.JSONB,
                defaultValue: {}
            },
            durability: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            max_durability: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            created_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            },
            updated_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            }
        });

        // Create monsters table
        await queryInterface.createTable('monsters', {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            name: {
                type: DataTypes.STRING,
                allowNull: false
            },
            description: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            type: {
                type: DataTypes.ENUM('normal', 'elite', 'boss', 'raid_boss'),
                allowNull: false,
                defaultValue: 'normal'
            },
            level: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            health: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            attack: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            defense: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            crit_rate: {
                type: DataTypes.DECIMAL(5, 2),
                defaultValue: 5.00
            },
            crit_damage: {
                type: DataTypes.DECIMAL(6, 2),
                defaultValue: 150.00
            },
            base_exp: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            base_gold: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            gem_reward: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            drop_table: {
                type: DataTypes.JSONB,
                defaultValue: []
            },
            rare_drop_table: {
                type: DataTypes.JSONB,
                defaultValue: []
            },
            min_level_requirement: {
                type: DataTypes.INTEGER,
                defaultValue: 1
            },
            vip_requirement: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            location: {
                type: DataTypes.STRING,
                allowNull: true
            },
            spawn_rate: {
                type: DataTypes.DECIMAL(5, 4),
                defaultValue: 1.0000
            },
            emoji: {
                type: DataTypes.STRING,
                defaultValue: '👹'
            },
            color: {
                type: DataTypes.STRING(7),
                defaultValue: '#FF0000'
            },
            special_abilities: {
                type: DataTypes.JSONB,
                defaultValue: []
            },
            is_active: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            is_event_monster: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            event_start: {
                type: DataTypes.DATE,
                allowNull: true
            },
            event_end: {
                type: DataTypes.DATE,
                allowNull: true
            },
            created_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            },
            updated_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            }
        });

        // Create battle_logs table
        await queryInterface.createTable('battle_logs', {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            user_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'users',
                    key: 'id'
                },
                onDelete: 'CASCADE'
            },
            monster_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'monsters',
                    key: 'id'
                }
            },
            battle_type: {
                type: DataTypes.ENUM('fight', 'boss', 'adventure', 'afk'),
                allowNull: false,
                defaultValue: 'fight'
            },
            result: {
                type: DataTypes.ENUM('victory', 'defeat', 'flee'),
                allowNull: false
            },
            player_level: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            player_attack: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            player_defense: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            player_health_before: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            player_health_after: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            monster_level: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            monster_health: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            monster_attack: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            monster_defense: {
                type: DataTypes.INTEGER,
                allowNull: false
            },
            turns_taken: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            damage_dealt: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            damage_received: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            critical_hits: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            exp_gained: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            gold_gained: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            gems_gained: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            items_dropped: {
                type: DataTypes.JSONB,
                defaultValue: []
            },
            battle_details: {
                type: DataTypes.JSONB,
                defaultValue: {}
            },
            duration_seconds: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            location: {
                type: DataTypes.STRING,
                allowNull: true
            },
            special_events: {
                type: DataTypes.JSONB,
                defaultValue: []
            },
            created_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            },
            updated_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            }
        });

        // Create shop table
        await queryInterface.createTable('shop', {
            id: {
                type: DataTypes.INTEGER,
                primaryKey: true,
                autoIncrement: true
            },
            item_id: {
                type: DataTypes.INTEGER,
                allowNull: false,
                references: {
                    model: 'items',
                    key: 'id'
                },
                onDelete: 'CASCADE'
            },
            category: {
                type: DataTypes.ENUM('weapons', 'armor', 'accessories', 'consumables', 'materials', 'premium', 'vip_exclusive'),
                allowNull: false,
                defaultValue: 'weapons'
            },
            price_gold: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            price_gems: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            level_requirement: {
                type: DataTypes.INTEGER,
                defaultValue: 1
            },
            vip_requirement: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            stock_quantity: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            max_per_user: {
                type: DataTypes.INTEGER,
                allowNull: true
            },
            reset_period: {
                type: DataTypes.ENUM('never', 'daily', 'weekly', 'monthly'),
                defaultValue: 'never'
            },
            is_available: {
                type: DataTypes.BOOLEAN,
                defaultValue: true
            },
            is_featured: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            is_limited_time: {
                type: DataTypes.BOOLEAN,
                defaultValue: false
            },
            available_from: {
                type: DataTypes.DATE,
                allowNull: true
            },
            available_until: {
                type: DataTypes.DATE,
                allowNull: true
            },
            discount_percentage: {
                type: DataTypes.DECIMAL(5, 2),
                defaultValue: 0.00
            },
            discount_until: {
                type: DataTypes.DATE,
                allowNull: true
            },
            display_order: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            description: {
                type: DataTypes.TEXT,
                allowNull: true
            },
            total_purchases: {
                type: DataTypes.INTEGER,
                defaultValue: 0
            },
            last_purchased: {
                type: DataTypes.DATE,
                allowNull: true
            },
            created_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            },
            updated_at: {
                type: DataTypes.DATE,
                allowNull: false,
                defaultValue: DataTypes.NOW
            }
        });

        // Add indexes
        await queryInterface.addIndex('user_items', ['user_id']);
        await queryInterface.addIndex('user_items', ['item_id']);
        await queryInterface.addIndex('user_items', ['user_id', 'item_id']);
        await queryInterface.addIndex('user_items', ['is_equipped']);

        await queryInterface.addIndex('monsters', ['type']);
        await queryInterface.addIndex('monsters', ['level']);
        await queryInterface.addIndex('monsters', ['location']);
        await queryInterface.addIndex('monsters', ['is_active']);

        await queryInterface.addIndex('battle_logs', ['user_id']);
        await queryInterface.addIndex('battle_logs', ['monster_id']);
        await queryInterface.addIndex('battle_logs', ['battle_type']);
        await queryInterface.addIndex('battle_logs', ['result']);
        await queryInterface.addIndex('battle_logs', ['created_at']);
        await queryInterface.addIndex('battle_logs', ['user_id', 'created_at']);

        await queryInterface.addIndex('shop', ['category']);
        await queryInterface.addIndex('shop', ['is_available']);
        await queryInterface.addIndex('shop', ['is_featured']);
        await queryInterface.addIndex('shop', ['level_requirement']);
        await queryInterface.addIndex('shop', ['vip_requirement']);
        await queryInterface.addIndex('shop', ['display_order']);
    },

    down: async (queryInterface, Sequelize) => {
        await queryInterface.dropTable('shop');
        await queryInterface.dropTable('battle_logs');
        await queryInterface.dropTable('monsters');
        await queryInterface.dropTable('user_items');
    }
};

const { Sequelize } = require('sequelize');
const path = require('path');
const fs = require('fs');

class DatabaseInitializer {
    constructor(sequelize) {
        this.sequelize = sequelize;
        this.migrationsPath = path.join(__dirname, 'migrations');
        this.seedersPath = path.join(__dirname, 'seeders');
    }

    async initialize() {
        try {
            console.log('🔄 Initializing database...');
            
            // Test connection
            await this.testConnection();
            
            // Run migrations
            await this.runMigrations();
            
            // Run seeders
            await this.runSeeders();
            
            console.log('✅ Database initialization completed successfully!');
            
        } catch (error) {
            console.error('❌ Database initialization failed:', error);
            throw error;
        }
    }

    async testConnection() {
        try {
            await this.sequelize.authenticate();
            console.log('✅ Database connection established successfully.');
        } catch (error) {
            console.error('❌ Unable to connect to the database:', error);
            throw error;
        }
    }

    async runMigrations() {
        console.log('🔄 Running database migrations...');
        
        try {
            // Create migrations table if it doesn't exist
            await this.sequelize.query(`
                CREATE TABLE IF NOT EXISTS "SequelizeMeta" (
                    "name" VARCHAR(255) NOT NULL PRIMARY KEY
                );
            `);

            // Get list of migration files
            const migrationFiles = fs.readdirSync(this.migrationsPath)
                .filter(file => file.endsWith('.js'))
                .sort();

            // Get already executed migrations
            const [executedMigrations] = await this.sequelize.query(
                'SELECT name FROM "SequelizeMeta" ORDER BY name;'
            );
            const executedNames = executedMigrations.map(row => row.name);

            // Run pending migrations
            for (const file of migrationFiles) {
                if (!executedNames.includes(file)) {
                    console.log(`  📄 Running migration: ${file}`);
                    
                    const migration = require(path.join(this.migrationsPath, file));
                    await migration.up(this.sequelize.getQueryInterface(), this.sequelize);
                    
                    // Record migration as executed
                    await this.sequelize.query(
                        'INSERT INTO "SequelizeMeta" (name) VALUES (?);',
                        { replacements: [file] }
                    );
                    
                    console.log(`  ✅ Migration completed: ${file}`);
                }
            }

            console.log('✅ All migrations completed successfully.');
            
        } catch (error) {
            console.error('❌ Migration failed:', error);
            throw error;
        }
    }

    async runSeeders() {
        console.log('🔄 Running database seeders...');
        
        try {
            // Create seeders table if it doesn't exist
            await this.sequelize.query(`
                CREATE TABLE IF NOT EXISTS "SequelizeData" (
                    "name" VARCHAR(255) NOT NULL PRIMARY KEY
                );
            `);

            // Get list of seeder files
            const seederFiles = fs.readdirSync(this.seedersPath)
                .filter(file => file.endsWith('.js'))
                .sort();

            // Get already executed seeders
            const [executedSeeders] = await this.sequelize.query(
                'SELECT name FROM "SequelizeData" ORDER BY name;'
            );
            const executedNames = executedSeeders.map(row => row.name);

            // Run pending seeders
            for (const file of seederFiles) {
                if (!executedNames.includes(file)) {
                    console.log(`  🌱 Running seeder: ${file}`);
                    
                    const seeder = require(path.join(this.seedersPath, file));
                    await seeder.up(this.sequelize.getQueryInterface(), this.sequelize);
                    
                    // Record seeder as executed
                    await this.sequelize.query(
                        'INSERT INTO "SequelizeData" (name) VALUES (?);',
                        { replacements: [file] }
                    );
                    
                    console.log(`  ✅ Seeder completed: ${file}`);
                }
            }

            console.log('✅ All seeders completed successfully.');
            
        } catch (error) {
            console.error('❌ Seeder failed:', error);
            throw error;
        }
    }

    async rollbackMigration(migrationName) {
        console.log(`🔄 Rolling back migration: ${migrationName}`);
        
        try {
            const migrationFile = path.join(this.migrationsPath, migrationName);
            
            if (!fs.existsSync(migrationFile)) {
                throw new Error(`Migration file not found: ${migrationName}`);
            }

            const migration = require(migrationFile);
            await migration.down(this.sequelize.getQueryInterface(), this.sequelize);
            
            // Remove from executed migrations
            await this.sequelize.query(
                'DELETE FROM "SequelizeMeta" WHERE name = ?;',
                { replacements: [migrationName] }
            );
            
            console.log(`✅ Migration rolled back: ${migrationName}`);
            
        } catch (error) {
            console.error(`❌ Migration rollback failed: ${migrationName}`, error);
            throw error;
        }
    }

    async resetDatabase() {
        console.log('🔄 Resetting database...');
        
        try {
            // Get all migration files in reverse order
            const migrationFiles = fs.readdirSync(this.migrationsPath)
                .filter(file => file.endsWith('.js'))
                .sort()
                .reverse();

            // Roll back all migrations
            for (const file of migrationFiles) {
                try {
                    await this.rollbackMigration(file);
                } catch (error) {
                    console.warn(`⚠️ Could not rollback migration ${file}:`, error.message);
                }
            }

            // Drop meta tables
            await this.sequelize.query('DROP TABLE IF EXISTS "SequelizeMeta";');
            await this.sequelize.query('DROP TABLE IF EXISTS "SequelizeData";');
            
            console.log('✅ Database reset completed.');
            
        } catch (error) {
            console.error('❌ Database reset failed:', error);
            throw error;
        }
    }

    async getStatus() {
        try {
            // Get executed migrations
            const [executedMigrations] = await this.sequelize.query(
                'SELECT name FROM "SequelizeMeta" ORDER BY name;'
            ).catch(() => [[]]);

            // Get executed seeders
            const [executedSeeders] = await this.sequelize.query(
                'SELECT name FROM "SequelizeData" ORDER BY name;'
            ).catch(() => [[]]);

            // Get available migrations
            const availableMigrations = fs.readdirSync(this.migrationsPath)
                .filter(file => file.endsWith('.js'))
                .sort();

            // Get available seeders
            const availableSeeders = fs.readdirSync(this.seedersPath)
                .filter(file => file.endsWith('.js'))
                .sort();

            return {
                migrations: {
                    executed: executedMigrations.map(row => row.name),
                    available: availableMigrations,
                    pending: availableMigrations.filter(file => 
                        !executedMigrations.some(row => row.name === file)
                    )
                },
                seeders: {
                    executed: executedSeeders.map(row => row.name),
                    available: availableSeeders,
                    pending: availableSeeders.filter(file => 
                        !executedSeeders.some(row => row.name === file)
                    )
                }
            };
            
        } catch (error) {
            console.error('❌ Could not get database status:', error);
            throw error;
        }
    }

    async createBackup(backupName) {
        console.log(`🔄 Creating database backup: ${backupName}`);
        
        try {
            // This is a simplified backup - in production you'd use pg_dump or similar
            const backupData = {
                timestamp: new Date().toISOString(),
                name: backupName,
                // Add actual backup logic here based on your database type
            };

            const backupPath = path.join(__dirname, 'backups', `${backupName}.json`);
            
            // Ensure backups directory exists
            const backupsDir = path.dirname(backupPath);
            if (!fs.existsSync(backupsDir)) {
                fs.mkdirSync(backupsDir, { recursive: true });
            }

            fs.writeFileSync(backupPath, JSON.stringify(backupData, null, 2));
            
            console.log(`✅ Backup created: ${backupPath}`);
            
        } catch (error) {
            console.error(`❌ Backup creation failed: ${backupName}`, error);
            throw error;
        }
    }
}

module.exports = DatabaseInitializer;

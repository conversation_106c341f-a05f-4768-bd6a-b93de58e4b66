const { User, Character, Item, UserItem, Transaction } = require('../../database/database');
const GameEmbedBuilder = require('../../utils/embedBuilder');

module.exports = {
    data: {
        name: 'buy',
        aliases: ['purchase', 'get'],
        description: 'Purchase an item from the shop',
        usage: 'ibuy <item_name> [quantity]',
        cooldown: 3
    },
    async execute(message, args) {
        try {
            if (args.length === 0) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Missing Item Name',
                    'Please specify the item you want to buy.\n**Usage:** `!buy <item_name> [quantity]`'
                );
                return message.reply({ embeds: [embed] });
            }
            
            // Get user and character
            const user = await User.findByDiscordId(message.author.id);
            const character = await Character.findOne({
                where: { user_id: user.id }
            });
            
            // Parse arguments
            let quantity = 1;
            let itemName = args.join(' ').toLowerCase();
            
            // Check if last argument is a number (quantity)
            const lastArg = args[args.length - 1];
            if (!isNaN(lastArg) && parseInt(lastArg) > 0) {
                quantity = parseInt(lastArg);
                itemName = args.slice(0, -1).join(' ').toLowerCase();
            }
            
            // Find the item directly from Item model
            const { Op, fn, col, where } = require('sequelize');
            const items = await Item.findAll({
                where: where(
                    fn('LOWER', col('name')),
                    { [Op.like]: `%${itemName.toLowerCase()}%` }
                )
            });

            if (items.length === 0) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Item Not Found',
                    `No item matching "${itemName}" found in the shop.\nUse \`!shop\` to browse available items.`
                );
                return message.reply({ embeds: [embed] });
            }

            // If multiple matches, show options
            if (items.length > 1) {
                const embed = GameEmbedBuilder.createInfoEmbed(
                    'Multiple Items Found',
                    'Multiple items match your search. Please be more specific:'
                );

                let itemList = '';
                items.forEach((item, index) => {
                    const rarityInfo = item.getRarityInfo();
                    const price = item.base_price;
                    const gemPrice = item.gem_price;

                    itemList += `**${index + 1}.** ${rarityInfo.emoji} ${item.name}\n`;
                    let priceText = `💰 ${price.toLocaleString()} 🪙`;
                    if (gemPrice > 0) {
                        priceText += ` | ${gemPrice} 💎`;
                    }
                    itemList += `*${item.type} • Level ${item.level_requirement} • ${priceText}*\n\n`;
                });

                embed.setDescription(itemList);
                embed.setFooter({ text: 'Use the exact item name to purchase it' });

                return message.reply({ embeds: [embed] });
            }

            const item = items[0];
            
            // Check if item is available for user (level requirement)
            if (character.level < item.level_requirement) {
                const reason = `You need to be level **${item.level_requirement}** to buy this item.\nYour current level: **${character.level}**`;
                const embed = GameEmbedBuilder.createErrorEmbed('Item Not Available', reason);
                return message.reply({ embeds: [embed] });
            }
            
            // Calculate total cost
            const goldPrice = item.base_price || 0;
            const gemPrice = item.gem_price || 0;
            const totalGoldCost = goldPrice * quantity;
            const totalGemCost = gemPrice * quantity;
            
            // Check if user can afford it
            let canAfford = true;
            let affordabilityMessage = '';
            
            if (totalGoldCost > 0 && character.gold < totalGoldCost) {
                canAfford = false;
                affordabilityMessage += `Insufficient gold: need ${totalGoldCost} 🪙, have ${character.gold} 🪙\n`;
            }
            
            if (totalGemCost > 0 && character.gems < totalGemCost) {
                canAfford = false;
                affordabilityMessage += `Insufficient gems: need ${totalGemCost} 💎, have ${character.gems} 💎\n`;
            }
            
            if (!canAfford) {
                const embed = GameEmbedBuilder.createErrorEmbed('Cannot Afford Item', affordabilityMessage);
                return message.reply({ embeds: [embed] });
            }
            
            // Create purchase confirmation
            const rarityInfo = item.getRarityInfo();
            const confirmEmbed = GameEmbedBuilder.createWarningEmbed(
                '🛒 Confirm Purchase',
                `Are you sure you want to buy **${quantity}x ${rarityInfo.emoji} ${item.name}**?`
            );
            
            let costText = '';
            if (totalGoldCost > 0) costText += `**Gold:** ${totalGoldCost} 🪙\n`;
            if (totalGemCost > 0) costText += `**Gems:** ${totalGemCost} 💎\n`;
            
            confirmEmbed.addFields(
                {
                    name: '💰 Total Cost',
                    value: costText,    
                    inline: true
                },
                {
                    name: '📊 Item Info',
                    value: `**Type:** ${item.type}\n**Rarity:** ${rarityInfo.name}\n**Level Req:** ${item.level_requirement}`,
                    inline: true
                }
            );
            
            // Show remaining currency after purchase
            let remainingText = '';
            if (totalGoldCost > 0) remainingText += `**Gold:** ${(character.gold - totalGoldCost).toLocaleString()} 🪙\n`;
            if (totalGemCost > 0) remainingText += `**Gems:** ${(character.gems - totalGemCost).toLocaleString()} 💎\n`;
            
            if (remainingText) {
                confirmEmbed.addFields({
                    name: '💳 Remaining After Purchase',
                    value: remainingText,
                    inline: false
                });
            }
            
            // Show item stats
            if (item.attack_bonus > 0 || item.defense_bonus > 0 || item.health_bonus > 0) {
                let statsText = '';
                if (item.attack_bonus > 0) statsText += `**Attack:** +${item.attack_bonus}\n`;
                if (item.defense_bonus > 0) statsText += `**Defense:** +${item.defense_bonus}\n`;
                if (item.health_bonus > 0) statsText += `**Health:** +${item.health_bonus}\n`;
                if (item.crit_rate_bonus > 0) statsText += `**Crit Rate:** +${item.crit_rate_bonus}%\n`;
                if (item.crit_damage_bonus > 0) statsText += `**Crit Damage:** +${item.crit_damage_bonus}%\n`;
                if (item.luck_bonus > 0) statsText += `**Luck:** +${item.luck_bonus}\n`;
                
                if (statsText) {
                    confirmEmbed.addFields({
                        name: '⚔️ Item Stats',
                        value: statsText,
                        inline: false
                    });
                }
            }
            
            // No discount system for now (removed with Shop model)
            
            confirmEmbed.setFooter({ text: 'React with ✅ to confirm or ❌ to cancel' });
            
            const confirmMessage = await message.reply({ embeds: [confirmEmbed] });
            console.log('Adding reactions...');
            await confirmMessage.react('✅');
            console.log('Added ✅ reaction');
            await confirmMessage.react('❌');
            console.log('Added ❌ reaction');
            
            // Wait for confirmation
            const filter = (reaction, user) => {
                console.log(`Reaction filter: emoji=${reaction.emoji.name}, user=${user.tag}, author=${message.author.tag}`);
                const isValidEmoji = ['✅', '❌'].includes(reaction.emoji.name);
                const isCorrectUser = user.id === message.author.id;
                console.log(`Valid emoji: ${isValidEmoji}, Correct user: ${isCorrectUser}`);
                return isValidEmoji && isCorrectUser;
            };
            
            console.log('Creating reaction collector...');
            const collector = confirmMessage.createReactionCollector({ filter, time: 30000, max: 1 });
            console.log('Reaction collector created, waiting for reactions...');
            
            collector.on('collect', async (reaction) => {
                console.log(`Buy reaction collected: ${reaction.emoji.name}`);

                if (reaction.emoji.name === '❌') {
                    console.log('Purchase cancelled by user');
                    const cancelEmbed = GameEmbedBuilder.createInfoEmbed(
                        'Purchase Cancelled',
                        'You decided not to buy the item.'
                    );
                    await confirmMessage.edit({ embeds: [cancelEmbed] });
                    return;
                }

                // Process the purchase
                console.log('Processing purchase...');
                try {
                    // Deduct currency
                    console.log(`Deducting ${totalGoldCost} gold and ${totalGemCost} gems`);
                    character.gold -= totalGoldCost;
                    character.gems -= totalGemCost;
                    await character.save();
                    console.log('Currency deducted successfully');

                    // Add item to user's inventory
                    console.log('Adding item to inventory...');
                    let userItem = await UserItem.findOne({
                        where: {
                            user_id: user.id,
                            item_id: item.id
                        }
                    });
                    
                    if (userItem) {
                        // Stack if stackable
                        if (item.type === 'consumable' || item.type === 'material') {
                            userItem.quantity += quantity;
                            await userItem.save();
                        } else {
                            // Create new entry for equipment
                            userItem = await UserItem.create({
                                user_id: user.id,
                                item_id: item.id,
                                quantity: quantity,
                                acquired_from: 'shop'
                            });
                        }
                    } else {
                        // Create new item
                        userItem = await UserItem.create({
                            user_id: user.id,
                            item_id: item.id,
                            quantity: quantity,
                            acquired_from: 'shop'
                        });
                    }

                    // Create transaction record
                    console.log('Creating transaction record...');
                    await Transaction.createPurchase(
                        user.id,
                        item.id,
                        totalGoldCost,
                        totalGemCost,
                        character.gold,
                        character.gems,
                        `Purchased ${quantity}x ${item.name} from shop`
                    );
                    console.log('Transaction record created');

                    // Create success embed
                    console.log('Creating success embed...');
                    const successEmbed = GameEmbedBuilder.createSuccessEmbed(
                        '🛒 Purchase Successful!',
                        `Successfully purchased **${quantity}x ${rarityInfo.emoji} ${item.name}**!`
                    );
                    
                    successEmbed.addFields(
                        {
                            name: '💰 Cost',
                            value: costText,
                            inline: true
                        },
                        {
                            name: '💳 Remaining Currency',
                            value: `**Gold:** ${character.gold.toLocaleString()} 🪙\n**Gems:** ${character.gems.toLocaleString()} 💎`,
                            inline: true
                        }
                    );
                    
                    // Add equipment suggestion
                    if (['weapon', 'armor', 'helmet', 'boots', 'ring', 'necklace'].includes(item.type)) {
                        successEmbed.addFields({
                            name: '⚔️ Quick Tip',
                            value: `Use \`iequip ${item.name}\` to equip this item!`,
                            inline: false
                        });
                    }
                    
                    successEmbed.setFooter({ text: 'Use !inventory to see your new item' });
                    
                    await confirmMessage.edit({ embeds: [successEmbed] });
                    console.log('Purchase completed successfully!');

                } catch (error) {
                    console.error('Error processing purchase:', error);
                    const errorEmbed = GameEmbedBuilder.createErrorEmbed(
                        'Purchase Error',
                        'An error occurred while processing your purchase. Please try again.'
                    );
                    await confirmMessage.edit({ embeds: [errorEmbed] });
                }
            });
            
            collector.on('end', (collected) => {
                console.log(`Buy collector ended. Collected: ${collected.size} reactions`);
                if (collected.size === 0) {
                    console.log('Purchase timed out');
                    const timeoutEmbed = GameEmbedBuilder.createWarningEmbed(
                        'Purchase Timeout',
                        'You took too long to confirm. Purchase cancelled.'
                    );
                    confirmMessage.edit({ embeds: [timeoutEmbed] }).catch(console.error);
                }
                confirmMessage.reactions.removeAll().catch(console.error);
            });
            
        } catch (error) {
            console.error('Error in buy command:', error);
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Purchase Error',
                'An error occurred while processing your purchase. Please try again.'
            );
            await message.reply({ embeds: [embed] });
        }
    }
};

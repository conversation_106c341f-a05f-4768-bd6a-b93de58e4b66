const { DataTypes } = require('sequelize');
const { gameCache } = require('../../utils/performance');

module.exports = (sequelize) => {
    const User = sequelize.define('User', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        discord_id: {
            type: DataTypes.STRING,
            allowNull: false,
            unique: true,
            validate: {
                notEmpty: true,
                len: [17, 19] // Discord ID length
            }
        },
        username: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notEmpty: true,
                len: [1, 32]
            }
        },
        discriminator: {
            type: DataTypes.STRING,
            allowNull: true,
            validate: {
                len: [1, 4] // Allow 1-4 characters to support new Discord system
            }
        },
        avatar_url: {
            type: DataTypes.STRING,
            allowNull: true
        },
        is_banned: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        },
        ban_reason: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        ban_expires_at: {
            type: DataTypes.DATE,
            allowNull: true
        },
        last_seen: {
            type: DataTypes.DATE,
            defaultValue: DataTypes.NOW
        },
        total_spent: {
            type: DataTypes.DECIMAL(10, 2),
            defaultValue: 0.00,
            validate: {
                min: 0
            }
        },
        referral_code: {
            type: DataTypes.STRING(8),
            allowNull: true,
            unique: true
        },
        referred_by: {
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'users',
                key: 'id'
            }
        },
        settings: {
            type: DataTypes.TEXT,
            defaultValue: JSON.stringify({
                notifications: true,
                language: 'vi',
                timezone: 'Asia/Ho_Chi_Minh'
            }),
            get() {
                const value = this.getDataValue('settings');
                return value ? JSON.parse(value) : {};
            },
            set(value) {
                this.setDataValue('settings', JSON.stringify(value));
            }
        }
    }, {
        tableName: 'users',
        indexes: [
            {
                unique: true,
                fields: ['discord_id']
            },
            {
                fields: ['referral_code']
            },
            {
                fields: ['last_seen']
            }
        ],
        hooks: {
            beforeCreate: async (user) => {
                // Generate referral code
                if (!user.referral_code) {
                    user.referral_code = generateReferralCode();
                }
                user.last_seen = new Date();
            },
            beforeUpdate: (user) => {
                user.last_seen = new Date();
            }
        }
    });

    // Instance methods
    User.prototype.isBanned = function() {
        if (!this.is_banned) return false;
        if (!this.ban_expires_at) return true;
        return new Date() < this.ban_expires_at;
    };

    User.prototype.getAvatarURL = function() {
        if (!this.avatar_url) return null;
        return this.avatar_url;
    };

    User.prototype.getDisplayName = function() {
        // Only show discriminator if it's not '0' (new Discord system)
        return this.discriminator && this.discriminator !== '0' ? `${this.username}#${this.discriminator}` : this.username;
    };

    // Static methods
    User.findByDiscordId = async function(discordId) {
        const cacheKey = `user:${discordId}`;
        let user = gameCache.get(cacheKey);

        if (!user) {
            user = await this.findOne({ where: { discord_id: discordId } });

            if (user) {
                gameCache.set(cacheKey, user);
            }
        }

        return user;
    };

    User.findByReferralCode = function(code) {
        return this.findOne({ where: { referral_code: code } });
    };

    return User;
};

function generateReferralCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < 8; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

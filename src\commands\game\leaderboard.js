const { User, Character, BattleLog } = require('../../database/database');
const GameEmbedBuilder = require('../../utils/embedBuilder');

module.exports = {
    data: {
        name: 'leaderboard',
        aliases: ['lb', 'top', 'ranking'],
        description: 'View leaderboards for different categories',
        usage: '!leaderboard [level|gold|battles|vip] [page]',
        cooldown: 10
    },
    async execute(message, args) {
        try {
            // Parse arguments
            let category = 'level';
            let page = 1;
            
            if (args.length > 0) {
                const firstArg = args[0].toLowerCase();
                
                // Check if first argument is a page number
                if (!isNaN(firstArg) && parseInt(firstArg) > 0) {
                    page = parseInt(firstArg);
                } else {
                    // First argument is category
                    category = firstArg;
                    if (args.length > 1 && !isNaN(args[1])) {
                        page = parseInt(args[1]);
                    }
                }
            }
            
            // Validate category
            const validCategories = ['level', 'gold', 'battles', 'vip', 'prestige'];
            if (!validCategories.includes(category)) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Invalid Category',
                    `Valid categories: ${validCategories.join(', ')}\nExample: \`!lb level\` or \`!lb gold 2\``
                );
                return message.reply({ embeds: [embed] });
            }
            
            // If no category specified, show category selection
            if (args.length === 0) {
                const embed = GameEmbedBuilder.createInfoEmbed(
                    '🏆 Leaderboard Categories',
                    'Choose a category to view rankings:'
                );
                
                embed.addFields(
                    {
                        name: '📊 Available Categories',
                        value: [
                            '🏅 **Level** - `!lb level`',
                            '💰 **Gold** - `!lb gold`',
                            '⚔️ **Battles** - `!lb battles`',
                            '👑 **VIP** - `!lb vip`',
                            '✨ **Prestige** - `!lb prestige`'
                        ].join('\n'),
                        inline: false
                    }
                );
                
                embed.setFooter({ text: 'Use !lb <category> [page] to view a specific leaderboard' });
                
                return message.reply({ embeds: [embed] });
            }
            
            // Get leaderboard data
            const playersPerPage = 10;
            const offset = (page - 1) * playersPerPage;
            
            let players, totalPlayers;
            
            switch (category) {
                case 'level':
                    [players, totalPlayers] = await this.getLevelLeaderboard(playersPerPage, offset);
                    break;
                case 'gold':
                    [players, totalPlayers] = await this.getGoldLeaderboard(playersPerPage, offset);
                    break;
                case 'battles':
                    [players, totalPlayers] = await this.getBattleLeaderboard(playersPerPage, offset);
                    break;
                case 'vip':
                    [players, totalPlayers] = await this.getVipLeaderboard(playersPerPage, offset);
                    break;
                case 'prestige':
                    [players, totalPlayers] = await this.getPrestigeLeaderboard(playersPerPage, offset);
                    break;
            }
            
            const totalPages = Math.ceil(totalPlayers / playersPerPage);
            
            // Validate page number
            if (page > totalPages && totalPages > 0) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Invalid Page',
                    `Page ${page} doesn't exist. Maximum page: ${totalPages}`
                );
                return message.reply({ embeds: [embed] });
            }
            
            // Create leaderboard embed
            const embed = this.createLeaderboardEmbed(players, category, page, totalPages, message.author.id);
            
            const leaderboardMessage = await message.reply({ embeds: [embed] });
            
            // Add navigation reactions for multi-page leaderboards
            if (totalPages > 1) {
                if (page > 1) {
                    await leaderboardMessage.react('⬅️');
                }
                if (page < totalPages) {
                    await leaderboardMessage.react('➡️');
                }
                
                // Add category navigation reactions
                await leaderboardMessage.react('🏅'); // Level
                await leaderboardMessage.react('💰'); // Gold
                await leaderboardMessage.react('⚔️'); // Battles
                await leaderboardMessage.react('👑'); // VIP
                await leaderboardMessage.react('✨'); // Prestige
                
                // Create reaction collector
                const filter = (reaction, user) => {
                    return ['⬅️', '➡️', '🏅', '💰', '⚔️', '👑', '✨'].includes(reaction.emoji.name) && 
                           user.id === message.author.id;
                };
                
                const collector = leaderboardMessage.createReactionCollector({ filter, time: 120000 });
                
                collector.on('collect', async (reaction, user) => {
                    await reaction.users.remove(user.id);
                    
                    let newArgs = [];
                    
                    switch (reaction.emoji.name) {
                        case '⬅️':
                            if (page > 1) {
                                newArgs = [category, page - 1];
                            }
                            break;
                        case '➡️':
                            if (page < totalPages) {
                                newArgs = [category, page + 1];
                            }
                            break;
                        case '🏅':
                            newArgs = ['level'];
                            break;
                        case '💰':
                            newArgs = ['gold'];
                            break;
                        case '⚔️':
                            newArgs = ['battles'];
                            break;
                        case '👑':
                            newArgs = ['vip'];
                            break;
                        case '✨':
                            newArgs = ['prestige'];
                            break;
                    }
                    
                    if (newArgs.length > 0) {
                        try {
                            await this.execute(message, newArgs);
                            await leaderboardMessage.delete();
                        } catch (error) {
                            console.error('Error in leaderboard navigation:', error);
                        }
                    }
                });
                
                collector.on('end', () => {
                    leaderboardMessage.reactions.removeAll().catch(console.error);
                });
            }
            
        } catch (error) {
            console.error('Error in leaderboard command:', error);
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Leaderboard Error',
                'An error occurred while loading the leaderboard. Please try again.'
            );
            await message.reply({ embeds: [embed] });
        }
    },
    
    async getLevelLeaderboard(limit, offset) {
        const characters = await Character.findAll({
            include: [{
                model: User,
                as: 'user',
                attributes: ['username', 'discriminator', 'discord_id']
            }],
            order: [
                ['level', 'DESC'],
                ['experience', 'DESC'],
                ['created_at', 'ASC']
            ],
            limit: limit,
            offset: offset
        });
        
        const totalCount = await Character.count();
        
        return [characters, totalCount];
    },
    
    async getGoldLeaderboard(limit, offset) {
        const characters = await Character.findAll({
            include: [{
                model: User,
                as: 'user',
                attributes: ['username', 'discriminator', 'discord_id']
            }],
            order: [
                ['gold', 'DESC'],
                ['level', 'DESC'],
                ['created_at', 'ASC']
            ],
            limit: limit,
            offset: offset
        });
        
        const totalCount = await Character.count();
        
        return [characters, totalCount];
    },
    
    async getBattleLeaderboard(limit, offset) {
        const battleStats = await BattleLog.findAll({
            attributes: [
                'user_id',
                [require('sequelize').fn('COUNT', '*'), 'total_battles'],
                [require('sequelize').fn('SUM', require('sequelize').literal("CASE WHEN result = 'victory' THEN 1 ELSE 0 END")), 'victories'],
                [require('sequelize').fn('SUM', 'damage_dealt'), 'total_damage']
            ],
            include: [{
                model: User,
                as: 'user',
                attributes: ['username', 'discriminator', 'discord_id'],
                include: [{
                    model: Character,
                    as: 'character',
                    attributes: ['level']
                }]
            }],
            group: ['user_id', 'user.id', 'user.username', 'user.discriminator', 'user.discord_id', 'user.character.id', 'user.character.level'],
            order: [[require('sequelize').literal('victories'), 'DESC']],
            limit: limit,
            offset: offset,
            raw: false
        });
        
        const totalCount = await BattleLog.findAll({
            attributes: ['user_id'],
            group: ['user_id']
        }).then(results => results.length);
        
        return [battleStats, totalCount];
    },
    
    async getVipLeaderboard(limit, offset) {
        const characters = await Character.findAll({
            include: [{
                model: User,
                as: 'user',
                attributes: ['username', 'discriminator', 'discord_id', 'total_spent']
            }],
            where: {
                vip_level: { [require('sequelize').Op.gt]: 0 }
            },
            order: [
                ['vip_level', 'DESC'],
                [{ model: User, as: 'user' }, 'total_spent', 'DESC'],
                ['level', 'DESC']
            ],
            limit: limit,
            offset: offset
        });
        
        const totalCount = await Character.count({
            where: {
                vip_level: { [require('sequelize').Op.gt]: 0 }
            }
        });
        
        return [characters, totalCount];
    },
    
    async getPrestigeLeaderboard(limit, offset) {
        const characters = await Character.findAll({
            include: [{
                model: User,
                as: 'user',
                attributes: ['username', 'discriminator', 'discord_id']
            }],
            where: {
                prestige_level: { [require('sequelize').Op.gt]: 0 }
            },
            order: [
                ['prestige_level', 'DESC'],
                ['level', 'DESC'],
                ['experience', 'DESC']
            ],
            limit: limit,
            offset: offset
        });
        
        const totalCount = await Character.count({
            where: {
                prestige_level: { [require('sequelize').Op.gt]: 0 }
            }
        });
        
        return [characters, totalCount];
    },
    
    createLeaderboardEmbed(players, category, page, totalPages, requesterId) {
        const categoryInfo = {
            level: { name: 'Level', emoji: '🏅', color: '#FFD700' },
            gold: { name: 'Gold', emoji: '💰', color: '#FFA500' },
            battles: { name: 'Battle Victories', emoji: '⚔️', color: '#FF4500' },
            vip: { name: 'VIP Status', emoji: '👑', color: '#9932CC' },
            prestige: { name: 'Prestige', emoji: '✨', color: '#00CED1' }
        };
        
        const info = categoryInfo[category];
        const embed = GameEmbedBuilder.createInfoEmbed(
            `${info.emoji} ${info.name} Leaderboard`,
            players.length === 0 ? 'No players found for this category.' : null
        );
        
        embed.setColor(info.color);
        
        if (players.length > 0) {
            let description = '';
            let userRank = null;
            
            players.forEach((player, index) => {
                const rank = ((page - 1) * 10) + index + 1;
                const medal = rank === 1 ? '🥇' : rank === 2 ? '🥈' : rank === 3 ? '🥉' : `${rank}.`;
                
                let playerName, playerValue;
                
                if (category === 'battles') {
                    // Battle leaderboard has different structure
                    playerName = player.user.username;
                    playerValue = `${player.dataValues.victories} victories (${player.dataValues.total_battles} total)`;
                    
                    if (player.user.discord_id === requesterId) {
                        userRank = rank;
                    }
                } else {
                    // Regular character leaderboard
                    playerName = player.user.username;
                    
                    switch (category) {
                        case 'level':
                            playerValue = `Level ${player.level} (${player.experience.toLocaleString()} XP)`;
                            break;
                        case 'gold':
                            playerValue = `${player.gold.toLocaleString()} 🪙`;
                            break;
                        case 'vip':
                            const vipInfo = character.getVipBenefits();
                            playerValue = `${vipInfo.name} ($${player.user.total_spent})`;
                            break;
                        case 'prestige':
                            playerValue = `Prestige ${player.prestige_level} • Level ${player.level}`;
                            break;
                    }
                    
                    if (player.user.discord_id === requesterId) {
                        userRank = rank;
                    }
                }
                
                // Highlight current user
                if (player.user?.discord_id === requesterId || player.user.discord_id === requesterId) {
                    description += `**${medal} ${playerName}** ⭐\n${playerValue}\n\n`;
                } else {
                    description += `${medal} **${playerName}**\n${playerValue}\n\n`;
                }
            });
            
            embed.setDescription(description);
            
            // Add user's rank if not on current page
            if (userRank) {
                embed.addFields({
                    name: '⭐ Your Rank',
                    value: `You are ranked #${userRank} in ${info.name}!`,
                    inline: false
                });
            }
        }
        
        // Add pagination info
        embed.addFields({
            name: '📄 Page Info',
            value: `Page ${page}/${totalPages || 1}`,
            inline: true
        });
        
        // Add category navigation help
        embed.addFields({
            name: '🔄 Navigation',
            value: '🏅 Level • 💰 Gold • ⚔️ Battles • 👑 VIP • ✨ Prestige',
            inline: false
        });
        
        embed.setFooter({ 
            text: `Use reactions to navigate • Updated every 10 minutes` 
        });
        
        return embed;
    }
};

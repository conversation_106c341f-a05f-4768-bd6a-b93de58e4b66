const { User, Character, Item } = require('../../database/database');
const GameEmbedBuilder = require('../../utils/embedBuilder');
const { ActionRowBuilder, StringSelectMenuBuilder, StringSelectMenuOptionBuilder } = require('discord.js');

module.exports = {
    data: {
        name: 'shop',
        aliases: ['store', 'market'],
        description: 'Browse the item shop and purchase items',
        usage: '!shop [category] [page]',
        cooldown: 3
    },
    async execute(message, args) {
        console.log('🛒 Shop command executed by:', message.author.username, 'with args:', args);

        try {
            // Get user and character
            const user = await User.findByDiscordId(message.author.id);
            const character = await Character.findOne({
                where: { user_id: user.id }
            });
            
            // Parse arguments
            let category = null;
            let page = 1;
            
            if (args.length > 0) {
                const firstArg = args[0].toLowerCase();
                
                // Check if first argument is a page number
                if (!isNaN(firstArg) && parseInt(firstArg) > 0) {
                    page = parseInt(firstArg);
                } else {
                    // First argument is category
                    category = firstArg;
                    if (args.length > 1 && !isNaN(args[1])) {
                        page = parseInt(args[1]);
                    }
                }
            }
            
            // Validate category
            const validCategories = ['weapons', 'armor', 'accessories', 'consumables', 'materials', 'premium', 'vip_exclusive'];
            if (category && !validCategories.includes(category)) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Invalid Category',
                    `Valid categories: ${validCategories.join(', ')}\nOr use \`!shop\` to see all items.`
                );
                return message.reply({ embeds: [embed] });
            }
            
            // Debug: Check category value
            console.log(`Shop command - category: "${category}", args:`, args);

            // If no category specified, show categories
            if (!category) {
                const embed = GameEmbedBuilder.createInfoEmbed(
                    '🏪 Welcome to the Shop!',
                    'Choose a category to browse:'
                );
                
                const categories = [
                    { key: 'weapons', name: 'Weapons', emoji: '⚔️', description: 'Swords, axes, and other weapons' },
                    { key: 'armor', name: 'Armor & Protection', emoji: '🛡️', description: 'Armor, helmets, and boots' },
                    { key: 'accessories', name: 'Accessories', emoji: '💍', description: 'Rings and necklaces' },
                    { key: 'consumables', name: 'Consumables', emoji: '🧪', description: 'Potions and consumable items' },
                    { key: 'materials', name: 'Materials', emoji: '⚒️', description: 'Crafting materials and resources' }
                ];
                let categoryText = '';
                
                for (const cat of categories) {
                    // Get item count for each category
                    const itemCount = await Item.count({
                        where: {
                            type: cat.key === 'weapons' ? 'weapon' :
                                  cat.key === 'armor' ? ['armor', 'helmet', 'boots'] :
                                  cat.key === 'accessories' ? ['ring', 'necklace'] :
                                  cat.key === 'consumables' ? 'consumable' :
                                  cat.key === 'materials' ? 'material' : 'weapon',
                            level_requirement: { [require('sequelize').Op.lte]: character.level }
                        }
                    });
                    
                    categoryText += `${cat.emoji} **${cat.name}** (${itemCount} items)\n`;
                    categoryText += `\`!shop ${cat.key}\`\n\n`;
                }
                
                embed.setDescription(categoryText);
                
                // Add featured items (random high-rarity items)
                const featuredItems = await Item.findAll({
                    where: {
                        rarity: { [require('sequelize').Op.in]: ['epic', 'legendary', 'mythic'] },
                        level_requirement: { [require('sequelize').Op.lte]: character.level }
                    },
                    order: require('sequelize').literal('RANDOM()'), // Works for both PostgreSQL and SQLite
                    limit: 3
                });

                if (featuredItems.length > 0) {
                    let featuredText = '';
                    featuredItems.forEach(item => {
                        const rarityInfo = item.getRarityInfo();
                        const price = item.base_price;

                        featuredText += `${rarityInfo.emoji} **${item.name}** - ${price} 🪙\n`;
                    });

                    embed.addFields({
                        name: '⭐ Featured Items',
                        value: featuredText,
                        inline: false
                    });
                }
                
                embed.addFields({
                    name: '💰 Your Currency',
                    value: `**Gold:** ${character.gold.toLocaleString()} 🪙\n**Gems:** ${character.gems.toLocaleString()} 💎`,
                    inline: true
                });
                
                embed.setFooter({ text: 'Use !buy <item_name> to purchase an item • Menu expires in 3 minutes' });

                // Create category dropdown menu for overview
                console.log('Creating category overview dropdown menu...');

                try {
                    const selectMenu = new StringSelectMenuBuilder()
                        .setCustomId('shop_category_select')
                        .setPlaceholder('🛒 Choose a category to browse...')
                        .addOptions([
                            new StringSelectMenuOptionBuilder()
                                .setLabel('Weapons')
                                .setDescription('Swords, axes, and other weapons')
                                .setValue('weapons')
                                .setEmoji('⚔️'),
                            new StringSelectMenuOptionBuilder()
                                .setLabel('Armor & Protection')
                                .setDescription('Armor, helmets, and boots')
                                .setValue('armor')
                                .setEmoji('🛡️'),
                            new StringSelectMenuOptionBuilder()
                                .setLabel('Accessories')
                                .setDescription('Rings and necklaces')
                                .setValue('accessories')
                                .setEmoji('💍'),
                            new StringSelectMenuOptionBuilder()
                                .setLabel('Consumables')
                                .setDescription('Potions and consumable items')
                                .setValue('consumables')
                                .setEmoji('🧪'),
                            new StringSelectMenuOptionBuilder()
                                .setLabel('Materials')
                                .setDescription('Crafting materials and resources')
                                .setValue('materials')
                                .setEmoji('⚒️')
                        ]);

                    const row = new ActionRowBuilder()
                        .addComponents(selectMenu);

                    console.log('Category overview dropdown menu created successfully');

                    const shopMessage = await message.reply({
                        embeds: [embed],
                        components: [row]
                    });

                    // Create interaction collector for category overview
                    this.createCategoryCollector(shopMessage, message, user, character);
                    return;

                } catch (menuError) {
                    console.error('Error creating category overview dropdown menu:', menuError);
                    return message.reply({ embeds: [embed] });
                }
            }
            
            // Get items for the specified category
            const itemsPerPage = 8;
            const offset = (page - 1) * itemsPerPage;
            
            // Map category to item types
            const categoryToTypes = {
                'weapons': ['weapon'],
                'armor': ['armor', 'helmet', 'boots'],
                'accessories': ['ring', 'necklace'],
                'consumables': ['consumable'],
                'materials': ['material'],
                'premium': ['weapon', 'armor', 'consumable'], // Mix of premium items
                'vip_exclusive': ['weapon', 'armor'] // VIP items
            };

            const itemTypes = categoryToTypes[category] || ['weapon'];

            const shopItems = await Item.findAll({
                where: {
                    type: { [require('sequelize').Op.in]: itemTypes },
                    level_requirement: { [require('sequelize').Op.lte]: character.level }
                },
                order: [['rarity', 'DESC'], ['level_requirement', 'ASC']],
                limit: itemsPerPage,
                offset: offset
            });
            
            // Get total count for pagination
            const totalItems = await Item.count({
                where: {
                    type: { [require('sequelize').Op.in]: itemTypes },
                    level_requirement: { [require('sequelize').Op.lte]: character.level }
                }
            });
            
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            
            // Validate page number
            if (page > totalPages && totalPages > 0) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Invalid Page',
                    `Page ${page} doesn't exist. Maximum page: ${totalPages}`
                );
                return message.reply({ embeds: [embed] });
            }
            
            // Create shop embed
            const categories = [
                { key: 'weapons', name: 'Weapons' },
                { key: 'armor', name: 'Armor & Protection' },
                { key: 'accessories', name: 'Accessories' },
                { key: 'consumables', name: 'Consumables' },
                { key: 'materials', name: 'Materials' }
            ];
            const categoryInfo = categories.find(cat => cat.key === category);
            const embed = GameEmbedBuilder.createShopEmbed(
                shopItems,
                categoryInfo ? categoryInfo.name : category,
                page,
                totalPages
            );

            // Add detailed item information
            if (shopItems.length > 0) {
                let itemsText = '';

                shopItems.forEach((item, index) => {
                    const rarityInfo = item.getRarityInfo();
                    const price = item.base_price;

                    itemsText += `**${index + 1}. ${rarityInfo.emoji} ${item.name}**\n`;
                    itemsText += `*Level ${item.level_requirement} • ${rarityInfo.name} ${item.type}*\n`;
                    itemsText += `**Price:** ${price} 🪙`;

                    // VIP discount
                    const vipInfo = character.getVipBenefits();
                    if (vipInfo.level > 0) {
                        const discountedPrice = Math.floor(price * 0.95); // 5% VIP discount
                        itemsText += ` ~~${price}~~ ${discountedPrice} 🪙 (VIP -5%)`;
                    }

                    itemsText += '\n';

                    // Add item stats preview
                    if (item.attack_bonus > 0 || item.defense_bonus > 0 || item.health_bonus > 0) {
                        let statsText = '';
                        if (item.attack_bonus > 0) statsText += `ATK +${item.attack_bonus} `;
                        if (item.defense_bonus > 0) statsText += `DEF +${item.defense_bonus} `;
                        if (item.health_bonus > 0) statsText += `HP +${item.health_bonus} `;

                        itemsText += `*${statsText.trim()}*\n`;
                    }

                    itemsText += '\n';
                });

                embed.setDescription(itemsText);
            }
            
            // Add user currency info
            embed.addFields({
                name: '💰 Your Currency',
                value: `**Gold:** ${character.gold.toLocaleString()} 🪙\n**Gems:** ${character.gems.toLocaleString()} 💎`,
                inline: true
            });
            
            // Add VIP info if applicable
            if (character.vip_level > 0) {
                const vipInfo = character.getVipBenefits();
                embed.addFields({
                    name: '👑 VIP Benefits',
                    value: `**${vipInfo.name}**\nAccess to VIP exclusive items`,
                    inline: true
                });
            }
            
            // Add navigation help
            let footerText = `Page ${page}/${totalPages || 1} | Use !buy <item_name> to purchase`;
            if (totalPages > 1) {
                footerText += ` | !shop ${category} ${page + 1} for next page`;
            }
            
            embed.setFooter({ text: footerText });
            
            // Create shop menus
            console.log(`Creating shop menus for category: ${category}, page: ${page}, totalPages: ${totalPages}`);

            try {
                const components = [];

                // First row: Quick buy menu (if there are items)
                if (shopItems.length > 0) {
                    const buyMenu = new StringSelectMenuBuilder()
                        .setCustomId('shop_buy_item')
                        .setPlaceholder('💰 Quick Buy - Select an item to purchase...')
                        .addOptions(
                            shopItems.slice(0, 25).map((item, index) => { // Discord limit: 25 options
                                const rarityInfo = item.getRarityInfo();
                                const price = item.base_price;

                                // Calculate VIP discount if applicable
                                let displayPrice = price;
                                let priceText = `${price} 🪙`;

                                try {
                                    const vipInfo = character.getVipBenefits();
                                    if (vipInfo && vipInfo.level > 0) {
                                        displayPrice = Math.floor(price * 0.95); // 5% VIP discount
                                        priceText = `${displayPrice} 🪙 (VIP -5%)`;
                                    }
                                } catch (vipError) {
                                    console.log('VIP info not available, using regular price');
                                }

                                return new StringSelectMenuOptionBuilder()
                                    .setLabel(`${item.name}`)
                                    .setDescription(`${priceText} | Lv.${item.level_requirement} ${rarityInfo.name}`)
                                    .setValue(`buy_${item.id}`)
                                    .setEmoji(rarityInfo.emoji);
                            })
                        );

                    const buyRow = new ActionRowBuilder().addComponents(buyMenu);
                    components.push(buyRow);
                }

                // Second row: Category navigation dropdown menu
                const categoryMenu = new StringSelectMenuBuilder()
                    .setCustomId('shop_category_select')
                    .setPlaceholder(`🛒 Currently viewing: ${category.charAt(0).toUpperCase() + category.slice(1)} | Switch category...`)
                    .addOptions([
                        new StringSelectMenuOptionBuilder()
                            .setLabel('Weapons')
                            .setDescription('Swords, axes, and other weapons')
                            .setValue('weapons')
                            .setEmoji('⚔️')
                            .setDefault(category === 'weapons'),
                        new StringSelectMenuOptionBuilder()
                            .setLabel('Armor & Protection')
                            .setDescription('Armor, helmets, and boots')
                            .setValue('armor')
                            .setEmoji('🛡️')
                            .setDefault(category === 'armor'),
                        new StringSelectMenuOptionBuilder()
                            .setLabel('Accessories')
                            .setDescription('Rings and necklaces')
                            .setValue('accessories')
                            .setEmoji('💍')
                            .setDefault(category === 'accessories'),
                        new StringSelectMenuOptionBuilder()
                            .setLabel('Consumables')
                            .setDescription('Potions and consumable items')
                            .setValue('consumables')
                            .setEmoji('🧪')
                            .setDefault(category === 'consumables'),
                        new StringSelectMenuOptionBuilder()
                            .setLabel('Materials')
                            .setDescription('Crafting materials and resources')
                            .setValue('materials')
                            .setEmoji('⚒️')
                            .setDefault(category === 'materials')
                    ]);

                const categoryRow = new ActionRowBuilder().addComponents(categoryMenu);
                components.push(categoryRow);

                console.log(`Created ${components.length} menu rows`);

                const shopMessage = await message.reply({
                    embeds: [embed],
                    components: components
                });

                console.log('Shop message sent with menus');

                // Create interaction collectors
                this.createShopCollectors(shopMessage, message, user, character, category, page);

            } catch (menuError) {
                console.error('Error creating dropdown menu:', menuError);

                // Fallback: send without menu
                const shopMessage = await message.reply({
                    embeds: [embed]
                });
                console.log('Shop message sent without dropdown menu (fallback)');
            }

        } catch (error) {
            console.error('Error in shop command:', error);
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Shop Error',
                'An error occurred while loading the shop. Please try again.'
            );
            await message.reply({ embeds: [embed] });
        }
    },

    createShopNavigation(currentCategory, currentPage, totalPages) {
        console.log(`createShopNavigation called with: ${currentCategory}, ${currentPage}, ${totalPages}`);

        const categories = [
            { key: 'weapons', name: 'Weapons', emoji: '⚔️' },
            { key: 'armor', name: 'Armor', emoji: '🛡️' },
            { key: 'accessories', name: 'Accessories', emoji: '💍' },
            { key: 'consumables', name: 'Consumables', emoji: '🧪' },
            { key: 'materials', name: 'Materials', emoji: '⚒️' }
        ];

        const components = [];
        console.log('Creating category buttons...');

        // Category buttons (first row)
        const categoryRow = new ActionRowBuilder();
        categories.forEach(cat => {
            const button = new ButtonBuilder()
                .setCustomId(`shop_category_${cat.key}`)
                .setLabel(cat.name)
                .setEmoji(cat.emoji)
                .setStyle(currentCategory === cat.key ? ButtonStyle.Primary : ButtonStyle.Secondary);

            categoryRow.addComponents(button);
        });
        components.push(categoryRow);

        // Pagination buttons (second row) - only if there are multiple pages
        if (totalPages > 1) {
            const paginationRow = new ActionRowBuilder();

            // Previous page button
            const prevButton = new ButtonBuilder()
                .setCustomId(`shop_page_${currentCategory}_${Math.max(1, currentPage - 1)}`)
                .setLabel('◀️ Previous')
                .setStyle(ButtonStyle.Secondary)
                .setDisabled(currentPage <= 1);

            // Current page indicator
            const pageButton = new ButtonBuilder()
                .setCustomId('shop_page_info')
                .setLabel(`Page ${currentPage}/${totalPages}`)
                .setStyle(ButtonStyle.Secondary)
                .setDisabled(true);

            // Next page button
            const nextButton = new ButtonBuilder()
                .setCustomId(`shop_page_${currentCategory}_${Math.min(totalPages, currentPage + 1)}`)
                .setLabel('Next ▶️')
                .setStyle(ButtonStyle.Secondary)
                .setDisabled(currentPage >= totalPages);

            paginationRow.addComponents(prevButton, pageButton, nextButton);
            components.push(paginationRow);
        }

        // Utility buttons (third row)
        const utilityRow = new ActionRowBuilder();

        const refreshButton = new ButtonBuilder()
            .setCustomId(`shop_refresh_${currentCategory}_${currentPage}`)
            .setLabel('🔄 Refresh')
            .setStyle(ButtonStyle.Secondary);

        const helpButton = new ButtonBuilder()
            .setCustomId('shop_help')
            .setLabel('❓ Help')
            .setStyle(ButtonStyle.Secondary);

        utilityRow.addComponents(refreshButton, helpButton);
        components.push(utilityRow);

        console.log(`Returning ${components.length} component rows`);
        return components;
    },

    async generateShopEmbed(message, args) {
        // This is a simplified version of the main execute logic
        // to generate embed data for button interactions
        const user = await User.findByDiscordId(message.author.id);
        const character = await Character.findOne({
            where: { user_id: user.id }
        });

        let category = 'all';
        let page = 1;

        if (args.length > 0) {
            category = args[0].toLowerCase();
            if (args.length > 1) {
                page = parseInt(args[1]) || 1;
            }
        }

        const itemsPerPage = 5;
        const offset = (page - 1) * itemsPerPage;

        if (category === 'all') {
            // Show categories overview
            const categories = [
                { key: 'weapons', name: 'Weapons', emoji: '⚔️', description: 'Swords, axes, and other weapons' },
                { key: 'armor', name: 'Armor & Protection', emoji: '🛡️', description: 'Armor, helmets, and boots' },
                { key: 'accessories', name: 'Accessories', emoji: '💍', description: 'Rings and necklaces' },
                { key: 'consumables', name: 'Consumables', emoji: '🧪', description: 'Potions and consumable items' },
                { key: 'materials', name: 'Materials', emoji: '⚒️', description: 'Crafting materials and resources' }
            ];

            const embed = GameEmbedBuilder.createShopEmbed([], 'Shop Categories', 1, 1);

            let description = '**Welcome to the Shop!** 🏪\n\n';
            description += 'Choose a category to browse items:\n\n';

            for (const cat of categories) {
                const itemCount = await Item.count({
                    where: {
                        type: cat.key === 'weapons' ? 'weapon' :
                              cat.key === 'armor' ? ['armor', 'helmet', 'boots'] :
                              cat.key === 'accessories' ? ['ring', 'necklace'] :
                              cat.key === 'consumables' ? 'consumable' :
                              cat.key === 'materials' ? 'material' : 'weapon',
                        level_requirement: { [require('sequelize').Op.lte]: character.level }
                    }
                });

                description += `${cat.emoji} **${cat.name}** (${itemCount} items)\n`;
                description += `*${cat.description}*\n\n`;
            }

            embed.setDescription(description);
            embed.addFields({
                name: '💰 Your Gold',
                value: `${character.gold.toLocaleString()} 🪙`,
                inline: true
            });

            return { embed, totalPages: 1 };
        } else {
            // Show specific category items (simplified version)
            const categoryToTypes = {
                'weapons': ['weapon'],
                'armor': ['armor', 'helmet', 'boots'],
                'accessories': ['ring', 'necklace'],
                'consumables': ['consumable'],
                'materials': ['material']
            };

            const itemTypes = categoryToTypes[category] || ['weapon'];

            const shopItems = await Item.findAll({
                where: {
                    type: { [require('sequelize').Op.in]: itemTypes },
                    level_requirement: { [require('sequelize').Op.lte]: character.level }
                },
                order: [['rarity', 'DESC'], ['level_requirement', 'ASC']],
                limit: itemsPerPage,
                offset: offset
            });

            const totalItems = await Item.count({
                where: {
                    type: { [require('sequelize').Op.in]: itemTypes },
                    level_requirement: { [require('sequelize').Op.lte]: character.level }
                }
            });

            const totalPages = Math.ceil(totalItems / itemsPerPage);
            const categories = [
                { key: 'weapons', name: 'Weapons' },
                { key: 'armor', name: 'Armor & Protection' },
                { key: 'accessories', name: 'Accessories' },
                { key: 'consumables', name: 'Consumables' },
                { key: 'materials', name: 'Materials' }
            ];
            const categoryInfo = categories.find(cat => cat.key === category);
            const embed = GameEmbedBuilder.createShopEmbed(
                shopItems,
                categoryInfo ? categoryInfo.name : category,
                page,
                totalPages
            );

            return { embed, totalPages };
        }
    },

    // Helper function to generate category embed
    async generateCategoryEmbed(user, character, category, page = 1) {
        const itemsPerPage = 8;
        const offset = (page - 1) * itemsPerPage;

        // Map category to item types
        const categoryToTypes = {
            'weapons': ['weapon'],
            'armor': ['armor', 'helmet', 'boots'],
            'accessories': ['ring', 'necklace'],
            'consumables': ['consumable'],
            'materials': ['material']
        };

        const itemTypes = categoryToTypes[category] || ['weapon'];

        // Get items for the category
        const items = await Item.findAll({
            where: {
                type: { [require('sequelize').Op.in]: itemTypes },
                level_requirement: { [require('sequelize').Op.lte]: character.level }
            },
            order: [['rarity', 'DESC'], ['base_price', 'ASC']],
            limit: itemsPerPage,
            offset: offset
        });

        // Get total count for pagination
        const totalItems = await Item.count({
            where: {
                type: { [require('sequelize').Op.in]: itemTypes },
                level_requirement: { [require('sequelize').Op.lte]: character.level }
            }
        });

        const totalPages = Math.ceil(totalItems / itemsPerPage);

        // Create embed
        const categoryNames = {
            'weapons': '⚔️ Weapons',
            'armor': '🛡️ Armor & Protection',
            'accessories': '💍 Accessories',
            'consumables': '🧪 Consumables',
            'materials': '⚒️ Materials'
        };

        const embed = GameEmbedBuilder.createInfoEmbed(
            `🏪 ${categoryNames[category] || 'Shop'}`,
            `Browse ${category} available for your level:`
        );

        if (items.length === 0) {
            embed.setDescription(`No ${category} available for your level yet.`);
        } else {
            let itemsText = '';
            items.forEach(item => {
                const rarityInfo = item.getRarityInfo();
                const price = item.base_price;
                const gemPrice = item.gem_price;

                itemsText += `${rarityInfo.emoji} **${item.name}**\n`;
                itemsText += `${item.description}\n`;
                itemsText += `💰 ${price.toLocaleString()} 🪙`;
                if (gemPrice > 0) {
                    itemsText += ` | ${gemPrice} 💎`;
                }
                itemsText += `\n\`!buy ${item.name}\`\n\n`;
            });

            embed.setDescription(itemsText);
        }

        // Add currency info
        embed.addFields({
            name: '💰 Your Currency',
            value: `**Gold:** ${character.gold.toLocaleString()} 🪙\n**Gems:** ${character.gems.toLocaleString()} 💎`,
            inline: true
        });

        // Add footer
        let footerText = `Page ${page}/${totalPages || 1} | Use !buy <item_name> to purchase`;
        if (totalPages > 1) {
            footerText += ` | !shop ${category} ${page + 1} for next page`;
        }
        footerText += ' • Menu expires in 3 minutes';
        embed.setFooter({ text: footerText });

        return embed;
    },

    // Helper function to create category select menu
    createCategorySelectMenu(currentCategory = null) {
        const selectMenu = new StringSelectMenuBuilder()
            .setCustomId('shop_category_select')
            .setPlaceholder(currentCategory ?
                `🛒 Currently viewing: ${currentCategory.charAt(0).toUpperCase() + currentCategory.slice(1)} | Switch category...` :
                '🛒 Choose a category to browse...'
            )
            .addOptions([
                new StringSelectMenuOptionBuilder()
                    .setLabel('Weapons')
                    .setDescription('Swords, axes, and other weapons')
                    .setValue('weapons')
                    .setEmoji('⚔️')
                    .setDefault(currentCategory === 'weapons'),
                new StringSelectMenuOptionBuilder()
                    .setLabel('Armor & Protection')
                    .setDescription('Armor, helmets, and boots')
                    .setValue('armor')
                    .setEmoji('🛡️')
                    .setDefault(currentCategory === 'armor'),
                new StringSelectMenuOptionBuilder()
                    .setLabel('Accessories')
                    .setDescription('Rings and necklaces')
                    .setValue('accessories')
                    .setEmoji('💍')
                    .setDefault(currentCategory === 'accessories'),
                new StringSelectMenuOptionBuilder()
                    .setLabel('Consumables')
                    .setDescription('Potions and consumable items')
                    .setValue('consumables')
                    .setEmoji('🧪')
                    .setDefault(currentCategory === 'consumables'),
                new StringSelectMenuOptionBuilder()
                    .setLabel('Materials')
                    .setDescription('Crafting materials and resources')
                    .setValue('materials')
                    .setEmoji('⚒️')
                    .setDefault(currentCategory === 'materials')
            ]);

        return new ActionRowBuilder().addComponents(selectMenu);
    },

    // Helper function to create shop collectors (both buy and category)
    createShopCollectors(shopMessage, originalMessage, user, character, currentCategory, currentPage) {
        console.log('Creating shop collectors...');

        const filter = (interaction) => {
            return interaction.user.id === originalMessage.author.id &&
                   (interaction.customId === 'shop_category_select' || interaction.customId === 'shop_buy_item');
        };

        const collector = shopMessage.createMessageComponentCollector({
            filter,
            time: 180000 // 3 minutes
        });

        collector.on('collect', async (interaction) => {
            try {
                console.log(`Shop interaction received: ${interaction.customId}`);
                await interaction.deferUpdate();

                if (interaction.customId === 'shop_category_select') {
                    // Handle category selection
                    const selectedCategory = interaction.values[0];
                    console.log(`User selected category: ${selectedCategory}`);

                    // Generate new embed for selected category
                    const newEmbed = await this.generateCategoryEmbed(user, character, selectedCategory, 1);
                    const newComponents = await this.createShopComponents(user, character, selectedCategory, 1);

                    // Update the message with new embed and menus
                    await interaction.editReply({
                        embeds: [newEmbed],
                        components: newComponents
                    });

                    console.log(`Successfully updated to category: ${selectedCategory}`);

                } else if (interaction.customId === 'shop_buy_item') {
                    // Handle item purchase
                    const itemValue = interaction.values[0];
                    const itemId = itemValue.replace('buy_', '');
                    console.log(`User wants to buy item ID: ${itemId}`);

                    // Process the purchase
                    await this.processPurchase(interaction, user, character, itemId, currentCategory, currentPage);
                }

            } catch (error) {
                console.error('Error in shop interaction:', error);
                if (!interaction.replied && !interaction.deferred) {
                    await interaction.reply({
                        content: 'An error occurred while processing your request.',
                        ephemeral: true
                    });
                }
            }
        });

        collector.on('end', async (collected, reason) => {
            console.log(`Shop collector ended. Reason: ${reason}`);

            try {
                // Remove the menu components, keep only the embed
                await shopMessage.edit({
                    components: []
                });
                console.log('Shop menus removed after timeout');
            } catch (error) {
                console.error('Error removing shop menus:', error);
            }
        });
    },

    // Helper function to create category collector (for overview page)
    createCategoryCollector(shopMessage, originalMessage, user, character) {
        console.log('Creating category collector...');

        const filter = (interaction) => {
            return interaction.user.id === originalMessage.author.id && interaction.customId === 'shop_category_select';
        };

        const collector = shopMessage.createMessageComponentCollector({
            filter,
            time: 180000 // 3 minutes
        });

        collector.on('collect', async (interaction) => {
            try {
                console.log('Dropdown interaction received');
                await interaction.deferUpdate();

                // Get selected category from dropdown
                const selectedCategory = interaction.values[0];
                console.log(`User selected category: ${selectedCategory}`);

                // Generate new embed for selected category
                const newEmbed = await this.generateCategoryEmbed(user, character, selectedCategory, 1);
                const newComponents = await this.createShopComponents(user, character, selectedCategory, 1);

                // Update the message with new embed and menus
                await interaction.editReply({
                    embeds: [newEmbed],
                    components: newComponents
                });

                console.log(`Successfully updated to category: ${selectedCategory}`);

            } catch (error) {
                console.error('Error in shop dropdown interaction:', error);
                if (!interaction.replied && !interaction.deferred) {
                    await interaction.reply({
                        content: 'An error occurred while processing your request.',
                        ephemeral: true
                    });
                }
            }
        });

        collector.on('end', async (collected, reason) => {
            console.log(`Shop dropdown collector ended. Reason: ${reason}`);

            try {
                // Remove the dropdown menu components, keep only the embed
                await shopMessage.edit({
                    components: []
                });
                console.log('Dropdown menu removed after timeout');
            } catch (error) {
                console.error('Error removing dropdown menu:', error);
            }
        });
    },

    // Helper function to create shop components (buy menu + category menu)
    async createShopComponents(user, character, category, page = 1) {
        const components = [];

        // Get items for the buy menu
        const itemsPerPage = 8;
        const offset = (page - 1) * itemsPerPage;

        const categoryToTypes = {
            'weapons': ['weapon'],
            'armor': ['armor', 'helmet', 'boots'],
            'accessories': ['ring', 'necklace'],
            'consumables': ['consumable'],
            'materials': ['material']
        };

        const itemTypes = categoryToTypes[category] || ['weapon'];

        const shopItems = await Item.findAll({
            where: {
                type: { [require('sequelize').Op.in]: itemTypes },
                level_requirement: { [require('sequelize').Op.lte]: character.level }
            },
            order: [['rarity', 'DESC'], ['base_price', 'ASC']],
            limit: itemsPerPage,
            offset: offset
        });

        // First row: Quick buy menu (if there are items)
        if (shopItems.length > 0) {
            const buyMenu = new StringSelectMenuBuilder()
                .setCustomId('shop_buy_item')
                .setPlaceholder('💰 Quick Buy - Select an item to purchase...')
                .addOptions(
                    shopItems.slice(0, 25).map((item) => { // Discord limit: 25 options
                        const rarityInfo = item.getRarityInfo();
                        const price = item.base_price;

                        // Calculate VIP discount if applicable
                        let displayPrice = price;
                        let priceText = `${price} 🪙`;

                        try {
                            const vipInfo = character.getVipBenefits();
                            if (vipInfo && vipInfo.level > 0) {
                                displayPrice = Math.floor(price * 0.95); // 5% VIP discount
                                priceText = `${displayPrice} 🪙 (VIP -5%)`;
                            }
                        } catch (vipError) {
                            console.log('VIP info not available, using regular price');
                        }

                        return new StringSelectMenuOptionBuilder()
                            .setLabel(`${item.name}`)
                            .setDescription(`${priceText} | Lv.${item.level_requirement} ${rarityInfo.name}`)
                            .setValue(`buy_${item.id}`)
                            .setEmoji(rarityInfo.emoji);
                    })
                );

            const buyRow = new ActionRowBuilder().addComponents(buyMenu);
            components.push(buyRow);
        }

        // Second row: Category navigation menu
        const categoryMenu = new StringSelectMenuBuilder()
            .setCustomId('shop_category_select')
            .setPlaceholder(`🛒 Currently viewing: ${category.charAt(0).toUpperCase() + category.slice(1)} | Switch category...`)
            .addOptions([
                new StringSelectMenuOptionBuilder()
                    .setLabel('Weapons')
                    .setDescription('Swords, axes, and other weapons')
                    .setValue('weapons')
                    .setEmoji('⚔️')
                    .setDefault(category === 'weapons'),
                new StringSelectMenuOptionBuilder()
                    .setLabel('Armor & Protection')
                    .setDescription('Armor, helmets, and boots')
                    .setValue('armor')
                    .setEmoji('🛡️')
                    .setDefault(category === 'armor'),
                new StringSelectMenuOptionBuilder()
                    .setLabel('Accessories')
                    .setDescription('Rings and necklaces')
                    .setValue('accessories')
                    .setEmoji('💍')
                    .setDefault(category === 'accessories'),
                new StringSelectMenuOptionBuilder()
                    .setLabel('Consumables')
                    .setDescription('Potions and consumable items')
                    .setValue('consumables')
                    .setEmoji('🧪')
                    .setDefault(category === 'consumables'),
                new StringSelectMenuOptionBuilder()
                    .setLabel('Materials')
                    .setDescription('Crafting materials and resources')
                    .setValue('materials')
                    .setEmoji('⚒️')
                    .setDefault(category === 'materials')
            ]);

        const categoryRow = new ActionRowBuilder().addComponents(categoryMenu);
        components.push(categoryRow);

        return components;
    },

    // Helper function to process item purchase
    async processPurchase(interaction, user, character, itemId, currentCategory, currentPage) {
        try {
            console.log(`Processing purchase for item ID: ${itemId}`);

            // Get the item
            const item = await Item.findByPk(itemId);
            if (!item) {
                await interaction.followUp({
                    content: '❌ Item not found!',
                    ephemeral: true
                });
                return;
            }

            // Process the purchase using the existing buy command logic
            const buyCommand = require('./buy');

            // Create a mock message object for the buy command
            const mockMessage = {
                author: interaction.user,
                reply: async (options) => {
                    return await interaction.followUp({
                        ...options,
                        ephemeral: false
                    });
                }
            };

            // Execute the buy command with the item name
            await buyCommand.execute(mockMessage, [item.name]);

            // Refresh the shop display after purchase
            setTimeout(async () => {
                try {
                    // Refresh character data
                    await character.reload();

                    // Generate updated embed and components
                    const newEmbed = await this.generateCategoryEmbed(user, character, currentCategory, currentPage);
                    const newComponents = await this.createShopComponents(user, character, currentCategory, currentPage);

                    // Update the original shop message
                    await interaction.editReply({
                        embeds: [newEmbed],
                        components: newComponents
                    });
                } catch (refreshError) {
                    console.error('Error refreshing shop after purchase:', refreshError);
                }
            }, 1500); // Small delay to ensure purchase is processed

        } catch (error) {
            console.error('Error processing purchase:', error);
            await interaction.followUp({
                content: '❌ An error occurred while processing your purchase. Please try again.',
                ephemeral: true
            });
        }
    }
};

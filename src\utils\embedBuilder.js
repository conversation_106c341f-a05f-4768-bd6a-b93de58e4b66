const { EmbedBuilder } = require('discord.js');

// Default embed color - can be changed via admin command
const DEFAULT_COLOR = 0x8D689E; // Purple color #8D689E

class GameEmbedBuilder {
    static createBaseEmbed(title, description = null, color = DEFAULT_COLOR) {
        const embed = new EmbedBuilder()
            .setTitle(title)
            .setColor(color)
            .setTimestamp()
            .setFooter({ text: 'AFK Game Bot' });

        if (description) {
            embed.setDescription(description);
        }

        return embed;
    }

    static createSuccessEmbed(title, description = null) {
        return this.createBaseEmbed(title, description, 0x00FF00); // Keep green for success
    }

    static createErrorEmbed(title, description = null) {
        return this.createBaseEmbed(title, description, 0xFF0000); // Keep red for errors
    }

    static createWarningEmbed(title, description = null) {
        return this.createBaseEmbed(title, description, 0xFFFF00); // Keep yellow for warnings
    }

    static createInfoEmbed(title, description = null) {
        return this.createBaseEmbed(title, description, DEFAULT_COLOR); // Use default color
    }

    static createProfileEmbed(user, character) {
        const embed = this.createBaseEmbed(`${user.getDisplayName()}'s Profile`, null, DEFAULT_COLOR);
        
        // Calculate total stats with equipment
        const totalStats = character.getTotalStats();
        
        // Progress bar for XP
        const requiredXP = character.getRequiredXP();
        const xpProgress = Math.floor((character.experience / requiredXP) * 20);
        const xpBar = '█'.repeat(xpProgress) + '░'.repeat(20 - xpProgress);
        
        // Health bar
        const healthProgress = Math.floor((character.health / character.max_health) * 20);
        const healthBar = '█'.repeat(healthProgress) + '░'.repeat(20 - healthProgress);

        embed.addFields(
            {
                name: '📊 Basic Info',
                value: `**Level:** ${character.level}\n**VIP:** ${character.vip_level}\n**Prestige:** ${character.prestige_level}`,
                inline: true
            },
            {
                name: '💰 Currency',
                value: `**Gold:** ${character.gold.toLocaleString()} 🪙\n**Gems:** ${character.gems.toLocaleString()} 💎`,
                inline: true
            },
            {
                name: '⚔️ Combat Stats',
                value: `**Attack:** ${totalStats.attack}\n**Defense:** ${totalStats.defense}\n**Crit Rate:** ${totalStats.crit_rate}%`,
                inline: true
            },
            {
                name: '❤️ Health',
                value: `${character.health}/${character.max_health}\n${healthBar}`,
                inline: false
            },
            {
                name: '✨ Experience',
                value: `${character.experience}/${requiredXP} XP\n${xpBar}`,
                inline: false
            }
        );

        // Add VIP info if applicable
        if (character.vip_level > 0) {
            const vipInfo = require('../database/models/VipLevel').getBenefits(character.vip_level);
            embed.addFields({
                name: '👑 VIP Benefits',
                value: `**${vipInfo.name}**\n-${Math.round(vipInfo.cooldown_reduction * 100)}% Cooldown\n+${Math.round(vipInfo.gold_bonus * 100)}% Gold`,
                inline: true
            });
        }

        return embed;
    }

    static createInventoryEmbed(user, items, page = 1, totalPages = 1) {
        const embed = this.createBaseEmbed(`${user.getDisplayName()}'s Inventory (Page ${page}/${totalPages})`, null, DEFAULT_COLOR);
        
        if (items.length === 0) {
            embed.setDescription('Your inventory is empty! Go fight some monsters to get items.');
            return embed;
        }

        let description = '';
        items.forEach((userItem, index) => {
            const item = userItem.item;
            const rarityInfo = item.getRarityInfo();
            const equipped = userItem.is_equipped ? ' ✅' : '';
            const upgrade = userItem.upgrade_level > 0 ? ` +${userItem.upgrade_level}` : '';
            
            description += `${rarityInfo.emoji} **${item.name}**${upgrade}${equipped}\n`;
            description += `*${item.type} • ${rarityInfo.name}*\n`;
            
            if (userItem.quantity > 1) {
                description += `Quantity: ${userItem.quantity}\n`;
            }
            
            description += '\n';
        });

        embed.setDescription(description);
        return embed;
    }

    static createCombatResultEmbed(result) {
        const isVictory = result.result === 'victory';
        const embed = isVictory ? 
            this.createSuccessEmbed('⚔️ Combat Victory!') : 
            this.createErrorEmbed('💀 Combat Defeat!');

        const monster = result.monster;
        embed.addFields(
            {
                name: '👹 Enemy',
                value: `${monster.getFormattedName()}\nLevel ${monster.level}`,
                inline: true
            },
            {
                name: '📊 Battle Stats',
                value: `**Turns:** ${result.turns}\n**Damage Dealt:** ${result.damageDealt}\n**Damage Received:** ${result.damageReceived}`,
                inline: true
            }
        );

        if (isVictory && result.rewards) {
            let rewardsText = '';
            if (result.rewards.exp > 0) rewardsText += `**EXP:** +${result.rewards.exp}\n`;
            if (result.rewards.gold > 0) rewardsText += `**Gold:** +${result.rewards.gold} 🪙\n`;
            if (result.rewards.gems > 0) rewardsText += `**Gems:** +${result.rewards.gems} 💎\n`;
            
            if (result.drops && result.drops.length > 0) {
                rewardsText += `**Items:** ${result.drops.length} dropped\n`;
            }

            if (rewardsText) {
                embed.addFields({
                    name: '🎁 Rewards',
                    value: rewardsText,
                    inline: false
                });
            }
        }

        return embed;
    }

    static createShopEmbed(items, category = 'All Items', page = 1, totalPages = 1) {
        const embed = this.createBaseEmbed(`🏪 Shop - ${category} (Page ${page}/${totalPages})`, null, DEFAULT_COLOR);
        
        if (items.length === 0) {
            embed.setDescription('No items available in this category.');
            return embed;
        }

        let description = '';
        items.forEach((item, index) => {
            const rarityInfo = item.getRarityInfo();
            const price = `${item.base_price} 🪙`;

            description += `${rarityInfo.emoji} **${item.name}**\n`;
            description += `*Level ${item.level_requirement} • ${rarityInfo.name} ${item.type}*\n`;
            description += `**Price:** ${price}\n`;

            // Add item stats if available
            if (item.attack_bonus > 0 || item.defense_bonus > 0 || item.health_bonus > 0) {
                let statsText = '';
                if (item.attack_bonus > 0) statsText += `ATK +${item.attack_bonus} `;
                if (item.defense_bonus > 0) statsText += `DEF +${item.defense_bonus} `;
                if (item.health_bonus > 0) statsText += `HP +${item.health_bonus} `;
                description += `*${statsText.trim()}*\n`;
            }

            description += '\n';
        });

        embed.setDescription(description);
        return embed;
    }

    static createCooldownEmbed(action, remainingTime) {
        const embed = this.createWarningEmbed('⏰ Cooldown Active');
        
        const minutes = Math.floor(remainingTime / 60000);
        const seconds = Math.floor((remainingTime % 60000) / 1000);
        
        let timeText = '';
        if (minutes > 0) {
            timeText = `${minutes}m ${seconds}s`;
        } else {
            timeText = `${seconds}s`;
        }

        embed.setDescription(`You must wait **${timeText}** before using **${action}** again.`);
        return embed;
    }

    static createLeaderboardEmbed(players, type = 'Level', page = 1, totalPages = 1) {
        const embed = this.createBaseEmbed(`🏆 Leaderboard - ${type} (Page ${page}/${totalPages})`, null, DEFAULT_COLOR);
        
        if (players.length === 0) {
            embed.setDescription('No players found.');
            return embed;
        }

        let description = '';
        players.forEach((player, index) => {
            const rank = ((page - 1) * 10) + index + 1;
            const medal = rank === 1 ? '🥇' : rank === 2 ? '🥈' : rank === 3 ? '🥉' : `${rank}.`;
            
            description += `${medal} **${player.user.getDisplayName()}**\n`;
            
            switch (type.toLowerCase()) {
                case 'level':
                    description += `Level ${player.level} (${player.experience.toLocaleString()} XP)\n`;
                    break;
                case 'gold':
                    description += `${player.gold.toLocaleString()} 🪙\n`;
                    break;
                case 'battles':
                    description += `${player.battles_won} victories\n`;
                    break;
            }
            
            description += '\n';
        });

        embed.setDescription(description);
        return embed;
    }
}

module.exports = GameEmbedBuilder;

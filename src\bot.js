const { Client, GatewayIntentBits, Collection, REST, Routes } = require('discord.js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Import database
const { sequelize } = require('./database/database');
const DatabaseInitializer = require('./database/init');

// Import utilities
const { User, Character } = require('./database/database');
const { commandRateLimiter, performanceMonitor } = require('./utils/performance');

class AFKGameBot {
    constructor() {
        this.client = new Client({
            intents: [
                GatewayIntentBits.Guilds,
                GatewayIntentBits.GuildMessages,
                GatewayIntentBits.MessageContent,
                GatewayIntentBits.GuildMembers,
                GatewayIntentBits.GuildMessageReactions
            ]
        });

        this.commands = new Collection();
        this.slashCommands = new Collection();
        this.cooldowns = new Collection();
        this.prefix = process.env.PREFIX || '!';
        
        this.setupEventHandlers();
    }

    async initialize() {
        try {
            console.log('🚀 Starting AFK Game Bot...');
            
            // Initialize database
            const dbInit = new DatabaseInitializer(sequelize);
            await dbInit.initialize();
            
            // Load commands
            await this.loadCommands();
            await this.loadSlashCommands();
            
            // Register slash commands
            await this.registerSlashCommands();
            
            // Login to Discord
            await this.client.login(process.env.DISCORD_TOKEN);
            
        } catch (error) {
            console.error('❌ Failed to initialize bot:', error);
            process.exit(1);
        }
    }

    setupEventHandlers() {
        this.client.once('ready', () => {
            console.log(`✅ Bot is ready! Logged in as ${this.client.user.tag}`);
            console.log(`📊 Serving ${this.client.guilds.cache.size} servers`);
            
            // Set bot activity
            this.client.user.setActivity('AFK Game | ihelp', { type: 'PLAYING' });
        });

        this.client.on('messageCreate', async (message) => {
            await this.handleMessage(message);
        });

        this.client.on('interactionCreate', async (interaction) => {
            await this.handleInteraction(interaction);
        });

        this.client.on('guildMemberAdd', async (member) => {
            await this.handleNewMember(member);
        });

        this.client.on('error', (error) => {
            console.error('Discord client error:', error);
        });

        this.client.on('warn', (warning) => {
            console.warn('Discord client warning:', warning);
        });
    }

    async loadCommands() {
        console.log('📂 Loading prefix commands...');
        
        const commandsPath = path.join(__dirname, 'commands');
        const commandFolders = fs.readdirSync(commandsPath);

        for (const folder of commandFolders) {
            const folderPath = path.join(commandsPath, folder);
            if (!fs.statSync(folderPath).isDirectory()) continue;

            const commandFiles = fs.readdirSync(folderPath).filter(file => file.endsWith('.js'));

            for (const file of commandFiles) {
                const filePath = path.join(folderPath, file);
                try {
                    const command = require(filePath);
                    
                    if (command.data && command.data.name && command.execute) {
                        this.commands.set(command.data.name, command);
                        
                        // Also register aliases
                        if (command.data.aliases) {
                            command.data.aliases.forEach(alias => {
                                this.commands.set(alias, command);
                            });
                        }
                        
                        console.log(`  ✅ Loaded command: ${command.data.name}`);
                    } else {
                        console.warn(`  ⚠️ Invalid command file: ${file}`);
                    }
                } catch (error) {
                    console.error(`  ❌ Error loading command ${file}:`, error);
                }
            }
        }

        console.log(`📂 Loaded ${this.commands.size} prefix commands`);
    }

    async loadSlashCommands() {
        console.log('📂 Loading slash commands...');
        
        const adminPath = path.join(__dirname, 'commands', 'admin');
        if (fs.existsSync(adminPath)) {
            const adminFiles = fs.readdirSync(adminPath).filter(file => file.endsWith('.js'));

            for (const file of adminFiles) {
                const filePath = path.join(adminPath, file);
                try {
                    const command = require(filePath);
                    
                    if (command.data && command.data.name && command.execute) {
                        this.slashCommands.set(command.data.name, command);
                        console.log(`  ✅ Loaded slash command: ${command.data.name}`);
                    }
                } catch (error) {
                    console.error(`  ❌ Error loading slash command ${file}:`, error);
                }
            }
        }

        console.log(`📂 Loaded ${this.slashCommands.size} slash commands`);
    }

    async registerSlashCommands() {
        if (this.slashCommands.size === 0) return;

        console.log('🔄 Registering slash commands...');
        
        try {
            const rest = new REST({ version: '10' }).setToken(process.env.DISCORD_TOKEN);
            
            const commands = Array.from(this.slashCommands.values()).map(command => command.data.toJSON());
            
            await rest.put(
                Routes.applicationCommands(process.env.CLIENT_ID),
                { body: commands }
            );
            
            console.log(`✅ Successfully registered ${commands.length} slash commands`);
        } catch (error) {
            console.error('❌ Error registering slash commands:', error);
        }
    }

    async handleMessage(message) {
        // Ignore bots and messages without prefix
        if (message.author.bot || !message.content.startsWith(this.prefix)) return;

        // Parse command and arguments
        const args = message.content.slice(this.prefix.length).trim().split(/ +/);
        const commandName = args.shift().toLowerCase();

        const command = this.commands.get(commandName);
        if (!command) return;

        try {
            // Check rate limiting
            if (!commandRateLimiter.isAllowed(message.author.id)) {
                const resetTime = commandRateLimiter.getResetTime(message.author.id);
                const timeLeft = Math.ceil((resetTime - Date.now()) / 1000);

                await message.reply({
                    content: `⏰ You're sending commands too quickly! Please wait ${timeLeft} seconds.`,
                    allowedMentions: { repliedUser: false }
                });
                return;
            }

            // Ensure user exists in database
            await this.ensureUser(message.author);

            // Check cooldowns
            if (!await this.checkCooldown(message.author.id, commandName, command.data.cooldown || 3)) {
                return; // Cooldown message is handled in checkCooldown
            }

            // Execute command with performance monitoring
            await performanceMonitor.monitorCommand(
                () => command.execute(message, args),
                commandName
            );

            // Set cooldown
            this.setCooldown(message.author.id, commandName, command.data.cooldown || 3);

        } catch (error) {
            console.error(`Error executing command ${commandName}:`, error);
            
            try {
                await message.reply({
                    content: '❌ An error occurred while executing this command. Please try again later.',
                    allowedMentions: { repliedUser: false }
                });
            } catch (replyError) {
                console.error('Error sending error message:', replyError);
            }
        }
    }

    async handleInteraction(interaction) {
        if (!interaction.isChatInputCommand()) return;

        const command = this.slashCommands.get(interaction.commandName);
        if (!command) return;

        try {
            await command.execute(interaction);
        } catch (error) {
            console.error(`Error executing slash command ${interaction.commandName}:`, error);
            
            const errorMessage = {
                content: '❌ An error occurred while executing this command.',
                ephemeral: true
            };

            try {
                if (interaction.replied || interaction.deferred) {
                    await interaction.followUp(errorMessage);
                } else {
                    await interaction.reply(errorMessage);
                }
            } catch (replyError) {
                console.error('Error sending error message:', replyError);
            }
        }
    }

    async handleNewMember(member) {
        try {
            // Auto-create user account for new members
            await this.ensureUser(member.user);
            
            // Send welcome message to specific channel
            const welcomeChannelId = '1400152778692624524';
            const welcomeChannel = member.guild.channels.cache.get(welcomeChannelId);

            if (welcomeChannel) {
                const GameEmbedBuilder = require('./utils/embedBuilder');
                const welcomeEmbed = GameEmbedBuilder.createSuccessEmbed(
                    '🎮 Welcome to AFK Game!',
                    `Welcome to the server, ${member}!\n\n` +
                    `🎯 **Get Started:**\n` +
                    `• Use \`${this.prefix}help\` to see all commands\n` +
                    `• Use \`${this.prefix}profile\` to view your character\n` +
                    `• Use \`${this.prefix}fight\` to start battling monsters\n` +
                    `• Use \`${this.prefix}shop\` to buy equipment\n\n` +
                    `🎁 **New Player Bonus:**\n` +
                    `• 100 Gold to start your adventure\n` +
                    `• Basic equipment ready to use\n\n` +
                    `Have fun and enjoy your AFK adventure! 🗡️✨`
                );

                welcomeEmbed.setColor('#8D689E');
                welcomeEmbed.setThumbnail(member.user.displayAvatarURL());
                welcomeEmbed.setFooter({
                    text: `Player #${member.guild.memberCount} | ${member.user.tag}`,
                    iconURL: member.guild.iconURL()
                });

                await welcomeChannel.send({
                    content: `${member}`,
                    embeds: [welcomeEmbed]
                });

                console.log(`📨 Sent welcome message for ${member.user.tag} to channel ${welcomeChannelId}`);
            } else {
                console.log(`⚠️ Welcome channel ${welcomeChannelId} not found`);
            }
        } catch (error) {
            console.error('Error handling new member:', error);
        }
    }

    async ensureUser(discordUser) {
        try {
            let user = await User.findByDiscordId(discordUser.id);

            if (!user) {
                // Use transaction to ensure both user and character are created together
                const { sequelize } = require('./database/database');
                const transaction = await sequelize.transaction();

                try {
                    // Create new user
                    user = await User.create({
                        discord_id: discordUser.id,
                        username: discordUser.username,
                        discriminator: discordUser.discriminator || '0',
                        avatar_url: discordUser.displayAvatarURL()
                    }, { transaction });

                    // Create character for the user
                    await Character.create({
                        user_id: user.id
                    }, { transaction });

                    // Commit transaction
                    await transaction.commit();

                    console.log(`📝 Created new user and character: ${discordUser.username}${discordUser.discriminator && discordUser.discriminator !== '0' ? '#' + discordUser.discriminator : ''}`);

                } catch (transactionError) {
                    // Rollback transaction on error
                    await transaction.rollback();
                    throw transactionError;
                }
            } else {
                // Update user info if changed
                let updated = false;
                
                if (user.username !== discordUser.username) {
                    user.username = discordUser.username;
                    updated = true;
                }
                
                const newDiscriminator = discordUser.discriminator || '0';
                if (user.discriminator !== newDiscriminator) {
                    user.discriminator = newDiscriminator;
                    updated = true;
                }
                
                const newAvatarUrl = discordUser.displayAvatarURL();
                if (user.avatar_url !== newAvatarUrl) {
                    user.avatar_url = newAvatarUrl;
                    updated = true;
                }
                
                if (updated) {
                    user.last_seen = new Date();
                    await user.save();
                }
            }

            return user;
        } catch (error) {
            console.error('Error ensuring user:', error);

            // If it's a foreign key constraint error, try to find existing user
            if (error.name === 'SequelizeForeignKeyConstraintError') {
                console.log('Foreign key error, attempting to find existing user...');
                try {
                    const existingUser = await User.findByDiscordId(discordUser.id);
                    if (existingUser) {
                        console.log('Found existing user, returning it');
                        return existingUser;
                    }
                } catch (findError) {
                    console.error('Error finding existing user:', findError);
                }
            }

            throw error;
        }
    }

    async checkCooldown(userId, commandName, cooldownSeconds) {
        if (!this.cooldowns.has(commandName)) {
            this.cooldowns.set(commandName, new Collection());
        }

        const now = Date.now();
        const timestamps = this.cooldowns.get(commandName);
        const cooldownAmount = cooldownSeconds * 1000;

        if (timestamps.has(userId)) {
            const expirationTime = timestamps.get(userId) + cooldownAmount;

            if (now < expirationTime) {
                const timeLeft = (expirationTime - now) / 1000;
                return false; // On cooldown
            }
        }

        return true; // Not on cooldown
    }

    setCooldown(userId, commandName, cooldownSeconds) {
        if (!this.cooldowns.has(commandName)) {
            this.cooldowns.set(commandName, new Collection());
        }

        const timestamps = this.cooldowns.get(commandName);
        timestamps.set(userId, Date.now());

        setTimeout(() => timestamps.delete(userId), cooldownSeconds * 1000);
    }

    async shutdown() {
        console.log('🔄 Shutting down bot...');
        
        try {
            await sequelize.close();
            this.client.destroy();
            console.log('✅ Bot shutdown complete');
        } catch (error) {
            console.error('❌ Error during shutdown:', error);
        }
    }
}

// Handle process termination
process.on('SIGINT', async () => {
    console.log('\n🛑 Received SIGINT, shutting down gracefully...');
    if (global.bot) {
        await global.bot.shutdown();
    }
    process.exit(0);
});

process.on('SIGTERM', async () => {
    console.log('\n🛑 Received SIGTERM, shutting down gracefully...');
    if (global.bot) {
        await global.bot.shutdown();
    }
    process.exit(0);
});

// Initialize and start bot
async function startBot() {
    try {
        const bot = new AFKGameBot();
        global.bot = bot;
        await bot.initialize();
    } catch (error) {
        console.error('❌ Failed to start bot:', error);
        process.exit(1);
    }
}

// Start the bot if this file is run directly
if (require.main === module) {
    startBot();
}

module.exports = AFKGameBot;

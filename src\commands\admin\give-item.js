const { SlashCommandBuilder } = require('discord.js');
const { User, Character, Item, UserItem, Transaction } = require('../../database/database');
const GameEmbedBuilder = require('../../utils/embedBuilder');

module.exports = {
    data: new SlashCommandBuilder()
        .setName('admin-give-item')
        .setDescription('Give an item to a user (Admin only)')
        .addUserOption(option =>
            option.setName('user')
                .setDescription('The user to give item to')
                .setRequired(true))
        .addStringOption(option =>
            option.setName('item_name')
                .setDescription('Name of the item to give')
                .setRequired(true))
        .addIntegerOption(option =>
            option.setName('quantity')
                .setDescription('Quantity to give')
                .setRequired(false)
                .setMinValue(1)
                .setMaxValue(100))
        .addIntegerOption(option =>
            option.setName('upgrade_level')
                .setDescription('Upgrade level for the item')
                .setRequired(false)
                .setMinValue(0)
                .setMaxValue(20))
        .addStringOption(option =>
            option.setName('reason')
                .setDescription('Reason for giving item')
                .setRequired(false)),
    
    async execute(interaction) {
        try {
            // Check if user is admin
            if (!this.isAdmin(interaction.user.id)) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    '🚫 Access Denied',
                    'You do not have permission to use this command.'
                );
                return interaction.reply({ embeds: [embed], ephemeral: true });
            }
            
            const targetUser = interaction.options.getUser('user');
            const itemName = interaction.options.getString('item_name');
            const quantity = interaction.options.getInteger('quantity') || 1;
            const upgradeLevel = interaction.options.getInteger('upgrade_level') || 0;
            const reason = interaction.options.getString('reason') || 'Admin gift';
            
            // Get target user from database
            const user = await User.findByDiscordId(targetUser.id);
            if (!user) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'User Not Found',
                    `${targetUser.username} hasn't started playing yet!`
                );
                return interaction.reply({ embeds: [embed], ephemeral: true });
            }
            
            // Find the item
            const items = await Item.findAll({
                where: {
                    name: { [require('sequelize').Op.iLike]: `%${itemName}%` }
                },
                limit: 5
            });
            
            if (items.length === 0) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Item Not Found',
                    `No item found matching "${itemName}".`
                );
                return interaction.reply({ embeds: [embed], ephemeral: true });
            }
            
            // If multiple items found, use the first exact match or first item
            let selectedItem = items.find(item => item.name.toLowerCase() === itemName.toLowerCase()) || items[0];
            
            // If multiple matches and no exact match, show options
            if (items.length > 1 && !items.find(item => item.name.toLowerCase() === itemName.toLowerCase())) {
                let itemList = 'Multiple items found. Please be more specific:\n\n';
                items.forEach((item, index) => {
                    const rarityInfo = item.getRarityInfo();
                    itemList += `**${index + 1}.** ${rarityInfo.emoji} ${item.name} (${rarityInfo.name} ${item.type})\n`;
                });
                
                const embed = GameEmbedBuilder.createWarningEmbed(
                    'Multiple Items Found',
                    itemList
                );
                return interaction.reply({ embeds: [embed], ephemeral: true });
            }
            
            // Validate upgrade level
            if (upgradeLevel > selectedItem.max_upgrade_level) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Invalid Upgrade Level',
                    `Maximum upgrade level for ${selectedItem.name} is ${selectedItem.max_upgrade_level}.`
                );
                return interaction.reply({ embeds: [embed], ephemeral: true });
            }
            
            // Add item to user's inventory
            let userItem = await UserItem.findOne({
                where: {
                    user_id: user.id,
                    item_id: selectedItem.id
                }
            });
            
            if (userItem && (selectedItem.type === 'consumable' || selectedItem.type === 'material')) {
                // Stack consumables and materials
                userItem.quantity += quantity;
                await userItem.save();
            } else {
                // Create new entry for equipment or if user doesn't have the item
                userItem = await UserItem.create({
                    user_id: user.id,
                    item_id: selectedItem.id,
                    quantity: quantity,
                    upgrade_level: upgradeLevel,
                    acquired_from: 'admin'
                });
            }
            
            // Create transaction record
            await Transaction.create({
                user_id: user.id,
                type: 'admin',
                gold_change: 0,
                gems_change: 0,
                gold_balance_after: (await Character.findOne({ where: { user_id: user.id } })).gold,
                gems_balance_after: (await Character.findOne({ where: { user_id: user.id } })).gems,
                description: `Admin gave ${quantity}x ${selectedItem.name}${upgradeLevel > 0 ? ` +${upgradeLevel}` : ''}: ${reason}`,
                reference_type: 'item',
                reference_id: selectedItem.id,
                items_involved: [{
                    item_id: selectedItem.id,
                    quantity: quantity,
                    action: 'gained'
                }],
                admin_id: interaction.user.id,
                admin_reason: reason
            });
            
            // Create success embed
            const rarityInfo = selectedItem.getRarityInfo();
            const embed = GameEmbedBuilder.createSuccessEmbed(
                '🎁 Item Given Successfully',
                `Successfully gave **${quantity}x ${rarityInfo.emoji} ${selectedItem.name}${upgradeLevel > 0 ? ` +${upgradeLevel}` : ''}** to ${targetUser.username}!`
            );
            
            embed.addFields(
                {
                    name: '📦 Item Details',
                    value: `**Name:** ${selectedItem.name}\n**Type:** ${selectedItem.type}\n**Rarity:** ${rarityInfo.name}\n**Quantity:** ${quantity}`,
                    inline: true
                },
                {
                    name: '⬆️ Enhancement',
                    value: `**Upgrade Level:** +${upgradeLevel}\n**Max Upgrade:** +${selectedItem.max_upgrade_level}`,
                    inline: true
                }
            );
            
            // Show item stats if applicable
            if (selectedItem.attack_bonus > 0 || selectedItem.defense_bonus > 0 || selectedItem.health_bonus > 0) {
                const stats = selectedItem.getTotalStats(upgradeLevel);
                let statsText = '';
                if (stats.attack > 0) statsText += `**Attack:** +${stats.attack}\n`;
                if (stats.defense > 0) statsText += `**Defense:** +${stats.defense}\n`;
                if (stats.health > 0) statsText += `**Health:** +${stats.health}\n`;
                if (stats.crit_rate > 0) statsText += `**Crit Rate:** +${stats.crit_rate}%\n`;
                
                if (statsText) {
                    embed.addFields({
                        name: '⚔️ Item Stats',
                        value: statsText,
                        inline: false
                    });
                }
            }
            
            embed.addFields({
                name: '👑 Admin Info',
                value: `**Admin:** ${interaction.user.username}\n**Reason:** ${reason}\n**Timestamp:** <t:${Math.floor(Date.now() / 1000)}:F>`,
                inline: false
            });
            
            embed.setFooter({ text: 'This action has been logged for audit purposes.' });
            
            await interaction.reply({ embeds: [embed] });
            
            // Try to notify the user
            try {
                const notifyEmbed = GameEmbedBuilder.createSuccessEmbed(
                    '🎁 You Received an Item!',
                    `An admin has given you **${quantity}x ${rarityInfo.emoji} ${selectedItem.name}${upgradeLevel > 0 ? ` +${upgradeLevel}` : ''}**!`
                );
                
                notifyEmbed.addFields({
                    name: '📋 Details',
                    value: `**Item:** ${selectedItem.name}\n**Quantity:** ${quantity}\n**Upgrade:** +${upgradeLevel}\n**Reason:** ${reason}`,
                    inline: false
                });
                
                if (['weapon', 'armor', 'helmet', 'boots', 'ring', 'necklace'].includes(selectedItem.type)) {
                    notifyEmbed.addFields({
                        name: '💡 Tip',
                        value: `Use \`!equip ${selectedItem.name}\` to equip this item!`,
                        inline: false
                    });
                }
                
                await targetUser.send({ embeds: [notifyEmbed] });
            } catch (error) {
                console.log(`Could not notify user ${targetUser.username} about item gift:`, error.message);
            }
            
        } catch (error) {
            console.error('Error in admin-give-item command:', error);
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Command Error',
                'An error occurred while giving the item. Please try again.'
            );
            await interaction.reply({ embeds: [embed], ephemeral: true });
        }
    },
    
    isAdmin(userId) {
        const adminIds = process.env.ADMIN_IDS ? process.env.ADMIN_IDS.split(',') : [];
        return adminIds.includes(userId);
    }
};

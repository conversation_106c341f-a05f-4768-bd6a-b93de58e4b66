const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
    const Pet = sequelize.define('Pet', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notEmpty: true,
                len: [1, 50]
            }
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        type: {
            type: DataTypes.ENUM,
            values: ['combat', 'support', 'gathering', 'special'],
            allowNull: false,
            defaultValue: 'combat'
        },
        rarity: {
            type: DataTypes.ENUM,
            values: ['common', 'rare', 'epic', 'legendary', 'mythic'],
            allowNull: false,
            defaultValue: 'common'
        },
        // Base stats
        base_attack: {
            type: DataTypes.INTEGER,
            defaultValue: 5,
            validate: {
                min: 0
            }
        },
        base_defense: {
            type: DataTypes.INTEGER,
            defaultValue: 3,
            validate: {
                min: 0
            }
        },
        base_health: {
            type: DataTypes.INTEGER,
            defaultValue: 50,
            validate: {
                min: 1
            }
        },
        // Growth rates per level
        attack_growth: {
            type: DataTypes.DECIMAL(4, 2),
            defaultValue: 1.00,
            validate: {
                min: 0
            }
        },
        defense_growth: {
            type: DataTypes.DECIMAL(4, 2),
            defaultValue: 0.50,
            validate: {
                min: 0
            }
        },
        health_growth: {
            type: DataTypes.DECIMAL(4, 2),
            defaultValue: 5.00,
            validate: {
                min: 0
            }
        },
        // Special abilities
        special_abilities: {
            type: DataTypes.JSONB,
            defaultValue: []
            // Format: [{ name: 'Critical Strike', description: '...', effect: {...} }]
        },
        // Evolution
        max_level: {
            type: DataTypes.INTEGER,
            defaultValue: 50,
            validate: {
                min: 1,
                max: 100
            }
        },
        evolution_pet_id: {
            type: DataTypes.INTEGER,
            allowNull: true,
            references: {
                model: 'pets',
                key: 'id'
            }
        },
        evolution_level_required: {
            type: DataTypes.INTEGER,
            allowNull: true,
            validate: {
                min: 1
            }
        },
        evolution_materials: {
            type: DataTypes.JSONB,
            defaultValue: []
            // Format: [{ item_id: 1, quantity: 5 }]
        },
        // Acquisition
        acquisition_method: {
            type: DataTypes.ENUM,
            values: ['gacha', 'shop', 'quest', 'event', 'evolution', 'vip_exclusive'],
            allowNull: false,
            defaultValue: 'gacha'
        },
        acquisition_cost: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0
            }
        },
        acquisition_currency: {
            type: DataTypes.ENUM,
            values: ['gold', 'gems'],
            defaultValue: 'gems'
        },
        // Requirements
        level_requirement: {
            type: DataTypes.INTEGER,
            defaultValue: 1,
            validate: {
                min: 1,
                max: 100
            }
        },
        vip_requirement: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0,
                max: 5
            }
        },
        // Visual
        emoji: {
            type: DataTypes.STRING,
            defaultValue: '🐾'
        },
        color: {
            type: DataTypes.STRING(7),
            allowNull: true,
            validate: {
                is: /^#[0-9A-F]{6}$/i
            }
        },
        // Availability
        is_available: {
            type: DataTypes.BOOLEAN,
            defaultValue: true
        },
        is_event_pet: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        },
        event_start: {
            type: DataTypes.DATE,
            allowNull: true
        },
        event_end: {
            type: DataTypes.DATE,
            allowNull: true
        }
    }, {
        tableName: 'pets',
        indexes: [
            {
                fields: ['type']
            },
            {
                fields: ['rarity']
            },
            {
                fields: ['acquisition_method']
            },
            {
                fields: ['is_available']
            }
        ]
    });

    // Static methods
    Pet.getRarityInfo = function(rarity) {
        const rarityData = {
            common: { name: 'Common', color: '#FFFFFF', emoji: '⚪', multiplier: 1.0 },
            rare: { name: 'Rare', color: '#00FF00', emoji: '🟢', multiplier: 1.5 },
            epic: { name: 'Epic', color: '#9932CC', emoji: '🟣', multiplier: 2.0 },
            legendary: { name: 'Legendary', color: '#FFA500', emoji: '🟠', multiplier: 3.0 },
            mythic: { name: 'Mythic', color: '#FF0000', emoji: '🔴', multiplier: 5.0 }
        };
        return rarityData[rarity] || rarityData.common;
    };

    Pet.getTypeInfo = function(type) {
        const typeData = {
            combat: { name: 'Combat', emoji: '⚔️', description: 'Assists in battle' },
            support: { name: 'Support', emoji: '🛡️', description: 'Provides buffs and healing' },
            gathering: { name: 'Gathering', emoji: '⛏️', description: 'Improves resource gathering' },
            special: { name: 'Special', emoji: '✨', description: 'Unique abilities' }
        };
        return typeData[type] || typeData.combat;
    };

    // Instance methods
    Pet.prototype.getRarityInfo = function() {
        return Pet.getRarityInfo(this.rarity);
    };

    Pet.prototype.getTypeInfo = function() {
        return Pet.getTypeInfo(this.type);
    };

    Pet.prototype.getFormattedName = function() {
        const rarityInfo = this.getRarityInfo();
        return `${rarityInfo.emoji} ${this.name} ${this.emoji}`;
    };

    Pet.prototype.getStatsAtLevel = function(level) {
        const clampedLevel = Math.min(level, this.max_level);
        
        return {
            attack: Math.floor(this.base_attack + (this.attack_growth * (clampedLevel - 1))),
            defense: Math.floor(this.base_defense + (this.defense_growth * (clampedLevel - 1))),
            health: Math.floor(this.base_health + (this.health_growth * (clampedLevel - 1)))
        };
    };

    Pet.prototype.canEvolve = function() {
        return this.evolution_pet_id !== null;
    };

    Pet.prototype.getEvolutionRequirements = function() {
        if (!this.canEvolve()) return null;

        return {
            level: this.evolution_level_required,
            materials: this.evolution_materials || []
        };
    };

    Pet.prototype.isAvailableFor = function(character) {
        if (!this.is_available) return false;
        if (character.level < this.level_requirement) return false;
        if (character.vip_level < this.vip_requirement) return false;

        // Check event availability
        if (this.is_event_pet) {
            const now = new Date();
            if (this.event_start && now < this.event_start) return false;
            if (this.event_end && now > this.event_end) return false;
        }

        return true;
    };

    return Pet;
};

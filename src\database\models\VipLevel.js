const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
    const VipLevel = sequelize.define('VipLevel', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        level: {
            type: DataTypes.INTEGER,
            allowNull: false,
            unique: true,
            validate: {
                min: 0,
                max: 5
            }
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false
        },
        required_spending: {
            type: DataTypes.DECIMAL(10, 2),
            allowNull: false,
            validate: {
                min: 0
            }
        },
        cooldown_reduction: {
            type: DataTypes.DECIMAL(3, 2),
            defaultValue: 0.00,
            validate: {
                min: 0,
                max: 1
            }
        },
        gold_bonus: {
            type: DataTypes.DECIMAL(3, 2),
            defaultValue: 0.00,
            validate: {
                min: 0
            }
        },
        daily_gems: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0
            }
        },
        gacha_discount: {
            type: DataTypes.DECIMAL(3, 2),
            defaultValue: 0.00,
            validate: {
                min: 0,
                max: 1
            }
        },
        special_features: {
            type: DataTypes.JSONB,
            defaultValue: []
        },
        color: {
            type: DataTypes.STRING(7),
            defaultValue: '#FFFFFF',
            validate: {
                is: /^#[0-9A-F]{6}$/i
            }
        },
        icon: {
            type: DataTypes.STRING,
            allowNull: true
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true
        }
    }, {
        tableName: 'vip_levels',
        indexes: [
            {
                unique: true,
                fields: ['level']
            }
        ]
    });

    // Static method to get VIP benefits
    VipLevel.getBenefits = function(level) {
        const benefits = {
            0: {
                name: 'Free Player',
                cooldown_reduction: 0,
                gold_bonus: 0,
                daily_gems: 0,
                gacha_discount: 0,
                special_features: [],
                color: '#FFFFFF'
            },
            1: {
                name: 'VIP Bronze',
                cooldown_reduction: 0.25,
                gold_bonus: 0.10,
                daily_gems: 10,
                gacha_discount: 0.05,
                special_features: ['priority_support'],
                color: '#CD7F32'
            },
            2: {
                name: 'VIP Silver',
                cooldown_reduction: 0.35,
                gold_bonus: 0.20,
                daily_gems: 25,
                gacha_discount: 0.10,
                special_features: ['priority_support', 'exclusive_chat'],
                color: '#C0C0C0'
            },
            3: {
                name: 'VIP Gold',
                cooldown_reduction: 0.45,
                gold_bonus: 0.30,
                daily_gems: 50,
                gacha_discount: 0.15,
                special_features: ['priority_support', 'exclusive_chat', 'premium_gacha'],
                color: '#FFD700'
            },
            4: {
                name: 'VIP Platinum',
                cooldown_reduction: 0.55,
                gold_bonus: 0.50,
                daily_gems: 100,
                gacha_discount: 0.20,
                special_features: ['priority_support', 'exclusive_chat', 'premium_gacha', 'exclusive_pets'],
                color: '#E5E4E2'
            },
            5: {
                name: 'VIP Diamond',
                cooldown_reduction: 0.65,
                gold_bonus: 1.00,
                daily_gems: 200,
                gacha_discount: 0.25,
                special_features: ['priority_support', 'exclusive_chat', 'premium_gacha', 'exclusive_pets', 'special_titles'],
                color: '#B9F2FF'
            }
        };

        return benefits[level] || benefits[0];
    };

    // Instance methods
    VipLevel.prototype.getFormattedName = function() {
        return `${this.icon || '⭐'} ${this.name}`;
    };

    VipLevel.prototype.getFeaturesList = function() {
        const features = [];
        
        if (this.cooldown_reduction > 0) {
            features.push(`-${Math.round(this.cooldown_reduction * 100)}% Cooldown`);
        }
        
        if (this.gold_bonus > 0) {
            features.push(`+${Math.round(this.gold_bonus * 100)}% Gold`);
        }
        
        if (this.daily_gems > 0) {
            features.push(`${this.daily_gems} Daily Gems`);
        }
        
        if (this.gacha_discount > 0) {
            features.push(`${Math.round(this.gacha_discount * 100)}% Gacha Discount`);
        }

        // Add special features
        if (this.special_features && this.special_features.length > 0) {
            const specialNames = {
                'priority_support': 'Priority Support',
                'exclusive_chat': 'Exclusive Chat',
                'premium_gacha': 'Premium Gacha',
                'exclusive_pets': 'Exclusive Pets',
                'special_titles': 'Special Titles'
            };

            this.special_features.forEach(feature => {
                if (specialNames[feature]) {
                    features.push(specialNames[feature]);
                }
            });
        }

        return features;
    };

    return VipLevel;
};

const { Collection } = require('discord.js');
const logger = require('../utils/logger');
const { User, Character } = require('../database/database');

module.exports = {
    name: 'messageCreate',
    async execute(message) {
        // Ignore bot messages and messages without prefix
        if (message.author.bot) return;
        if (!message.content.startsWith(process.env.PREFIX || '!')) return;

        // Parse command and arguments
        const args = message.content.slice(process.env.PREFIX.length || 1).trim().split(/ +/);
        const commandName = args.shift().toLowerCase();

        // Get command (including aliases)
        const command = message.client.commands.get(commandName) ||
                       message.client.commands.find(cmd => cmd.data.aliases && cmd.data.aliases.includes(commandName));

        if (!command) return;

        // Check if maintenance mode is enabled (except for owner)
        if (process.env.MAINTENANCE_MODE === 'true' && message.author.id !== process.env.OWNER_ID) {
            return message.reply('🔧 <PERSON>t đang trong chế độ bảo trì. Vui lòng thử lại sau!');
        }

        // Initialize cooldowns collection if not exists
        if (!message.client.cooldowns.has(command.data.name)) {
            message.client.cooldowns.set(command.data.name, new Collection());
        }

        const now = Date.now();
        const timestamps = message.client.cooldowns.get(command.data.name);
        const cooldownAmount = (command.data.cooldown || 3) * 1000;

        // Check cooldown
        if (timestamps.has(message.author.id)) {
            const expirationTime = timestamps.get(message.author.id) + cooldownAmount;

            if (now < expirationTime) {
                const timeLeft = (expirationTime - now) / 1000;
                return message.reply(`⏰ Vui lòng đợi ${timeLeft.toFixed(1)} giây trước khi sử dụng lệnh \`${command.data.name}\` lại!`);
            }
        }

        // Set cooldown
        timestamps.set(message.author.id, now);
        setTimeout(() => timestamps.delete(message.author.id), cooldownAmount);

        try {
            // Ensure user exists in database
            await ensureUserExists(message.author);
            
            // Execute command
            await command.execute(message, args);
            
            // Log command usage
            logger.info(`Command executed: ${command.data.name} by ${message.author.tag} (${message.author.id})`);
            
        } catch (error) {
            logger.error(`Error executing command ${command.data.name}:`, error);
            
            const errorMessage = process.env.NODE_ENV === 'development' 
                ? `❌ Có lỗi xảy ra khi thực hiện lệnh!\n\`\`\`${error.message}\`\`\``
                : '❌ Có lỗi xảy ra khi thực hiện lệnh! Vui lòng thử lại sau.';
                
            await message.reply(errorMessage);
        }
    }
};

async function ensureUserExists(discordUser) {
    try {
        const [user, created] = await User.findOrCreate({
            where: { discord_id: discordUser.id },
            defaults: {
                discord_id: discordUser.id,
                username: discordUser.username,
                discriminator: discordUser.discriminator,
                avatar: discordUser.avatar
            }
        });

        if (created) {
            // Create character for new user
            await Character.create({
                user_id: user.id,
                level: 1,
                experience: 0,
                gold: 100, // Starting gold
                gems: 0,
                health: 100,
                max_health: 100,
                attack: 10,
                defense: 5,
                crit_rate: 5.0,
                crit_damage: 150.0,
                luck: 1.0,
                vip_level: 0
            });

            logger.info(`New user created: ${discordUser.username} (${discordUser.id})`);
        } else {
            // Update user info if changed
            const updateData = {};
            if (user.username !== discordUser.username) updateData.username = discordUser.username;
            if (user.discriminator !== discordUser.discriminator) updateData.discriminator = discordUser.discriminator;
            if (user.avatar !== discordUser.avatar) updateData.avatar = discordUser.avatar;

            if (Object.keys(updateData).length > 0) {
                await user.update(updateData);
            }
        }

        return user;
    } catch (error) {
        logger.error('Error ensuring user exists:', error);
        throw error;
    }
}

const { User, Character, UserItem, Item, Transaction } = require('../../database/database');
const GameEmbedBuilder = require('../../utils/embedBuilder');

module.exports = {
    data: {
        name: 'upgrade',
        aliases: ['up', 'enhance', 'improve'],
        description: 'Upgrade an item to increase its stats',
        usage: '!upgrade <item_name>',
        cooldown: 5
    },
    async execute(message, args) {
        try {
            if (args.length === 0) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Missing Item Name',
                    'Please specify the item you want to upgrade.\n**Usage:** `!upgrade <item_name>`'
                );
                return message.reply({ embeds: [embed] });
            }
            
            // Get user and character
            const user = await User.findByDiscordId(message.author.id);
            const character = await Character.findOne({
                where: { user_id: user.id }
            });
            
            const itemName = args.join(' ').toLowerCase();
            
            // Find the item in user's inventory
            const userItems = await UserItem.findAll({
                where: {
                    user_id: user.id,
                    quantity: { [require('sequelize').Op.gt]: 0 }
                },
                include: [{
                    model: Item,
                    as: 'item',
                    where: {
                        name: { [require('sequelize').Op.iLike]: `%${itemName}%` },
                        is_upgradeable: true
                    }
                }]
            });
            
            if (userItems.length === 0) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Item Not Found',
                    `You don't have any upgradeable item matching "${itemName}" in your inventory.`
                );
                return message.reply({ embeds: [embed] });
            }
            
            // If multiple matches, show options
            if (userItems.length > 1) {
                const embed = GameEmbedBuilder.createInfoEmbed(
                    'Multiple Items Found',
                    'Multiple items match your search. Please be more specific:'
                );
                
                let itemList = '';
                userItems.forEach((userItem, index) => {
                    const item = userItem.item;
                    const rarityInfo = item.getRarityInfo();
                    const upgradeCost = userItem.getUpgradeCost();
                    const equipped = userItem.is_equipped ? ' ✅' : '';
                    const upgrade = userItem.upgrade_level > 0 ? ` +${userItem.upgrade_level}` : '';
                    
                    itemList += `**${index + 1}.** ${rarityInfo.emoji} ${item.name}${upgrade}${equipped}\n`;
                    
                    if (upgradeCost) {
                        itemList += `*Upgrade Cost: ${upgradeCost} 🪙*\n`;
                    } else {
                        itemList += `*Max level reached*\n`;
                    }
                    itemList += '\n';
                });
                
                embed.setDescription(itemList);
                embed.setFooter({ text: 'Use the exact item name to upgrade it' });
                
                return message.reply({ embeds: [embed] });
            }
            
            const userItem = userItems[0];
            const item = userItem.item;
            
            // Check if item can be upgraded
            if (!userItem.canUpgrade()) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Cannot Upgrade',
                    `**${item.name}** is already at maximum upgrade level (+${item.max_upgrade_level})!`
                );
                return message.reply({ embeds: [embed] });
            }
            
            // Get upgrade cost
            const upgradeCost = userItem.getUpgradeCost();
            
            // Check if user has enough gold
            if (character.gold < upgradeCost) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Insufficient Gold',
                    `You need **${upgradeCost} 🪙** to upgrade **${item.name}**.\n` +
                    `Your current gold: **${character.gold.toLocaleString()} 🪙**`
                );
                return message.reply({ embeds: [embed] });
            }
            
            // Calculate stats before and after upgrade
            const currentStats = userItem.getTotalStats();
            const nextLevelStats = item.getTotalStats(userItem.upgrade_level + 1);
            
            // Create confirmation embed
            const rarityInfo = item.getRarityInfo();
            const confirmEmbed = GameEmbedBuilder.createWarningEmbed(
                '⬆️ Confirm Upgrade',
                `Are you sure you want to upgrade **${rarityInfo.emoji} ${item.name}**?`
            );
            
            confirmEmbed.addFields(
                {
                    name: '💰 Upgrade Cost',
                    value: `**${upgradeCost} 🪙**\nRemaining: ${(character.gold - upgradeCost).toLocaleString()} 🪙`,
                    inline: true
                },
                {
                    name: '📊 Current Level',
                    value: `**+${userItem.upgrade_level}** → **+${userItem.upgrade_level + 1}**`,
                    inline: true
                }
            );
            
            // Show stat improvements
            let statImprovements = '';
            if (nextLevelStats.attack > currentStats.attack) {
                statImprovements += `**Attack:** ${currentStats.attack} → ${nextLevelStats.attack} (+${nextLevelStats.attack - currentStats.attack})\n`;
            }
            if (nextLevelStats.defense > currentStats.defense) {
                statImprovements += `**Defense:** ${currentStats.defense} → ${nextLevelStats.defense} (+${nextLevelStats.defense - currentStats.defense})\n`;
            }
            if (nextLevelStats.health > currentStats.health) {
                statImprovements += `**Health:** ${currentStats.health} → ${nextLevelStats.health} (+${nextLevelStats.health - currentStats.health})\n`;
            }
            if (nextLevelStats.crit_rate > currentStats.crit_rate) {
                statImprovements += `**Crit Rate:** ${currentStats.crit_rate}% → ${nextLevelStats.crit_rate}% (+${(nextLevelStats.crit_rate - currentStats.crit_rate).toFixed(2)}%)\n`;
            }
            if (nextLevelStats.crit_damage > currentStats.crit_damage) {
                statImprovements += `**Crit Damage:** ${currentStats.crit_damage}% → ${nextLevelStats.crit_damage}% (+${(nextLevelStats.crit_damage - currentStats.crit_damage).toFixed(2)}%)\n`;
            }
            if (nextLevelStats.luck > currentStats.luck) {
                statImprovements += `**Luck:** ${currentStats.luck} → ${nextLevelStats.luck} (+${(nextLevelStats.luck - currentStats.luck).toFixed(2)})\n`;
            }
            
            if (statImprovements) {
                confirmEmbed.addFields({
                    name: '📈 Stat Improvements',
                    value: statImprovements,
                    inline: false
                });
            }
            
            // Show upgrade success chance (could be implemented later)
            confirmEmbed.addFields({
                name: '✨ Success Rate',
                value: '100% (Upgrades always succeed)',
                inline: false
            });
            
            confirmEmbed.setFooter({ text: 'React with ✅ to confirm or ❌ to cancel' });
            
            const confirmMessage = await message.reply({ embeds: [confirmEmbed] });
            await confirmMessage.react('✅');
            await confirmMessage.react('❌');
            
            // Wait for confirmation
            const filter = (reaction, user) => {
                return ['✅', '❌'].includes(reaction.emoji.name) && user.id === message.author.id;
            };
            
            const collector = confirmMessage.createReactionCollector({ filter, time: 30000, max: 1 });
            
            collector.on('collect', async (reaction) => {
                if (reaction.emoji.name === '❌') {
                    const cancelEmbed = GameEmbedBuilder.createInfoEmbed(
                        'Upgrade Cancelled',
                        'You decided not to upgrade the item.'
                    );
                    await confirmMessage.edit({ embeds: [cancelEmbed] });
                    return;
                }
                
                // Process the upgrade
                try {
                    // Deduct gold
                    character.gold -= upgradeCost;
                    await character.save();
                    
                    // Upgrade the item
                    const oldLevel = userItem.upgrade_level;
                    userItem.upgrade();
                    await userItem.save();
                    
                    // Create transaction record
                    await Transaction.create({
                        user_id: user.id,
                        type: 'upgrade',
                        gold_change: -upgradeCost,
                        gems_change: 0,
                        gold_balance_after: character.gold,
                        gems_balance_after: character.gems,
                        description: `Upgraded ${item.name} to +${userItem.upgrade_level}`,
                        reference_type: 'item',
                        reference_id: item.id
                    });
                    
                    // Create success embed
                    const successEmbed = GameEmbedBuilder.createSuccessEmbed(
                        '⬆️ Upgrade Successful!',
                        `**${rarityInfo.emoji} ${item.name}** has been upgraded!`
                    );
                    
                    successEmbed.addFields(
                        {
                            name: '📊 Upgrade Result',
                            value: `**+${oldLevel}** → **+${userItem.upgrade_level}**`,
                            inline: true
                        },
                        {
                            name: '💰 Cost',
                            value: `**${upgradeCost} 🪙**\nRemaining: ${character.gold.toLocaleString()} 🪙`,
                            inline: true
                        }
                    );
                    
                    // Show new stats
                    const newStats = userItem.getTotalStats();
                    let newStatsText = '';
                    if (newStats.attack > 0) newStatsText += `**Attack:** ${newStats.attack}\n`;
                    if (newStats.defense > 0) newStatsText += `**Defense:** ${newStats.defense}\n`;
                    if (newStats.health > 0) newStatsText += `**Health:** ${newStats.health}\n`;
                    if (newStats.crit_rate > 0) newStatsText += `**Crit Rate:** ${newStats.crit_rate}%\n`;
                    if (newStats.crit_damage > 0) newStatsText += `**Crit Damage:** ${newStats.crit_damage}%\n`;
                    if (newStats.luck > 0) newStatsText += `**Luck:** ${newStats.luck}\n`;
                    
                    if (newStatsText) {
                        successEmbed.addFields({
                            name: '📈 New Stats',
                            value: newStatsText,
                            inline: false
                        });
                    }
                    
                    // Show next upgrade cost if not at max level
                    const nextUpgradeCost = userItem.getUpgradeCost();
                    if (nextUpgradeCost) {
                        successEmbed.addFields({
                            name: '🔮 Next Upgrade',
                            value: `Cost: **${nextUpgradeCost} 🪙**\nMax Level: +${item.max_upgrade_level}`,
                            inline: false
                        });
                    } else {
                        successEmbed.addFields({
                            name: '🏆 Maximum Level',
                            value: 'This item has reached its maximum upgrade level!',
                            inline: false
                        });
                    }
                    
                    successEmbed.setFooter({ text: 'Use !profile to see how this affects your total stats' });
                    
                    await confirmMessage.edit({ embeds: [successEmbed] });
                    
                } catch (error) {
                    console.error('Error processing upgrade:', error);
                    const errorEmbed = GameEmbedBuilder.createErrorEmbed(
                        'Upgrade Error',
                        'An error occurred while upgrading the item. Please try again.'
                    );
                    await confirmMessage.edit({ embeds: [errorEmbed] });
                }
            });
            
            collector.on('end', (collected) => {
                if (collected.size === 0) {
                    const timeoutEmbed = GameEmbedBuilder.createWarningEmbed(
                        'Upgrade Timeout',
                        'You took too long to confirm. Upgrade cancelled.'
                    );
                    confirmMessage.edit({ embeds: [timeoutEmbed] }).catch(console.error);
                }
                confirmMessage.reactions.removeAll().catch(console.error);
            });
            
        } catch (error) {
            console.error('Error in upgrade command:', error);
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Upgrade Error',
                'An error occurred while upgrading the item. Please try again.'
            );
            await message.reply({ embeds: [embed] });
        }
    }
};

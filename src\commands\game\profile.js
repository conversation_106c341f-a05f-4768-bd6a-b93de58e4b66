const { User, Character } = require('../../database/database');
const GameEmbedBuilder = require('../../utils/embedBuilder');

module.exports = {
    data: {
        name: 'profile',
        aliases: ['p', 'me', 'stats'],
        description: 'View your character profile and stats',
        usage: '!profile [user]',
        cooldown: 3
    },
    async execute(message, args) {
        try {
            let targetUser = message.author;
            
            // Check if user mentioned someone else
            if (args.length > 0) {
                const mention = message.mentions.users.first();
                if (mention) {
                    targetUser = mention;
                } else {
                    // Try to find user by username
                    const username = args.join(' ');
                    const foundUser = message.guild?.members.cache.find(member => 
                        member.user.username.toLowerCase().includes(username.toLowerCase()) ||
                        member.displayName.toLowerCase().includes(username.toLowerCase())
                    );
                    
                    if (foundUser) {
                        targetUser = foundUser.user;
                    }
                }
            }
            
            // Get user from database
            const user = await User.findByDiscordId(targetUser.id);
            if (!user) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'User Not Found',
                    `${targetUser.username} hasn't started playing yet! Use any command to begin.`
                );
                return message.reply({ embeds: [embed] });
            }
            
            // Get character data
            const character = await Character.findOne({
                where: { user_id: user.id },
                include: [
                    { association: 'weapon', required: false },
                    { association: 'armor', required: false },
                    { association: 'helmet', required: false },
                    { association: 'boots', required: false },
                    { association: 'ring', required: false },
                    { association: 'necklace', required: false }
                ]
            });
            
            if (!character) {
                const embed = GameEmbedBuilder.createErrorEmbed(
                    'Character Not Found',
                    'Character data is corrupted. Please contact an administrator.'
                );
                return message.reply({ embeds: [embed] });
            }
            
            // Create profile embed
            const embed = GameEmbedBuilder.createProfileEmbed(user, character);
            
            // Add equipment information
            const equipment = [];
            const slots = ['weapon', 'armor', 'helmet', 'boots', 'ring', 'necklace'];
            
            slots.forEach(slot => {
                const item = character[slot];
                if (item) {
                    const rarityInfo = item.getRarityInfo();
                    equipment.push(`**${slot.charAt(0).toUpperCase() + slot.slice(1)}:** ${rarityInfo.emoji} ${item.name}`);
                } else {
                    equipment.push(`**${slot.charAt(0).toUpperCase() + slot.slice(1)}:** *None*`);
                }
            });
            
            if (equipment.length > 0) {
                embed.addFields({
                    name: '⚔️ Equipment',
                    value: equipment.join('\n'),
                    inline: false
                });
            }
            
            // Add cooldown information
            const cooldowns = [];
            const actions = ['fight', 'adventure', 'boss', 'daily'];
            
            actions.forEach(action => {
                if (character.isOnCooldown(action)) {
                    const remaining = character.getCooldownRemaining(action);
                    const minutes = Math.floor(remaining / 60000);
                    const seconds = Math.floor((remaining % 60000) / 1000);
                    
                    let timeText = '';
                    if (minutes > 0) {
                        timeText = `${minutes}m ${seconds}s`;
                    } else {
                        timeText = `${seconds}s`;
                    }
                    
                    cooldowns.push(`**${action.charAt(0).toUpperCase() + action.slice(1)}:** ${timeText}`);
                } else {
                    cooldowns.push(`**${action.charAt(0).toUpperCase() + action.slice(1)}:** Ready ✅`);
                }
            });
            
            if (cooldowns.length > 0) {
                embed.addFields({
                    name: '⏰ Cooldowns',
                    value: cooldowns.join('\n'),
                    inline: false
                });
            }
            
            // Add some statistics
            const { BattleLog } = require('../../database/database');
            const recentBattles = await BattleLog.findAll({
                where: { user_id: user.id },
                limit: 10,
                order: [['created_at', 'DESC']]
            });
            
            const victories = recentBattles.filter(battle => battle.result === 'victory').length;
            const defeats = recentBattles.filter(battle => battle.result === 'defeat').length;
            const winRate = recentBattles.length > 0 ? Math.round((victories / recentBattles.length) * 100) : 0;
            
            embed.addFields({
                name: '📊 Recent Battle Stats (Last 10)',
                value: `**Victories:** ${victories}\n**Defeats:** ${defeats}\n**Win Rate:** ${winRate}%`,
                inline: true
            });
            
            // Set user avatar as thumbnail
            if (targetUser.displayAvatarURL()) {
                embed.setThumbnail(targetUser.displayAvatarURL());
            }
            
            await message.reply({ embeds: [embed] });
            
        } catch (error) {
            console.error('Error in profile command:', error);
            const embed = GameEmbedBuilder.createErrorEmbed(
                'Command Error',
                'An error occurred while fetching the profile. Please try again.'
            );
            await message.reply({ embeds: [embed] });
        }
    }
};

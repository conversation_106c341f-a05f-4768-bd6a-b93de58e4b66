const { DataTypes } = require('sequelize');

module.exports = (sequelize) => {
    const Monster = sequelize.define('Monster', {
        id: {
            type: DataTypes.INTEGER,
            primaryKey: true,
            autoIncrement: true
        },
        name: {
            type: DataTypes.STRING,
            allowNull: false,
            validate: {
                notEmpty: true,
                len: [1, 100]
            }
        },
        description: {
            type: DataTypes.TEXT,
            allowNull: true
        },
        type: {
            type: DataTypes.ENUM,
            values: ['normal', 'elite', 'boss', 'raid_boss'],
            allowNull: false,
            defaultValue: 'normal'
        },
        level: {
            type: DataTypes.INTEGER,
            allowNull: false,
            validate: {
                min: 1,
                max: 100
            }
        },
        // Stats
        health: {
            type: DataTypes.INTEGER,
            allowNull: false,
            validate: {
                min: 1
            }
        },
        attack: {
            type: DataTypes.INTEGER,
            allowNull: false,
            validate: {
                min: 1
            }
        },
        defense: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0
            }
        },
        crit_rate: {
            type: DataTypes.DECIMAL(5, 2),
            defaultValue: 5.00,
            validate: {
                min: 0,
                max: 100
            }
        },
        crit_damage: {
            type: DataTypes.DECIMAL(6, 2),
            defaultValue: 150.00,
            validate: {
                min: 100
            }
        },
        // Rewards
        base_exp: {
            type: DataTypes.INTEGER,
            allowNull: false,
            validate: {
                min: 1
            }
        },
        base_gold: {
            type: DataTypes.INTEGER,
            allowNull: false,
            validate: {
                min: 0
            }
        },
        gem_reward: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0
            }
        },
        // Drop system
        drop_table: {
            type: DataTypes.JSONB,
            defaultValue: []
            // Format: [{ item_id: 1, chance: 0.1, min_quantity: 1, max_quantity: 1 }]
        },
        rare_drop_table: {
            type: DataTypes.JSONB,
            defaultValue: []
        },
        // Requirements
        min_level_requirement: {
            type: DataTypes.INTEGER,
            defaultValue: 1,
            validate: {
                min: 1,
                max: 100
            }
        },
        vip_requirement: {
            type: DataTypes.INTEGER,
            defaultValue: 0,
            validate: {
                min: 0,
                max: 5
            }
        },
        // Spawn information
        location: {
            type: DataTypes.STRING,
            allowNull: true
        },
        spawn_rate: {
            type: DataTypes.DECIMAL(5, 4),
            defaultValue: 1.0000,
            validate: {
                min: 0,
                max: 1
            }
        },
        // Visual
        emoji: {
            type: DataTypes.STRING,
            defaultValue: '👹'
        },
        color: {
            type: DataTypes.STRING(7),
            defaultValue: '#FF0000',
            validate: {
                is: /^#[0-9A-F]{6}$/i
            }
        },
        // Special abilities
        special_abilities: {
            type: DataTypes.JSONB,
            defaultValue: []
        },
        // Availability
        is_active: {
            type: DataTypes.BOOLEAN,
            defaultValue: true
        },
        is_event_monster: {
            type: DataTypes.BOOLEAN,
            defaultValue: false
        },
        event_start: {
            type: DataTypes.DATE,
            allowNull: true
        },
        event_end: {
            type: DataTypes.DATE,
            allowNull: true
        }
    }, {
        tableName: 'monsters',
        indexes: [
            {
                fields: ['type']
            },
            {
                fields: ['level']
            },
            {
                fields: ['location']
            },
            {
                fields: ['is_active']
            }
        ]
    });

    // Static methods
    Monster.getTypeInfo = function(type) {
        const typeData = {
            normal: {
                name: 'Normal',
                emoji: '👹',
                color: '#808080',
                multiplier: 1.0
            },
            elite: {
                name: 'Elite',
                emoji: '😈',
                color: '#9932CC',
                multiplier: 2.0
            },
            boss: {
                name: 'Boss',
                emoji: '👺',
                color: '#FF4500',
                multiplier: 5.0
            },
            raid_boss: {
                name: 'Raid Boss',
                emoji: '💀',
                color: '#8B0000',
                multiplier: 10.0
            }
        };

        return typeData[type] || typeData.normal;
    };

    Monster.findByLevel = function(level, type = 'normal') {
        return this.findAll({
            where: {
                level: {
                    [sequelize.Sequelize.Op.between]: [Math.max(1, level - 5), level + 5]
                },
                type: type,
                is_active: true
            },
            order: sequelize.literal('RANDOM()'),
            limit: 10
        });
    };

    Monster.findAvailableForUser = function(character) {
        const whereClause = {
            min_level_requirement: {
                [sequelize.Sequelize.Op.lte]: character.level
            },
            vip_requirement: {
                [sequelize.Sequelize.Op.lte]: character.vip_level
            },
            is_active: true
        };

        // Check event monsters
        const now = new Date();
        whereClause[sequelize.Sequelize.Op.or] = [
            { is_event_monster: false },
            {
                is_event_monster: true,
                event_start: { [sequelize.Sequelize.Op.lte]: now },
                event_end: { [sequelize.Sequelize.Op.gte]: now }
            }
        ];

        return this.findAll({
            where: whereClause,
            order: [['level', 'ASC']]
        });
    };

    // Instance methods
    Monster.prototype.getTypeInfo = function() {
        return Monster.getTypeInfo(this.type);
    };

    Monster.prototype.getFormattedName = function() {
        const typeInfo = this.getTypeInfo();
        return `${this.emoji || typeInfo.emoji} ${this.name} (Lv.${this.level})`;
    };

    Monster.prototype.calculateRewards = function(playerLevel, vipLevel = 0) {
        const typeInfo = this.getTypeInfo();
        const levelDifference = Math.max(0.1, 1 - Math.abs(this.level - playerLevel) * 0.05);
        
        // Base rewards with level scaling
        let exp = Math.floor(this.base_exp * typeInfo.multiplier * levelDifference);
        let gold = Math.floor(this.base_gold * typeInfo.multiplier * levelDifference);
        let gems = this.gem_reward;

        // VIP bonuses
        if (vipLevel > 0) {
            const vipBonus = 1 + (vipLevel * 0.1); // 10% per VIP level
            exp = Math.floor(exp * vipBonus);
            gold = Math.floor(gold * vipBonus);
        }

        return { exp, gold, gems };
    };

    Monster.prototype.rollDrops = function(playerLuck = 1.0) {
        const drops = [];
        
        // Regular drops
        if (this.drop_table && this.drop_table.length > 0) {
            this.drop_table.forEach(drop => {
                const chance = drop.chance * playerLuck;
                if (Math.random() < chance) {
                    const quantity = Math.floor(Math.random() * (drop.max_quantity - drop.min_quantity + 1)) + drop.min_quantity;
                    drops.push({
                        item_id: drop.item_id,
                        quantity: quantity,
                        type: 'normal'
                    });
                }
            });
        }

        // Rare drops
        if (this.rare_drop_table && this.rare_drop_table.length > 0) {
            this.rare_drop_table.forEach(drop => {
                const chance = drop.chance * playerLuck;
                if (Math.random() < chance) {
                    const quantity = Math.floor(Math.random() * (drop.max_quantity - drop.min_quantity + 1)) + drop.min_quantity;
                    drops.push({
                        item_id: drop.item_id,
                        quantity: quantity,
                        type: 'rare'
                    });
                }
            });
        }

        return drops;
    };

    Monster.prototype.isAvailableForUser = function(character) {
        if (!this.is_active) return false;
        if (character.level < this.min_level_requirement) return false;
        if (character.vip_level < this.vip_requirement) return false;

        // Check event availability
        if (this.is_event_monster) {
            const now = new Date();
            if (this.event_start && now < this.event_start) return false;
            if (this.event_end && now > this.event_end) return false;
        }

        return true;
    };

    return Monster;
};

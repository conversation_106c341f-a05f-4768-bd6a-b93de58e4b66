#!/usr/bin/env node

const readline = require('readline');
const { sequelize } = require('../src/database/database');
const DatabaseInitializer = require('../src/database/init');

function askQuestion(question) {
    const rl = readline.createInterface({
        input: process.stdin,
        output: process.stdout
    });

    return new Promise((resolve) => {
        rl.question(question, (answer) => {
            rl.close();
            resolve(answer);
        });
    });
}

async function resetDatabase() {
    try {
        console.log('⚠️  AFK Game Bot Database Reset\n');
        console.log('🚨 WARNING: This will completely reset your database!');
        console.log('   - All user data will be lost');
        console.log('   - All game progress will be lost');
        console.log('   - All transactions will be lost');
        console.log('   - This action cannot be undone!\n');
        
        const confirmation1 = await askQuestion('Are you sure you want to reset the database? (yes/no): ');
        
        if (confirmation1.toLowerCase() !== 'yes') {
            console.log('❌ Database reset cancelled.');
            return;
        }
        
        const confirmation2 = await askQuestion('Type "RESET" to confirm: ');
        
        if (confirmation2 !== 'RESET') {
            console.log('❌ Database reset cancelled.');
            return;
        }
        
        console.log('\n🔄 Resetting database...');
        
        const dbInit = new DatabaseInitializer(sequelize);
        
        // Reset database
        await dbInit.resetDatabase();
        
        console.log('✅ Database reset completed.');
        
        // Ask if user wants to reinitialize
        const reinit = await askQuestion('\nDo you want to reinitialize the database with default data? (yes/no): ');
        
        if (reinit.toLowerCase() === 'yes') {
            console.log('\n🔄 Reinitializing database...');
            await dbInit.initialize();
            console.log('✅ Database reinitialized successfully!');
        }
        
    } catch (error) {
        console.error('❌ Database reset failed:', error);
        process.exit(1);
    } finally {
        await sequelize.close();
    }
}

// Run if called directly
if (require.main === module) {
    resetDatabase();
}

module.exports = resetDatabase;
